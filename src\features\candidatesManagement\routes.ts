// src/features/candidatesManagement/routes.ts
import express from "express";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import {
  addApplicantAdditionalInfoController,
  getCandidateDetailsController,
  getCandidatesController,
  getTopCandidatesController,
  promoteDemoteCandidateController,
  archiveActiveApplication,
  updateJobApplicationStatusController,
  getCandidateInterviewHistoryController,
  getApplicationFinalSummaryController,
  generateFinalSummaryController,
  getAllHiredCandidate,
} from "./controller";
import HandleErrors from "../../middleware/handleError";
import {
  authorizedForAddAdditionalCandidateInfo,
  authorizedForArchiveRestoreCandidates,
  authorizedForManageTopCandidates,
  authorizedForCandidateDetailsAccess,
  authorizedForHireCandidateAccess,
  authorizedForManageCandidateProfileAccess,
  authorizedForHireCandidate,
  // authorizedForViewCandidateProfileSummary,
} from "../../middleware/isAuthorized";
import {
  queryValidation,
  schemaValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  addApplicantAdditionalInfoValidation,
  getAllCandidatesValidation,
  getTopCandidatesValidation,
  promoteDemoteCandidateValidation,
  archiveActiveApplicationValidation,
  updateJobApplicationStatusValidation,
  jobApplicationIdValidation,
} from "./validation";

const candidatesRoute = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     CandidateApplication:
 *       type: object
 *       properties:
 *         candidateName:
 *           type: string
 *           description: Full name of the candidate
 *           example: "John Doe"
 *         applicationCreatedTs:
 *           type: string
 *           format: date-time
 *           description: Timestamp when application was created
 *           example: "2024-01-15T10:30:00Z"
 *         atsScore:
 *           type: number
 *           description: ATS (Applicant Tracking System) score
 *           example: 85
 *         applicationId:
 *           type: number
 *           description: Unique identifier for the application
 *           example: 123
 *         applicationRankStatus:
 *           type: string
 *           enum: ["Promoted", "Demoted", "No Changes"]
 *           description: Current ranking status of the application
 *           example: "Promoted"
 *         applicationUpdatedTs:
 *           type: string
 *           format: date-time
 *           description: Timestamp when application was last updated
 *           example: "2024-01-16T14:20:00Z"
 *         applicationSource:
 *           type: string
 *           description: Source platform where application originated
 *           example: "LinkedIn"
 *         candidateId:
 *           type: number
 *           description: Unique identifier for the candidate
 *           example: 456
 *         aiReason:
 *           type: string
 *           description: AI-generated reason for application status
 *           example: "Strong technical background with relevant experience"
 *         aiDecision:
 *           type: string
 *           enum: ["Approved", "Rejected"]
 *           description: AI decision on the application
 *           example: "Approved"
 *         applicationStatus:
 *           type: string
 *           enum: ["Approved", "Rejected", "On-Hold", "Hired", "Pending", "Final-Reject"]
 *           description: Current status of the application
 *           example: "Approved"
 *         hiringManagerReason:
 *           type: string
 *           description: Reason provided by hiring manager
 *           example: "Excellent communication skills demonstrated in screening"
 *         job_id:
 *           type: number
 *           description: ID of the job position
 *           example: 789
 *         isTopApplication:
 *           type: boolean
 *           description: Whether this is marked as a top application
 *           example: true
 *         isActive:
 *           type: boolean
 *           description: Whether the application is active or archived
 *           example: true
 *         hiring_manager_id:
 *           type: number
 *           description: ID of the assigned hiring manager
 *           example: 101
 *
 *     CandidateProfile:
 *       type: object
 *       properties:
 *         candidateName:
 *           type: string
 *           description: Full name of the candidate
 *           example: "Jane Smith"
 *         jobTitle:
 *           type: string
 *           description: Title of the job position applied for
 *           example: "Senior Software Engineer"
 *         status:
 *           type: string
 *           description: Current application status
 *           example: "Approved"
 *         resumeLink:
 *           type: string
 *           format: uri
 *           description: URL link to candidate's resume
 *           example: "https://example.com/resumes/jane-smith.pdf"
 *         hiringManagerId:
 *           type: number
 *           description: ID of the assigned hiring manager
 *           example: 101
 *         interviewerName:
 *           type: string
 *           description: Name of the assigned interviewer
 *           example: "Mike Johnson"
 *         interviewerImage:
 *           type: string
 *           format: uri
 *           description: Profile image URL of the interviewer
 *           example: "https://example.com/images/mike-johnson.jpg"
 *         department:
 *           type: string
 *           description: Department for the position
 *           example: "Engineering"
 *         imageUrl:
 *           type: string
 *           format: uri
 *           description: Profile image URL of the candidate
 *           example: "https://example.com/images/jane-smith.jpg"
 *         roundNumber:
 *           type: number
 *           description: Current interview round number
 *           example: 2
 *         jobApplicationId:
 *           type: number
 *           description: Unique identifier for the job application
 *           example: 123
 *
 *     InterviewHistory:
 *       type: object
 *       properties:
 *         roundNumber:
 *           type: number
 *           description: Interview round number
 *           example: 1
 *         interviewerId:
 *           type: number
 *           description: ID of the interviewer
 *           example: 101
 *         interviewerName:
 *           type: string
 *           description: Name of the interviewer
 *           example: "Sarah Wilson"
 *         hardSkillMarks:
 *           type: number
 *           description: Hard skills evaluation score
 *           example: 8.5
 *         skillScores:
 *           type: object
 *           description: JSON object containing skill-specific scores
 *           example: {"JavaScript": 9, "React": 8, "Node.js": 7}
 *         interviewSummary:
 *           type: object
 *           description: Summary of the interview
 *           properties:
 *             highlight:
 *               type: array
 *               items:
 *                 type: string
 *               description: Key highlights from the interview
 *               example: ["Strong problem-solving skills", "Good communication"]
 *         interviewerPerformanceAiAnalysis:
 *           type: string
 *           description: AI analysis of interviewer performance
 *           example: "Interviewer conducted thorough technical assessment"
 *
 *     FinalAssessment:
 *       type: object
 *       properties:
 *         formattedFinalSummary:
 *           type: object
 *           description: Formatted final assessment summary
 *           properties:
 *             jobApplicationId:
 *               type: number
 *               description: Job application ID
 *               example: 603
 *             overallSuccessProbability:
 *               type: number
 *               description: Overall success probability percentage (0-100)
 *               example: 80
 *             developmentRecommendations:
 *               type: object
 *               description: Recommendations for candidate development
 *               properties:
 *                 recommendations:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       title:
 *                         type: string
 *                         description: Title of the recommendation
 *                         example: "Proactive Customer Engagement"
 *                       description:
 *                         type: string
 *                         description: Detailed description of the recommendation
 *                         example: "Develop a stronger, more anticipatory customer-first mindset"
 *             skillSummary:
 *               type: object
 *               description: Summary of candidate skills
 *               properties:
 *                 finalSummary:
 *                   type: array
 *                   items:
 *                     type: string
 *                   description: List of summary points about candidate skills
 *                   example: ["Demonstrates strong foundational skills", "Key strengths include attention to detail"]
 *             behaviouralScores:
 *               type: object
 *               description: Scores for different behavioral traits
 *               example: {"Perseverance": 80, "Relatability": 68, "Communication": 70}
 *         candidateProfileSkillScoreData:
 *           type: object
 *           description: Detailed skill score data for the candidate
 *           properties:
 *             careerBasedSkillsScore:
 *               type: number
 *               description: Overall career-based skills score
 *               example: 9
 *             skillsScores:
 *               type: array
 *               description: Individual skill assessments
 *               items:
 *                 type: object
 *                 properties:
 *                   skill_name:
 *                     type: string
 *                     description: Name of the assessed skill
 *                     example: "Work Ethic"
 *                   skill_marks:
 *                     type: number
 *                     description: Score for this skill
 *                     example: 5
 *                   strengths:
 *                     type: object
 *                     properties:
 *                       strengths:
 *                         type: array
 *                         items:
 *                           type: string
 *                         description: List of candidate strengths in this skill
 *                         example: ["Demonstrates consistent reliability"]
 *                   potentials_gaps:
 *                     type: object
 *                     properties:
 *                       potentialGaps:
 *                         type: array
 *                         items:
 *                           type: string
 *                         description: List of potential gaps or areas for improvement
 *                         example: ["Needs improvement in service quality"]
 *                   probability_of_success_in_this_skill:
 *                     type: object
 *                     properties:
 *                       probabilityOfSuccessInSkill:
 *                         type: number
 *                         description: Probability of success in this skill (0-100)
 *                         example: 64
 *
 *     SkillSpecificAssessment:
 *       type: object
 *       properties:
 *         careerBasedSkillsScore:
 *           type: number
 *           description: Overall career-based skills score (0-10)
 *           example: 8.2
 *         skillsScores:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               skill_name:
 *                 type: string
 *                 description: Name of the skill
 *                 example: "JavaScript"
 *               skill_marks:
 *                 type: number
 *                 description: Skill score (0-10)
 *                 example: 8.5
 *               strengths:
 *                 type: object
 *                 properties:
 *                   strengths:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: List of strengths in this skill
 *                     example: ["Advanced ES6 knowledge", "Async programming expertise"]
 *               potentials_gaps:
 *                 type: object
 *                 properties:
 *                   potentialGaps:
 *                     type: array
 *                     items:
 *                       type: string
 *                     description: Areas for improvement
 *                     example: ["Testing frameworks", "Performance optimization"]
 *               probability_of_success_in_this_skill:
 *                 type: object
 *                 properties:
 *                   probabilityOfSuccessInSkill:
 *                     type: number
 *                     description: Success probability for this skill (0-100)
 *                     example: 85
 *
 *     ApiResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Indicates if the request was successful
 *           example: true
 *         message:
 *           type: string
 *           description: Response message
 *           example: "candidates_fetched"
 *         code:
 *           type: number
 *           description: HTTP status code
 *           example: 200
 *         data:
 *           description: Response data (varies by endpoint)
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           description: Always false for error responses
 *           example: false
 *         message:
 *           type: string
 *           description: Error message
 *           example: "something_went_wrong"
 *         error:
 *           type: string
 *           description: Detailed error information
 *           example: "Database connection failed"
 *         code:
 *           type: number
 *           description: HTTP status code
 *           example: 500
 *
 *   securitySchemes:
 *     BearerAuth:
 *       type: http
 *       scheme: bearer
 *       bearerFormat: JWT
 *       description: JWT token for authentication
 *
 * tags:
 *   - name: Candidates Management
 *     description: API endpoints for managing candidate applications and profiles
 */

/**
 * @swagger
 * /candidates/get-candidates:
 *   get:
 *     summary: Get all candidates with their job applications
 *     description: Retrieves a paginated list of candidates with their job application details, supporting filtering by job ID, search string, and active status
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 0
 *           default: 0
 *         description: Page offset for pagination (0-based)
 *         example: 0
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 10
 *         description: Maximum number of candidates to return per page
 *         example: 10
 *       - in: query
 *         name: searchStr
 *         schema:
 *           type: string
 *         description: Search string to filter candidates by name
 *         example: "John Doe"
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by status - true for active candidates, false for archived
 *         example: true
 *       - in: query
 *         name: jobId
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter candidates for specific position
 *         example: 123
 *     responses:
 *       200:
 *         description: Successfully retrieved candidates
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/CandidateApplication'
 *             example:
 *               success: true
 *               message: "candidates_fetched"
 *               code: 200
 *               data: [
 *                  {
 *                   "candidateId": 32,
 *                   "candidateName": "hello",
 *                   "candidateEmail": "<EMAIL>",
 *                   "candidateImageUrl": "",
 *                   "applicationId": 36,
 *                   "applicationStatus": "Pending",
 *                   "applicationSource": "Other",
 *                   "applicationCreatedTs": "2025-07-10T10:39:27.319Z",
 *                   "applicationUpdatedTs": "2025-07-17T06:56:42.000Z",
 *                   "isActive": 1,
 *                   "jobId": 53,
 *                   "hiringManagerId": 844,
 *                   "hiringManagerReason": null,
 *                   "applicationRankStatus": "Demoted",
 *                   "aiReason": "Candidate is a good fit for the job",
 *                   "aiDecision": "Approved",
 *                   "atsScore": "93",
 *                   "currentRound": "0"
                  },
 *               ]
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_all_candidates_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATES,
  auth,
  queryValidation(getAllCandidatesValidation),
  HandleErrors(getCandidatesController)
);

/**
 * @swagger
 * /candidates/archive-active-application/{applicationId}:
 *   put:
 *     summary: Archive or restore a candidate application
 *     description: Archives or restores a candidate application by updating its active status. Requires special authorization for archive/restore operations.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: applicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the application to archive/restore
 *         example: 123
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: boolean
 *                 description: Archive status - false to archive, true to restore
 *                 example: false
 *               reason:
 *                 type: string
 *                 description: Optional reason for archiving (required when archiving)
 *                 example: "Candidate withdrew application"
 *           examples:
 *             archive:
 *               summary: Archive application
 *               value:
 *                 status: false
 *                 reason: "Candidate withdrew application"
 *             restore:
 *               summary: Restore application
 *               value:
 *                 status: true
 *     responses:
 *       200:
 *         description: Application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "updated_application_status_success"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       403:
 *         description: Forbidden - Insufficient permissions for archive/restore operations
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "You don't have permission to access this feature."
 *               code: 403
 *       404:
 *         description: Job application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "job_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "update_application_status_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.ARCHIVE_ACTIVE_APPLICATION,
  auth,
  authorizedForArchiveRestoreCandidates,
  schemaValidation(archiveActiveApplicationValidation),
  HandleErrors(archiveActiveApplication)
);

/**
 * @swagger
 * /candidates/top-candidates:
 *   get:
 *     summary: Get top-ranked candidates
 *     description: Retrieves a list of top-performing candidates based on AI scoring, ATS evaluation, and ranking algorithms. Results are pre-filtered and sorted by performance metrics.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobId
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter top candidates for specific position
 *         example: 123
 *     responses:
 *       200:
 *         description: Successfully retrieved top candidates
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/CandidateApplication'
 *             example:
 *               success: true
 *               message: "top_candidates_retrieved"
 *               code: 200
 *               data: [
 *                 {
 *                   candidateName: "Alice Johnson",
 *                   applicationCreatedTs: "2024-01-15T10:30:00Z",
 *                   atsScore: 95,
 *                   applicationId: 456,
 *                   applicationRankStatus: "Promoted",
 *                   applicationUpdatedTs: "2024-01-16T14:20:00Z",
 *                   applicationSource: "LinkedIn",
 *                   candidateId: 789,
 *                   aiReason: "Exceptional technical skills and experience",
 *                   aiDecision: "Approved",
 *                   applicationStatus: "Approved",
 *                   hiringManagerReason: "Outstanding portfolio and references",
 *                   job_id: 123,
 *                   isTopApplication: true,
 *                   isActive: true,
 *                   hiring_manager_id: 101
 *                 }
 *               ]
 *       400:
 *         description: Invalid query parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_top_candidates_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_TOP_CANDIDATES,
  auth,
  authorizedForManageTopCandidates,
  queryValidation(getTopCandidatesValidation),
  HandleErrors(getTopCandidatesController)
);

/**
 * @swagger
 * /candidates/update-candidate-rank-status:
 *   put:
 *     summary: Promote or demote a candidate
 *     description: Updates the ranking status of a candidate application by promoting or demoting them. This affects the candidate's visibility and priority in the hiring process.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - candidateId
 *               - applicationId
 *               - action
 *             properties:
 *               candidateId:
 *                 type: integer
 *                 description: Unique identifier of the candidate
 *                 example: 456
 *               applicationId:
 *                 type: integer
 *                 description: Unique identifier of the application
 *                 example: 123
 *               action:
 *                 type: string
 *                 enum: ["Promoted", "Demoted"]
 *                 description: Action to perform on the candidate ranking
 *                 example: "Promoted"
 *           examples:
 *             promote:
 *               summary: Promote candidate
 *               value:
 *                 candidateId: 456
 *                 applicationId: 123
 *                 action: "Promoted"
 *             demote:
 *               summary: Demote candidate
 *               value:
 *                 candidateId: 456
 *                 applicationId: 123
 *                 action: "Demoted"
 *     responses:
 *       200:
 *         description: Candidate ranking updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "update_rank_status_success"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "update_rank_status_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.PROMOTE_DEMOTE_CANDIDATE,
  auth,
  authorizedForManageTopCandidates,
  schemaValidation(promoteDemoteCandidateValidation),
  HandleErrors(promoteDemoteCandidateController)
);

/**
 * @swagger
 * /candidates/get-candidate-details:
 *   get:
 *     summary: Get detailed candidate profile information
 *     description: Retrieves comprehensive candidate profile details including personal information, job application status, assigned interviewer details, resume links, and current round information.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 123
 *     responses:
 *       200:
 *         description: Successfully retrieved candidate details
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/CandidateProfile'
 *             example:
 *               success: true
 *               code: 200
 *               data:
 *                 candidateName: "ramful"
 *                 candidateEmail: "<EMAIL>"
 *                 jobApplicationId: 653
 *                 jobId: 176
 *                 jobTitle: "Technical Lead"
 *                 status: "Approved"
 *                 resumeLink: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/176/resumes/ramful-resume-1756452308766.pdf"
 *                 hiringManagerId: 844
 *                 imageUrl: "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-5.png"
 *                 department: "Tech"
 *                 interviewerName: "John1g Doe844"
 *                 interviewerImage: "https://dxxd0n8h8rh9s.cloudfront.net/profile-images/Innerview%20Logo-1757509863963.png"
 *                 roundNumber: 1
 *                 isFinalAssessmentGenerated: null
 *                 isFinalSummaryGenerated: false
 *                 isFinalAssessmentGenerating: null
 *       400:
 *         description: Invalid or missing candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or not accessible by organization
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found_for_org"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "fetch_candidate_details_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATE_DETAILS,
  auth,
  authorizedForCandidateDetailsAccess,
  queryValidation(jobApplicationIdValidation),
  HandleErrors(getCandidateDetailsController)
);

/**
 * @swagger
 * /candidates/add-applicant-additional-info:
 *   post:
 *     summary: Add additional information to candidate application
 *     description: Allows candidates or hiring managers to submit supplementary information, documents, or clarifications to an existing application. Commonly used for portfolio submissions, additional certifications, or responses to specific questions.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - applicationId
 *               - description
 *               - images
 *             properties:
 *               applicationId:
 *                 type: string
 *                 description: Unique identifier of the application to update
 *                 example: "123"
 *               description:
 *                 type: string
 *                 description: Description or text content of the additional information
 *                 example: "Portfolio showcasing recent projects and achievements"
 *               images:
 *                 type: string
 *                 description: Image URLs, file references, or document links
 *                 example: "https://example.com/portfolio.pdf,https://example.com/certificate.jpg"
 *           example:
 *             applicationId: "123"
 *             description: "Portfolio showcasing recent projects and achievements"
 *             images: "https://example.com/portfolio.pdf"
 *     responses:
 *       200:
 *         description: Additional information added successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "additional_info_saved"
 *               data:
 *                 description: "Portfolio showcasing recent projects and achievements"
 *                 jobApplicationId: "123"
 *                 images:
 *                   urls:
 *                     - "https://example.com/portfolio.pdf"
 *                 id: 63
 *                 createdTs: "2025-09-15T11:14:47.856Z"
 *                 updatedTs: "2025-09-15T11:14:47.856Z"
 *               code: 200
 *       400:
 *         description: Invalid request body or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "job_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "something_went_wrong"
 *               error: "Database connection failed"
 *               code: 500
 */

candidatesRoute.post(
  ROUTES.CANDIDATES.ADD_APPLICANT_ADDITIONAL_INFO,
  auth,
  authorizedForAddAdditionalCandidateInfo,
  schemaValidation(addApplicantAdditionalInfoValidation),
  HandleErrors(addApplicantAdditionalInfoController)
);

/**
 * @swagger
 * /candidates/update-job-application-status/{jobApplicationId}:
 *   put:
 *     summary: Update job application status (Hire/Reject)
 *     description: Updates the final status of a job application to either "Hired" or "Final-Reject". This action typically triggers workflow notifications and updates the candidate's status across the system.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the job application to update
 *         example: 123
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: ["Hired", "Final-Reject", "Approved", "Rejected", "On-Hold", "Pending"]
 *                 description: New status for the job application
 *                 example: "Hired"
 *           examples:
 *             hire:
 *               summary: Hire candidate
 *               value:
 *                 status: "Hired"
 *             reject:
 *               summary: Reject candidate
 *               value:
 *                 status: "Final-Reject"
 *             approve:
 *               summary: Approve candidate
 *               value:
 *                 status: "Approved"
 *     responses:
 *       200:
 *         description: Job application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ApiResponse'
 *             example:
 *               success: true
 *               message: "updated_application_status_success"
 *               code: 200
 *       400:
 *         description: Invalid request body or parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Job application not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_application_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "Failed to update job application status"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.put(
  ROUTES.CANDIDATES.UPDATE_JOB_APPLICATION_STATUS,
  auth,
  authorizedForHireCandidate,
  paramsValidation(jobApplicationIdValidation),
  schemaValidation(updateJobApplicationStatusValidation),
  HandleErrors(updateJobApplicationStatusController)
);

/**
 * @swagger
 * /candidates/get-candidate-interview-history/{jobApplicationId}:
 *   get:
 *     summary: Get candidate interview history
 *     description: Retrieves comprehensive interview history for a specific candidate including interviewer information, skill scores, hard skill marks, interview summaries, and AI-powered performance analysis across all rounds.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the job application
 *         example: 603
 *     responses:
 *       200:
 *         description: Successfully retrieved interview history
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/InterviewHistory'
 *             example:
 *               success: true
 *               message: "Interview history retrieved successfully"
 *               code: 200
 *               data: [
 *                 {
 *                   roundNumber: 1,
 *                   interviewerId: 101,
 *                   interviewerName: "Sarah Wilson",
 *                   hardSkillMarks: 8.5,
 *                   skillScores: {
 *                     "JavaScript": 9,
 *                     "React": 8,
 *                     "Node.js": 7
 *                   },
 *                   interviewSummary: {
 *                     highlight: [
 *                       "Strong problem-solving skills",
 *                       "Good communication",
 *                       "Excellent technical knowledge"
 *                     ]
 *                   },
 *                   interviewerPerformanceAiAnalysis: "Interviewer conducted thorough technical assessment with good coverage of required skills"
 *                 }
 *               ]
 *       400:
 *         description: Invalid or missing candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or no interview history available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "get_interview_history_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_CANDIDATE_INTERVIEW_HISTORY,
  auth,
  paramsValidation(jobApplicationIdValidation),
  HandleErrors(getCandidateInterviewHistoryController)
);

/**
 * @swagger
 * /candidates/application-final-summary/{candidateId}:
 *   get:
 *     summary: Get application final assessment summary
 *     description: Retrieves comprehensive final assessment analysis generated after all interview rounds are completed. Includes AI-powered insights, success probability calculations, skill summaries, and personalized development recommendations.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: path
 *         name: candidateId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Unique identifier of the candidate
 *         example: 456
 *     responses:
 *       200:
 *         description: Successfully retrieved final assessment summary
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       $ref: '#/components/schemas/FinalAssessment'
 *             example:
 *               success: true
 *               message: "application_final_summary_retrieved"
 *               code: 200
 *               data: {
 *                 formattedFinalSummary: {
 *                   jobApplicationId: 603,
 *                   developmentRecommendations: {
 *                     recommendations: [
 *                       {
 *                         title: "Proactive Customer Engagement",
 *                         description: "Develop a stronger, more anticipatory customer-first mindset to proactively address client needs and elevate customer satisfaction."
 *                       },
 *                       {
 *                         title: "Team Collaboration",
 *                         description: "Increase participation and rapport within team settings to foster collaboration and a shared sense of purpose."
 *                       }
 *                     ]
 *                   },
 *                   skillSummary: {
 *                     finalSummary: [
 *                       "Candidate demonstrates strong foundational skills with reliable focus and adaptability.",
 *                       "Key strengths include attention to detail and process adherence."
 *                     ]
 *                   },
 *                   overallSuccessProbability: 80,
 *                   behaviouralScores: {
 *                     Perseverance: 80,
 *                     Relatability: 68,
 *                     Communication: 70
 *                   }
 *                 },
 *                 candidateProfileSkillScoreData: {
 *                   careerBasedSkillsScore: 9,
 *                   skillsScores: [
 *                     {
 *                       skill_name: "Work Ethic",
 *                       skill_marks: 5,
 *                       strengths: { strengths: ["Demonstrates consistent reliability"] },
 *                       potentials_gaps: { potentialGaps: ["Needs improvement in service quality"] },
 *                       probability_of_success_in_this_skill: { probabilityOfSuccessInSkill: 64 }
 *                     }
 *                   ]
 *                 }
 *               }
 *       400:
 *         description: Invalid candidateId parameter
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "invalid_params"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       404:
 *         description: Candidate not found or final assessment not available
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidate_not_found"
 *               code: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "something_went_wrong"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.APPLICATION_FINAL_SUMMARY,
  auth,
  paramsValidation(jobApplicationIdValidation),
  HandleErrors(getApplicationFinalSummaryController)
);
// candidatesRoute.get(
//   ROUTES.CANDIDATES.APPLICATION_SKILL_SCORE_DATA,
//   auth,
//   paramsValidation(getSkillSpecificAssessmentValidation),
//   HandleErrors(getApplicationSKillScoreDataController)
// );

/**
 * @swagger
 * /candidates/generate-final-summary:
 *   get:
 *     summary: Generate final summary for candidate application
 *     description: Triggers the generation of a comprehensive final summary for a candidate based on their interview performance, skill assessments, and overall evaluation data. The summary includes AI-powered insights and recommendations for hiring decisions.
 *     tags: [Candidates Management]
 *     security:
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: string
 *         description: Unique identifier of the job application
 *         example: "123"
 *     responses:
 *       200:
 *         description: Final summary generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/ApiResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         candidateId:
 *                           type: string
 *                           description: ID of the candidate
 *                           example: "456"
 *                         jobApplicationId:
 *                           type: string
 *                           description: ID of the job application
 *                           example: "123"
 *                         orgId:
 *                           type: number
 *                           description: Organization ID
 *                           example: 1
 *                         generatedAt:
 *                           type: string
 *                           format: date-time
 *                           description: Timestamp when summary was generated
 *                           example: "2024-01-15T10:30:00Z"
 *             example:
 *               success: true
 *               message: "final_summary_generated_successfully"
 *               code: 200
 *               data:
 *                 candidateId: "456"
 *                 jobApplicationId: "123"
 *                 orgId: 1
 *                 generatedAt: "2024-01-15T10:30:00Z"
 *       400:
 *         description: Invalid or missing required parameters
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "candidateId and jobApplicationId are required"
 *               code: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "unauthorized"
 *               code: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *             example:
 *               success: false
 *               message: "generate_final_summary_failed"
 *               error: "Database connection failed"
 *               code: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GENERATE_FINAL_SUMMARY,
  auth,
  authorizedForManageCandidateProfileAccess,
  queryValidation(jobApplicationIdValidation),
  HandleErrors(generateFinalSummaryController)
);

/**
 * @swagger
 * /candidates/get-all-hired-candidates:
 *   get:
 *     summary: Get all hired candidates
 *     description: Retrieves a list of all candidates who have been hired by the organization
 *     tags: [Candidates Management]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved hired candidates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "get_hired_candidate_success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       candidateName:
 *                         type: string
 *                         example: "Jane Smith"
 *                       candidateImageUrl:
 *                         type: string
 *                         example: "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-17.png"
 *                       jobTitle:
 *                         type: string
 *                         example: "Senior Python Backend Developer"
 *                       overallSuccessProbability:
 *                         type: number
 *                         nullable: true
 *                         example: 90
 *                       hiredDate:
 *                         type: string
 *                         format: date-time
 *                         nullable: true
 *                         example: "2025-08-26T15:17:09.000Z"
 *                       jobApplicationId:
 *                         type: integer
 *                         example: 22
 *                       interviewers:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 898
 *                             name:
 *                               type: string
 *                               example: "John Doe898"
 *                             email:
 *                               type: string
 *                               example: "<EMAIL>"
 *                             image:
 *                               type: string
 *                               example: ""
 *                             roundNumber:
 *                               type: integer
 *                               example: 1
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       403:
 *         description: Forbidden - User doesn't have permission to access hired candidates
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "You don't have permission to access this feature."
 *                 code:
 *                   type: integer
 *                   example: 403
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "get_hired_candidates_failed"
 *                 error:
 *                   type: string
 *                   example: "Database connection failed"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
candidatesRoute.get(
  ROUTES.CANDIDATES.GET_ALL_HIRED_CANDIDATE,
  auth,
  authorizedForHireCandidateAccess,
  HandleErrors(getAllHiredCandidate)
);

export default candidatesRoute;
