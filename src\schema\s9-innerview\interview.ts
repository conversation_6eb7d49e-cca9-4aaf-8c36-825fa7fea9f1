/* eslint-disable no-unused-vars */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  <PERSON>in<PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
} from "typeorm";
import { JobsModel } from "./jobs";
import JobApplicationsModel from "./job_applications";

export enum RoundType {
  ONE_ON_ONE = "One-On-One",
  VIDEO_CALL = "Video Call",
}

export enum AIDecisionForNextRound {
  APPROVED = "Approved",
  REJECTED = "Rejected",
}

interface IAttachments {
  fileUrls: string[];
}

interface IRecordingUrl {
  recordingUrls: string[];
}

@Entity("interview")
class InterviewModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "title", nullable: false, length: 100 })
  title: string;

  @Column({
    name: "description",
    nullable: true,
    type: "varchar",
    length: 2000,
  })
  description: string;

  @Column({ name: "attachments", nullable: true, type: "json" })
  attachments: IAttachments;

  @Column({ name: "is_canceled", default: false })
  isCanceled: boolean;

  @Column({ name: "job_id", nullable: false })
  jobId: number;

  @ManyToOne(() => JobsModel)
  @JoinColumn({ name: "job_id", referencedColumnName: "id" })
  job: JobsModel;

  @Column({ name: "interviewer_id", nullable: false })
  interviewerId: number;

  @Column({ name: "scheduled_by", nullable: true })
  scheduledBy: number;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @ManyToOne(() => JobApplicationsModel)
  @JoinColumn({ name: "job_application_id", referencedColumnName: "id" })
  jobApplication: JobApplicationsModel;

  @Column({ name: "schedule_at", type: "timestamp" })
  scheduleAt: Date;

  @Column({ name: "start_time", type: "timestamp" })
  startTime: Date;

  @Column({ name: "end_time", type: "timestamp" })
  endTime: Date;

  @Column({ name: "recording_url", nullable: true, type: "json" })
  recordingUrl: IRecordingUrl;

  @Column({ name: "round_number", nullable: false, default: 1 })
  roundNumber: number;

  @Column({
    name: "round_type",
    nullable: false,
    default: RoundType.ONE_ON_ONE,
  })
  roundType: RoundType;

  @Column({ name: "hard_skill_marks", nullable: false })
  hardSkillMarks: number;

  @Column({ name: "transcript_text", type: "longtext" })
  transcriptText: string;

  // this column is used to store AI generated behavioral analysis of the applicant
  @Column({ name: "applicant_ai_behavioral_analysis", type: "text" })
  applicantAiBehavioralAnalysis: string;

  // this column is used to store interviewer's feedback on the applicant's behavioral notes
  @Column({ name: "applicant_behavioral_notes", type: "varchar", length: 1500 })
  applicantBehavioralNotes: string;

  @Column({
    name: "interviewer_performance_ai_analysis",
    type: "json",
    nullable: true,
  })
  interviewerPerformanceNotes: object;

  @Column({ name: "interview_summary", type: "json" })
  interviewSummary: object;

  @Column({ name: "channel_name", nullable: false })
  channelName: string;

  @Column({ name: "is_ended", default: false })
  isEnded: boolean;

  @Column({ name: "is_allowed_for_next_round", default: false })
  isAllowedForNextRound: boolean;

  @Column({ name: "is_feedback_filled", default: false })
  isFeedbackFilled: boolean;

  @Column({
    name: "ai_decision_for_next_round",
    type: "enum",
    enum: AIDecisionForNextRound,
    nullable: true,
  })
  aiDecisionForNextRound: AIDecisionForNextRound;

  @Column({ name: "interview_end_time", nullable: true, type: "timestamp" })
  interviewEndTime: Date;

  @Column({ name: "agora_additional_files_deleted", default: false })
  agoraAdditionalFilesDeleted: boolean;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}

export default InterviewModel;
