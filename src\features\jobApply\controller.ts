import { Request, Response } from "express";
import JobApply from "./service";

/**
 * Generate Job Portal Script - Returns a script for embedding the job portal
 * This endpoint generates a JavaScript snippet that can be embedded on third-party websites.
 * It uses the organization ID (from middleware) to generate a secure script.
 *
 * @param req - The HTTP request object (expects orgId from middleware)
 * @param res - The HTTP response object
 * @returns {Promise<Response>} - Returns a JSON response with the script content or error
 */
export const generateJobPortalScript = async (req: Request, res: Response) => {
  try {
    console.log("organizationId  in controller from middleware ", req.orgId);
    const scriptContent = await JobApply.generateJobPortalScript(req.orgId);
    res.status(200).json({
      ...scriptContent,
      code: 200,
    });
  } catch (error) {
    console.error("Error encrypting organization ID:", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * load the iframe - Returns JavaScript code that creates an iframe
 * This endpoint is called by the script tag embedded on third-party websites
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const loadIframe = async (req: Request, res: Response) => {
  console.log("enter>>>>>>>>---loadIframe->>>");

  try {
    return await JobApply.loadIframe(req, res);
  } catch (error) {
    console.error("Error in getOrganizationJobs controller:", error);
    res.setHeader("Content-Type", "application/javascript");
    return res
      .status(500)
      .send("console.error('An error occurred while loading the job portal');");
  }
};

/**
 * Get iframe content - Serves the actual iframe HTML content
 * This is the endpoint that the JavaScript loads in an iframe
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getIframeContent = async (req: Request, res: Response) => {
  try {
    // Use the service layer method to handle the request
    return await JobApply.IframeContent(req, res);
  } catch (error) {
    console.error("Error in getIframeContent controller:", error);
    res.setHeader("Content-Type", "text/html");
    return res.status(500).send(`<!DOCTYPE html>
<html><body><h1>Error loading content</h1><p>${error.message}</p></body></html>`);
  }
};

/**
 * Get job list according to the organization with pagination and filtering
 *
 * @param {Request} req - The HTTP request object containing query parameters
 * @param {Response} res - The HTTP response object
 * @returns {Promise<Response>} - Returns a JSON response with job list data
 */
export const getOrganizationJobList = async (req: Request, res: Response) => {
  try {
    const data = await JobApply.getOrganizationJobList(req.query);
    return res.status(200).json(data);
  } catch (error) {
    console.error("Error in getOrganizationJobList controller:", error);
    return res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Apply for a job - Submit a job application
 *
 * @param {Request} req - The HTTP request object containing job application data
 * @param {Response} res - The HTTP response object
 * @returns {Promise<Response>} - Returns a JSON response with application details
 */
export const applyJob = async (req: Request, res: Response) => {
  try {
    const data = await JobApply.applyJob(req.body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    console.log("Error applying for job:", error);

    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Send otp to job applied candidate.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const sendOtpToJobAppliedCandidate = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };
    const data = await JobApply.sendOtpToJobAppliedCandidate(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    console.log("Error sending OTP to job applied candidate:", error);

    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * verify job applied candidate OTP.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const verifyJobAppliedCandidateOtp = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await JobApply.verifyJobAppliedCandidateOtp(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    console.log("Error verifying job applied candidate OTP:", error);

    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
