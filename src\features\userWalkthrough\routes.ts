import express from "express";
import HandleErrors from "../../middleware/handleError";
import auth from "../../middleware/auth";
import validationSchema from "./validation";
import { schemaValidation } from "../../middleware/validateSchema";
import { getWalkthroughStatus, updateWalkthroughStatus } from "./controller";

const userWalkthroughRoutes = express.Router();

/**
 * @swagger
 * /user-walkthrough:
 *   get:
 *     summary: Get user walkthrough status
 *     description: Retrieves the current walkthrough status for the authenticated user
 *     tags: [UserWalkthrough]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Walkthrough status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "walkthrough_status_updated"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: string
 *                     example: "dashboard"
 *                   example: ["user_roles", "top_ten_candidates", "video_call_interview", "one_to_one_interview", "calendar", "candidate_profile", "drag_and_drop", "dashboard", "user_permissions"]
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
userWalkthroughRoutes.get("/", auth, HandleErrors(getWalkthroughStatus));

/**
 * @swagger
 * components:
 *   schemas:
 *     UpdateWalkthroughRequest:
 *       type: object
 *       required:
 *         - name
 *       properties:
 *         name:
 *           type: string
 *           enum: [dashboard, interview_feedback, user_roles, user_permissions, calendar, one_to_one_interview, video_call_interview, drag_and_drop, candidate_profile, top_ten_candidates]
 *           description: Name of the walkthrough feature
 *           example: dashboard
 *     WalkthroughStatusResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "success"
 *         data:
 *           type: array
 *           items:
 *             type: string
 *             example: "dashboard"
 *           example: ["user_roles", "top_ten_candidates", "video_call_interview", "one_to_one_interview", "calendar", "candidate_profile", "drag_and_drop", "dashboard", "user_permissions"]
 */

/**
 * @swagger
 * /user-walkthrough:
 *   post:
 *     summary: Update user walkthrough status
 *     description: Updates the walkthrough status for the authenticated user
 *     tags: [UserWalkthrough]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/UpdateWalkthroughRequest'
 *     responses:
 *       200:
 *         description: Walkthrough status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/WalkthroughStatusResponse'
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request body"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
userWalkthroughRoutes.post(
  "/",
  auth,
  schemaValidation(validationSchema.updateWalkthroughStatusValidation),
  HandleErrors(updateWalkthroughStatus)
);

export default userWalkthroughRoutes;
