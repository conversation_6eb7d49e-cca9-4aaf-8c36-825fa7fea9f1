import sgMail from "@sendgrid/mail";
import { getSecretKeys } from "../config/awsConfig";
import { IEmailData, ISendMail } from "../interface/commonInterface";

/**
 *  Send mail
 */
export const sendMail = async ({
  email,
  subject,
  textContent,
  htmlContent,
  attachments,
}: ISendMail) => {
  try {
    const keys = await getSecretKeys();

    sgMail.setApiKey(keys.sendgrid_key);
    const mailData: IEmailData = {
      from: keys.sendgrid_mail,
      to: email,
      subject,
      text: textContent,
      html: htmlContent,
      attachments,
    };

    const [res] = await sgMail.send(mailData);

    return {
      isError: false,
      messageId: res.headers["x-message-id"],
      message: "Sent",
    };
  } catch (error) {
    console.log("Sendgrid error===>>>", error.response.body.errors);
    return {
      isError: true,
      errorMessage: error.response.body.errors,
    };
  }
};

export default sendMail;
