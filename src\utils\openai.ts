import OpenAI from "openai";
import {
  IQuestion,
  QuestionFormat,
} from "../features/finalAssessment/interface";
import { getSecretKeys } from "../config/awsConfig";
import { handleSentryError } from "./helper";
import {
  GPT_MODEL,
  MAX_FINAL_ASSESSMENT_QUESTIONS_PER_SKILL,
} from "./constants";

/**
 * Makes OpenAI API calls with retry logic
 * @param model OpenAI model name
 * @param messages Array of message objects
 * @param options API parameters and retry settings
 * @returns Response content string
 */
export const makeOpenAIRequest = async (
  model: string,
  messages: Array<{ role: string; content: string }>,
  options: {
    maxRetries?: number;
    initialDelayMs?: number;
    responseFormat?: { type: string };
    temperature?: number;
    maxTokens?: number;
  } = {}
): Promise<string> => {
  const {
    maxRetries = 3,
    initialDelayMs = 1000,
    responseFormat,
    temperature,
    maxTokens,
  } = options;

  const keys = await getSecretKeys();
  const openAiClient = new OpenAI({ apiKey: keys.openai_api_key });

  let attempt = 0;
  let lastError: any = null;

  console.log("inside makeOpenAIRequest");

  while (attempt <= maxRetries) {
    try {
      if (attempt > 0) {
        console.log(
          `Retrying OpenAI API call (attempt ${attempt}/${maxRetries})`
        );
        const delayMs =
          initialDelayMs * 2 ** (attempt - 1) + Math.random() * 1000;
        console.log(`Waiting ${delayMs}ms...`);
        // eslint-disable-next-line no-await-in-loop
        await new Promise<void>((resolve) => {
          setTimeout(resolve, delayMs);
        });
      }

      const requestParams: any = {
        model,
        messages,
      };

      // Add optional params if provided
      if (responseFormat) requestParams.response_format = responseFormat;
      if (temperature !== undefined) requestParams.temperature = temperature;
      if (maxTokens !== undefined) requestParams.max_tokens = maxTokens;

      const response =
        // eslint-disable-next-line no-await-in-loop
        await openAiClient.chat.completions.create(requestParams);
      const responseContent = response.choices[0].message.content || "";
      return responseContent;
    } catch (error) {
      lastError = error;

      const isRateLimitError =
        error.status === 429 ||
        (error.message &&
          typeof error.message === "string" &&
          error.message.toLowerCase().includes("rate limit")) ||
        (error.error &&
          error.error.type &&
          typeof error.error.type === "string" &&
          error.error.type.includes("rate_limit"));

      console.error(
        `Error making OpenAI API call (attempt ${attempt}/${maxRetries}):`,
        error
      );

      if (isRateLimitError && attempt < maxRetries) {
        attempt += 1;
      } else {
        break;
      }
    }
  }

  if (lastError) {
    console.error(
      "Failed to make OpenAI API call after all retries:",
      lastError
    );
    handleSentryError(lastError, "makeOpenAIRequest");
  }

  return "";
};

/**
 * Generate interview questions for multiple skills in a single API call
 * @param skills - Array of skill objects with title, description, and type
 * @param numQuestionsPerSkill - Number of questions to generate per skill (default: 2)
 * @returns Object mapping skillId to array of generated questions
 */
/**
 * Generate interview questions for multiple skills in a single API call
 * @param skills - Array of skill objects with title, description, and type
 * @param numQuestionsPerSkill - Number of questions to generate per skill (default: 2)
 * @param interviewSummary - Summary of previous interview rounds (optional)
 * @param jobDescription - Job description to provide context (optional)
 * @returns Object mapping skillId to array of generated questions
 */
export const generateQuestionsForMultipleSkills = async (
  skills: Array<{
    skillId: number;
    title: string;
    shortDescription: string;
    type: string;
  }>,
  interviewSummary?: string,
  jobDescription?: string,
  numQuestionsPerSkill: number = MAX_FINAL_ASSESSMENT_QUESTIONS_PER_SKILL
): Promise<Record<number, IQuestion[]>> => {
  try {
    // Always use batch processing for better performance
    const BATCH_SIZE = 3; // smaller batches reduce per-call time
    const CONCURRENCY = 4; // higher concurrency to lower wall time

    // Build batch prompting function
    const buildPrompt = (subset: typeof skills) => {
      const skillsPrompt = subset
        .map(
          (skill, index) =>
            `${index + 1}. Skill: "${skill.title}" (${skill.type})\n   Description: "${skill.shortDescription}"`
        )
        .join("\n");

      let contextSections = "";
      if (jobDescription) {
        contextSections += `\n\nJOB DESCRIPTION CONTEXT:\n${jobDescription}`;
      }
      if (interviewSummary) {
        contextSections += `\n\nPREVIOUS INTERVIEW SUMMARY:\n${interviewSummary}`;
      }

      return `You are an expert technical interviewer with 10+ years of experience in candidate assessment. Generate high-quality, targeted interview questions based on the job description, experience level, and candidate history.

      ### MANDATORY SKILL COVERAGE REQUIREMENT
      - Generate EXACTLY ${numQuestionsPerSkill} questions for EVERY skill in the provided list
      - DO NOT skip any skill IDs, regardless of perceived relevance to the job description
      - If insufficient information is available for a specific skill, create reasonable questions based SOLELY on the skill name
      - Total questions MUST equal (${subset.length} * ${numQuestionsPerSkill})
      - CRITICAL: Ensure ALL JSON STRINGS ARE PROPERLY TERMINATED with closing quotes
      
      ### JSON VALIDATION RULES
      - BEFORE returning your response, VALIDATE that ALL strings are properly closed with quotes
      - DOUBLE-CHECK that there are NO unterminated strings in your JSON response
      - If your response is approaching the token limit, COMPLETE the current question before ending
      - NEVER cut off a string mid-sentence - if you run out of tokens, finish the current string with a period and proper closing quotes
      
      ### EXPERIENCE LEVEL ANALYSIS
      Analyze the job description to determine the appropriate difficulty level:
      - **Beginner**: 0-2 years of experience required OR no experience requirement specified
      - **Intermediate**: 2-5 years of experience required
      - **Expert**: 5+ years of experience required OR "senior" mentioned in title
      
      ${jobDescription}
      
      ### CANDIDATE HISTORY ANALYSIS
      Review previous interview questions to avoid repetition and tailor difficulty:
      ${interviewSummary}
      
      ### QUESTION GENERATION PROTOCOL
      
      1. **DIFFICULTY ADJUSTMENT**:
         - For Beginner: Focus on foundational concepts in realistic workplace scenarios
         - For Intermediate: Include complex scenario-based questions requiring problem-solving
         - For Expert: Include advanced scenarios with optimization challenges and architectural decisions
      
      2. **QUESTION TYPE BALANCE**:
         - Generate EXACTLY ${numQuestionsPerSkill} questions per skill with this distribution:
           * 100% scenario-based questions (real-world workplace situations requiring application of knowledge)
         - Mix question types (MCQ and true/false) with true randomness (no patterns)
         - For all questions: Create realistic workplace situations that test practical application of knowledge
         - If insufficient information is available for a skill, create questions based SOLELY on the skill name
      
      3. **QUALITY STANDARDS**:
         - **NO DIRECT KNOWLEDGE QUESTIONS**: All questions must be scenario-based
         - **CHARACTER LIMIT**: All questions must be MAXIMUM 235 characters with spaces (strict limit)
         - **PLAUSIBLE DISTRRACTORS**: For MCQs, ensure all options are technically plausible:
           * One clearly correct answer
           * One option reflecting common misconception
           * One option that's partially correct but incomplete
           * One option that's completely wrong but plausible to beginners
         - **REAL-WORLD CONTEXT**: All questions must reflect actual workplace scenarios
         - **NO SKILL OMISSION**: Generate questions for EVERY skill in the provided list
         - **NO REPETITION**: Check against interview history to avoid previously asked questions
         - **SKILL RELEVANCE**: Questions must directly address the skill being assessed through realistic scenarios
      
      ${skillsPrompt}${contextSections}
      
      ### OUTPUT FORMAT INSTRUCTIONS
      - Format the response as a JSON object with a 'skillQuestions' object
      - Each key is the skill number (1, 2, 3, etc.) and the value is an array of EXACTLY ${numQuestionsPerSkill} questions
      - Maintain the EXACT structure shown below with no additional fields, no missing fields, and no modifications to field names
      - If a skill has fewer than ${numQuestionsPerSkill} questions, the entire output is INVALID
      - CRITICAL: ENSURE ALL JSON STRINGS ARE PROPERLY TERMINATED WITH CLOSING QUOTES
      - RETURN RAW JSON ONLY. DO NOT wrap the JSON in markdown code fences such as \`\`\` or specify a language. Do not include any explanatory text before or after the JSON.
      
      ### QUESTION STRUCTURE (MUST MATCH EXACTLY)
      
      For MCQ questions:
      - 'type': "mcq"
      - 'question': the question text (must include realistic scenario context, MAX 235 characters with spaces)
      - 'options': array of option objects with 'id' (A, B, C, or D) and 'text' (the option text)
      - 'correctAnswerId': the ID of the correct option (A, B, C, or D)
      
      For true/false questions:
      - 'type': "true_false"
      - 'question': the question text (must include realistic scenario context, MAX 235 characters with spaces)
      - 'options': array of option objects with 'id' (true or false) and 'text' (the option text)
      - 'correctAnswer': boolean (true or false)
      
      ### CRITICAL RULES
      - NEVER skip any skill IDs (generate questions for ALL skills)
      - NEVER generate fewer than ${numQuestionsPerSkill} questions for ANY skill
      - NEVER include direct knowledge questions (all questions must be scenario-based)
      - NEVER exceed 235 characters for any question (including spaces)
      - NEVER follow a pattern in question type distribution (truly random mix)
      - NEVER generate questions that were previously asked (check interview history)
      - NEVER include obvious questions where the answer is too easily guessed
      - NEVER use generic scenarios; all questions must reflect real workplace situations
      - NEVER add, remove, or modify ANY fields in the output format
      - ALWAYS generate EXACTLY ${numQuestionsPerSkill} questions per skill
      - ALWAYS ensure ALL JSON STRINGS ARE PROPERLY TERMINATED WITH CLOSING QUOTES
      - BEFORE FINALIZING YOUR RESPONSE, VALIDATE THE JSON STRUCTURE FOR PROPER STRING TERMINATION
      - If approaching token limit, COMPLETE the current question before ending response
      - NEVER cut off a string mid-sentence - finish with a period and proper closing quotes
      
      ### EXAMPLE FORMAT (COPY EXACTLY)
      {
        "skillQuestions": {
          "1": [
            {
              "type": "mcq",
              "question": "What is the best approach to solve X?",
              "options": [
                { "id": "A", "text": "Option A text" },
                { "id": "B", "text": "Option B text" },
                { "id": "C", "text": "Option C text" },
                { "id": "D", "text": "Option D text" }
              ],
              "correctAnswerId": "B"
            },
            {
              "type": "mcq",
              "question": "Another scenario-based question for skill 1",
              "options": [
                { "id": "A", "text": "Option A text" },
                { "id": "B", "text": "Option B text" },
                { "id": "C", "text": "Option C text" },
                { "id": "D", "text": "Option D text" }
              ],
              "correctAnswerId": "C"
            }
          ],
          "2": [
            {
              "type": "true_false",
              "question": "Statement about the skill that is true or false",
              "options": [
                { "id": "true", "text": "True" },
                { "id": "false", "text": "False" }
              ],
              "correctAnswer": true
            },
            {
              "type": "mcq",
              "question": "Scenario-based question for skill 2",
              "options": [
                { "id": "A", "text": "Option A text" },
                { "id": "B", "text": "Option B text" },
                { "id": "C", "text": "Option C text" },
                { "id": "D", "text": "Option D text" }
              ],
              "correctAnswerId": "A"
            }
          ]
        }
      }`;
    };

    // Function to process a batch with retry logic
    const callSubset = async (
      subset: typeof skills
    ): Promise<Record<number, IQuestion[]>> => {
      const prompt = buildPrompt(subset);
      const content = await makeOpenAIRequest(
        GPT_MODEL,
        [
          {
            role: "system",
            content:
              "You are an expert interviewer who creates targeted questions to assess specific skills.",
          },
          { role: "user", content: prompt },
        ],
        { temperature: 0.6, responseFormat: { type: "json_object" } }
      );

      let sanitized = (content || "").trim();
      if (sanitized.startsWith("```")) {
        sanitized = sanitized
          .replace(/^```[a-zA-Z0-9_-]*\s*/i, "")
          .replace(/```\s*$/, "");
      }
      if (!(sanitized.startsWith("{") && sanitized.endsWith("}"))) {
        const first = sanitized.indexOf("{");
        const last = sanitized.lastIndexOf("}");
        if (first !== -1 && last !== -1 && last > first)
          sanitized = sanitized.substring(first, last + 1);
      }

      const parsed = JSON.parse(sanitized || "{}");
      const skillQuestions = parsed.skillQuestions || {};
      const out: Record<number, IQuestion[]> = {};
      subset.forEach((skill, idx) => {
        const arr = skillQuestions[(idx + 1).toString()] || [];
        const valid = arr.filter((q: any) => {
          if (!q?.question || !q?.type) return false;
          if (q.type === QuestionFormat.MCQ)
            return (
              Array.isArray(q.options) &&
              q.options.length === 4 &&
              q.correctAnswerId
            );
          if (q.type === QuestionFormat.TRUE_FALSE)
            return typeof q.correctAnswer === "boolean";
          return false;
        });
        out[skill.skillId] = valid;
      });

      // Validate per-skill count
      const ok = subset.every(
        (s) => (out[s.skillId] || []).length === numQuestionsPerSkill
      );
      if (!ok) throw new Error("Batch validation failed");
      return out;
    };

    // Build batches
    const subsets: Array<typeof skills> = [];
    for (let i = 0; i < skills.length; i += BATCH_SIZE)
      subsets.push(skills.slice(i, i + BATCH_SIZE));

    // Store results
    const results: Record<number, IQuestion[]> = {};

    // Process a single batch with retry
    const processBatch = async (batchIndex: number): Promise<void> => {
      const subset = subsets[batchIndex];
      try {
        const out = await callSubset(subset);
        Object.assign(results, out);
      } catch (e1) {
        try {
          // Retry once on failure
          const out2 = await callSubset(subset);
          Object.assign(results, out2);
        } catch (e2) {
          handleSentryError(e2, "generateQuestionsForMultipleSkills:subset");
        }
      }
    };

    // Create workers to process batches in parallel
    const createWorkers = (): Promise<void>[] => {
      const batchPromises: Promise<void>[] = [];
      const workerCount = Math.min(CONCURRENCY, subsets.length);

      // Distribute batches in a round-robin fashion
      for (let workerIndex = 0; workerIndex < workerCount; workerIndex += 1) {
        // Start a worker that will process every Nth batch
        batchPromises.push(
          (async () => {
            for (
              let batchIndex = workerIndex;
              batchIndex < subsets.length;
              batchIndex += workerCount
            ) {
              // eslint-disable-next-line no-await-in-loop
              await processBatch(batchIndex);
            }
          })()
        );
      }

      return batchPromises;
    };

    // Start all workers and wait for them to complete
    await Promise.all(createWorkers());

    return results;
  } catch (error) {
    handleSentryError(error, "generateQuestionsForMultipleSkills");
    return {};
  }
};

export default {
  makeOpenAIRequest,
  generateQuestionsForMultipleSkills,
};
