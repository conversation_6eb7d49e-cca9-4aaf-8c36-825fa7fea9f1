#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo 'Running prettier fix on all files'
npm run prettier-fix ||
(
    echo '🤢🤮🤢🤮. Prettier fix Failed. You will have to fix prettier issues manually';
    true;
)

echo 'Running eslint fix on all files'
npm run lint-fix ||
(
    echo '🤢🤮🤢🤮. Linting fix Failed. You will have to fix linting issues manually';
    true;
)

# Check Prettier standards
npm run check-prettier ||
(
    echo '🤢🤮🤢🤮. Prettier Check Failed. Run npm run format, add changes, and try the commit again.';
    exit 1;
)

# Check ESLint Standards
npm run check-lint ||
(
    echo '😤🏀👋😤 ESLint Check Failed. Make the required changes listed above, add changes, and try the commit again.'
    exit 1;
)

# Check tsconfig standards
npm run check-types ||
(
    echo '🤡😂❌🤡 TypeScript Check Failed. Make the changes required above.'
    exit 1;
)

# If everything passes... Now we can commit
echo '✅✅✅✅ You win this time... I am committing this now. ✅✅✅✅'
exit 0;
