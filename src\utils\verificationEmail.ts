import { handleSentryError } from "./helper";
import sendMail from "./sendgrid";
import { OTP_TYPE, STRATUM9_LOGO_URL } from "./constants";

const sendVerificationMail = ({ type, email, firstname }) => {
  try {
    const otp = Math.floor(1000 + Math.random() * 9000);
    const subject =
      type === OTP_TYPE.SIGNUP
        ? "Verify your email"
        : "Password reset verification code";

    const textContent =
      type === OTP_TYPE.SIGNUP
        ? "Verify your email"
        : "Password reset verification code";
    const message =
      type === OTP_TYPE.SIGNUP
        ? "Welcome to Stratum9"
        : "Password Reset Request";

    const htmlContent = `
 <!DOCTYPE html>
 <html lang="en">
    <head>
       <meta charset="UTF-8" />
       <meta name="viewport" content="width=device-width, initial-scale=1.0" />
       <title>Verification</title>
       <style>
          body {
          margin: 0;
          background-color: #cccccc;
          }
          table {
          border-spacing: 0;
          }
          td {
          padding: 0;
          }
          img {
          border: 0;
          }
          .wrapper {
          width: 100%;
          table-layout: fixed;
          background-color: #436EB6;
          padding-bottom: 60px;
          }
          .main {
          background-color: #fff;
          margin: 0 auto;
          width: 100%;
          max-width: 600px;
          border-spacing: 0;
          font-family: sans-serif;
          color: #171a1b;
          padding: 20px;
          font-size: 14px;
          }
          td {
          padding: 5px 0;
          }
          ul{
             list-style: none;
          }
       </style>
    </head>
    <body style="margin: 0">
       <center class="wrapper">
          <img
             src="${STRATUM9_LOGO_URL}"
             alt="logo"
             style="
             margin: 0 auto;
             width: 150px;
             margin-bottom: 20px;
             margin-top: 20px;
             background:#fff;
             padding: 5px ;
             border-color:#436eb6;
             "
             />
          <table class="main" width="100%" style="border-right: solid 10px #436eb6;border-left: solid 10px #436eb6;">
             <tr>
                <td align="center">
                  <h2 style="margin: 0; font-size: 24px; color: #436EB6;">${message}</h2>
 
                </td>
             </tr>
             <tr>
                <td align="center">
                   <h3>Hey ${firstname},</h3>
                   <p>Your Verification Code is: <span style="color: #436EB6; font-weight: bold;">${otp}</span></p>
 
                  <p style="color: red;">${
                    type === OTP_TYPE.SIGNUP
                      ? "This code is used to verify your email and is valid for 5 minutes"
                      : "This code is used to reset your password  and is valid for 5 minutes"
                  }</p>
 
 
                </td>
             </tr>
             <tr>
                <td></td>
             </tr>
             <tr>
                <td algin="center">
                <div style="text-align: center;">
 
                <img
                   src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/best-seller+(1).png"
                   alt="logo"
                   style="
                   width: 200px;
                   height: 200px;
                   margin-bottom: 0px;
                   margin-top: 0px;
                   background:#fff;
                   "
                   />
                </div>
                </td>
             </tr>
             <tr>
                <td>
                   <ul style="list-style: none; padding: 0; margin: 0;">
                      <li><a href=""></a></li>
                      <li><a href=""></a></li>
                   </ul>
                </td>
             </tr>
             <tr>
                <td align="center">
                   <ul
                      style="
                      list-style: none;
                      padding: 0;
                      margin: 0;
                      align-items: center;"   
                      >
                      <li style="padding-right: 0px">
                         <a href="https://www.stratum-nine.com/terms-and-conditions" style="color: #000"> Terms and conditions </a>
                      </li>
                      <li style="padding-right: 0px">
                         <a href="https://www.stratum-nine.com/privacy-policy" style="color: #000"> Privacy Policy </a>
                      </li>
                   </ul>
                </td>
             </tr>
          </table>
       </center>
    </body>
 </html>`;

    const response = sendMail({
      email: email.toLowerCase(),
      subject,
      textContent,
      htmlContent,
    });

    return { response, otp };
  } catch (error) {
    // Handle the error here, you can log it or perform any other necessary action
    console.error("Error sending email:", error);
    handleSentryError(error, "sendVerificationMail");
    return { error: "Email sending failed" }; // You can customize the error message as needed
  }
};

export default sendVerificationMail;
