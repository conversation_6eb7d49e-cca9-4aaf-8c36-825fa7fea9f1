import express from "express";
import auth from "../../middleware/auth";

import {
  applyJobValidationSchema,
  organizationJobListValidationSchema,
  otpValidationSchema,
  verifyOtpValidationSchema,
} from "./validation";
import {
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import { ROUTES } from "../../utils/constants";
import {
  applyJob,
  generateJobPortalScript,
  getIframeContent,
  getOrganizationJobList,
  loadIframe,
  sendOtpToJobAppliedCandidate,
  verifyJobAppliedCandidateOtp,
} from "./controller";
import HandleErrors from "../../middleware/handleError";

const jobApplyRoutes = express.Router();

/**
 * @swagger
 * /job-apply/get-apply-job-portal-script:
 *   get:
 *     summary: Generate job portal script
 *     description: Generates a script for embedding the job portal for a specific organization
 *     tags: [Job Apply Module]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Script generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Script generated successfully"
 *                 data:
 *                   type: string
 *                   description: The generated script
 *                   example: "<script>...</script>"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */

// Route to generate a job portal script for embedding in third-party websites
jobApplyRoutes.get(
  ROUTES.JOB_APPLY.GET_APPLY_JOB_PORTAL_SCRIPT,
  auth,
  HandleErrors(generateJobPortalScript)
);

// Route to load the iframe for the job portal
jobApplyRoutes.get(ROUTES.JOB_APPLY.LOAD_IFRAME, HandleErrors(loadIframe));

// Route to serve the actual iframe content for embedding job portal
jobApplyRoutes.get(
  ROUTES.JOB_APPLY.GET_IFRAME_CONTENT,
  HandleErrors(getIframeContent)
);

/**
 * @swagger
 * /job-apply/get-organization-jobs-list:
 *   get:
 *     summary: Get job list for an organization with pagination and filtering
 *     description: Returns a paginated and filtered list of jobs for a specific organization
 *     tags: [Job Apply Module]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: organizationId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the organization
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *         required: true
 *         description: Pagination offset (default 0)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         required: true
 *         description: Number of records to return (default 10)
 *       - in: query
 *         name: searchTerm
 *         schema:
 *           type: string
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *         description: Sort  ('asc' or 'desc', default 'desc')
 *     responses:
 *       200:
 *         description: Successfully retrieved job list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: success
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobs:
 *                       type: array
 *                       items:
 *                         type: object
 *                     totalCount:
 *                       type: integer
 *                       example: 25
 *                 code:
 *                   type: number
 *                   example: 200
 *             examples:
 *               success:
 *                 summary: Example response
 *                 value:
 *                   success: true
 *                   message: success
 *                   data:
 *                     jobs:
 *                       - id: 00
 *                         isActive: true
 *                         location: "onsite"
 *                         orgId: 00
 *                         organizationName: "Org for Testing 2"
 *                         title: "Velit et omnis dolor"
 *                         aboutCompany: ""
 *                         finalJobDescription: null
 *                         perksBenefits: null
 *                         responsibilities: ""
 *                         roleOverview: ""
 *                       # ...more jobs...
 *                     totalCount: 25
 *                   code: 200
 *       400:
 *         description: Invalid organization ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid organization ID
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 code:
 *                   type: number
 *                   example: 500
 */

// Route to get a paginated and filtered list of jobs for a specific organization
jobApplyRoutes.get(
  ROUTES.JOB_APPLY.GET_ORGANIZATION_JOBS_LIST,
  queryValidation(organizationJobListValidationSchema),
  HandleErrors(getOrganizationJobList)
);

/**
 * @swagger
 * /job-apply/otp:
 *   post:
 *     summary: Send OTP to job applied candidate
 *     description: Sends an OTP to the candidate who applied for a job
 *     tags: [Job Apply Module]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - jobId
 *               - orgId
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the candidate
 *                 example: "<EMAIL>"
 *               jobId:
 *                 type: integer
 *                 description: ID of the job
 *                 example: 123
 *               orgId:
 *                 type: string
 *                 description: ID of the organization
 *                 example: "org_456"
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "OTP sent successfully"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid candidate ID or job ID"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */

// Route to send an OTP to a candidate who applied for a job
jobApplyRoutes.post(
  ROUTES.JOB_APPLY.SEND_OTP_TO_JOB_APPLIED_CANDIDATE,
  schemaValidation(otpValidationSchema),
  HandleErrors(sendOtpToJobAppliedCandidate)
);

/**
 * @swagger
 * /job-apply/verify-otp:
 *   post:
 *     summary: Verify OTP for job application
 *     description: Verifies the OTP sent to the candidate for a job application
 *     tags: [Job Apply Module]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - jobId
 *               - orgId
 *               - otp
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the candidate
 *                 example: "<EMAIL>"
 *               jobId:
 *                 type: integer
 *                 description: ID of the job
 *                 example: 123
 *               orgId:
 *                 type: string
 *                 description: ID of the organization
 *                 example: "org_456"
 *               otp:
 *                 type: string
 *                 description: 4-digit OTP sent to the candidate
 *                 example: "1234"
 *                 minLength: 4
 *                 maxLength: 4
 *     responses:
 *       200:
 *         description: OTP verified successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "OTP verified successfully"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid OTP or request data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid OTP"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */

// Route to verify the OTP sent to a candidate for a job application
jobApplyRoutes.post(
  ROUTES.JOB_APPLY.VERIFY_JOB_APPLIED_CANDIDATE_OTP,
  schemaValidation(verifyOtpValidationSchema),
  HandleErrors(verifyJobAppliedCandidateOtp)
);

/**
 * @swagger
 * /job-apply/apply-job:
 *   post:
 *     summary: Apply for a job
 *     description: Submit a job application for a specific job position
 *     tags: [Job Apply Module]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - candidateName
 *               - email
 *               - gender
 *               - resume
 *               - jobId
 *               - otp
 *             properties:
 *               candidateName:
 *                 type: string
 *                 description: Name of the candidate
 *                 example: "John Doe"
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the candidate
 *                 example: "<EMAIL>"
 *               gender:
 *                 type: string
 *                 enum: [Male, Female, Other]
 *                 description: Gender of the candidate
 *                 example: "Male"
 *               resume:
 *                 type: string
 *                 description: Resume file path/name
 *                 example: "resume_john_doe.pdf"
 *               jobId:
 *                 type: integer
 *                 description: ID of the job to apply for
 *                 example: 123
 *               otp:
 *                 type: integer
 *                 description: OTP sent to the candidate
 *                 example: 1234
 *     responses:
 *       201:
 *         description: Job application submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Job application submitted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     applicationId:
 *                       type: integer
 *                       description: ID of the created application
 *                       example: 789
 *                     jobId:
 *                       type: integer
 *                       description: ID of the job applied for
 *                       example: 123
 *                     candidateName:
 *                       type: string
 *                       description: Name of the candidate
 *                       example: "John Doe"
 *                     email:
 *                       type: string
 *                       description: Email address of the candidate
 *                       example: "<EMAIL>"
 *                     gender:
 *                       type: string
 *                       description: Gender of the candidate
 *                       example: "Male"
 *                     status:
 *                       type: string
 *                       description: Application status
 *                       example: "Pending"
 *                     appliedAt:
 *                       type: string
 *                       format: date-time
 *                       description: Application submission timestamp
 *                       example: "2024-01-15T10:30:00.000Z"
 *                 code:
 *                   type: integer
 *                   example: 201
 *       400:
 *         description: Bad request - validation error or business logic error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Candidate has already applied for this job"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized - authentication required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */

// Route to apply for a job by submitting a job application
jobApplyRoutes.post(
  ROUTES.JOB_APPLY.APPLY_JOB,
  schemaValidation(applyJobValidationSchema),
  HandleErrors(applyJob)
);

export default jobApplyRoutes;
