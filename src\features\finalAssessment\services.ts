import jwt from "jsonwebtoken";
import DbConnection from "../../db/dbConnection";

import {
  API_RESPONSE_MSG,
  FINAL_ASSESSMENT_MSG,
  CANDIDATE_APPLICATION_MSG,
  QuestionType,
  REDIS_KEYS,
} from "../../utils/constants";
import sendFinalAssessmentEmail from "../../utils/sendFinalAssessmentEmail";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import JobSkillsModel from "../../schema/s9-innerview/job_skills";
import FinalAssessmentsModel from "../../schema/s9-innerview/final_assessments";
import { FinalAssessmentQuestionsModel } from "../../schema/s9-innerview/final_assessment_questions";
import { ResponseObject } from "../../interface/commonInterface";
import {
  IFinalAssessmentData,
  ICreateManualQuestionRequest,
} from "./interface";
import JobApplicationsModel from "../../schema/s9-innerview/job_applications";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import SkillsModel from "../../schema/s9-innerview/skills";
import openai from "../../utils/openai";
import * as helper from "../../utils/helper";
import { createAssessmentToken } from "../../middleware/generate";
import NotificationServices from "../notification/services";
import { NotificationType } from "../../schema/s9-innerview/notifications";
import InterviewModel from "../../schema/s9-innerview/interview";
import { handleSentryError, stripHtmlTags } from "../../utils/helper";
import { getSecretKeys } from "../../config/awsConfig";
import Cache from "../../db/cache";

export class FinalAssessmentServices {
  static response: ResponseObject;

  /**
   * Generate a JWT token for a final assessment
   * @param finalAssessmentId - ID of the final assessment
   * @returns Object with token and success message
   */
  static generateAssessmentToken = async (finalAssessmentId: number) => {
    try {
      // Verify that the final assessment exists
      const connection = await DbConnection.getS9InnerviewDataSource();
      const finalAssessmentRepo = connection.getRepository(
        FinalAssessmentsModel
      );

      const finalAssessment = await finalAssessmentRepo.findOne({
        where: { id: finalAssessmentId },
      });

      if (!finalAssessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }

      // Generate token with finalAssessmentId in payload
      const token = await createAssessmentToken({ finalAssessmentId });

      return {
        success: true,
        message: "Assessment token generated successfully",
        data: {
          token,
          finalAssessmentId,
          expiresIn: "24 hours",
        },
      };
    } catch (error) {
      handleSentryError(error, "generateAssessmentToken");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Create a new final assessment
   * @param requestData - Final assessment data
   */
  static createFinalAssessment = async (requestData: IFinalAssessmentData) => {
    const connection = await DbConnection.getS9InnerviewDataSource(); // however you obtain your DataSource
    const queryRunner = connection.createQueryRunner();

    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const { jobId, jobApplicationId, orgId } = requestData;

      // Check if all interviews are ended and have feedback
      const interviewRepo = queryRunner.manager.getRepository(InterviewModel);

      const interviews = await interviewRepo.find({
        where: {
          jobApplicationId,
        },
      });

      // Validate minimum 1 interview round exists
      if (!interviews || interviews.length < 1) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message:
            CANDIDATE_APPLICATION_MSG.minimum_one_interview_round_required,
        };
      }

      // Check if all interviews are ended
      const notEndedInterviews = interviews.filter((i) => !i.isEnded);
      if (notEndedInterviews.length > 0) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interviews_not_completed,
        };
      }

      // Check if all interviews have feedback filled
      const pendingFeedbackInterviews = interviews.filter(
        (i) => !i.isFeedbackFilled
      );
      if (pendingFeedbackInterviews.length > 0) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interview_feedback_pending,
        };
      }

      // Repos via transaction manager
      const finalAssessmentRepo = queryRunner.manager.getRepository(
        FinalAssessmentsModel
      );
      const jobRepo = queryRunner.manager.getRepository(JobsModel);
      const jobAppRepo =
        queryRunner.manager.getRepository(JobApplicationsModel);
      const jobSkillsRepo = queryRunner.manager.getRepository(JobSkillsModel);

      // Fetch in parallel
      const [job, jobApp, existingAssessment] = await Promise.all([
        jobRepo.findOne({ where: { id: jobId, orgId, isActive: true } }),
        jobAppRepo.findOne({ where: { id: jobApplicationId, isActive: true } }),
        finalAssessmentRepo.findOne({
          where: { jobId, jobApplicationId },
        }),
      ]);

      console.log("Interviews:", interviews[0].interviewSummary);
      console.log(
        "stripHtmlTags(job.finalJobDescriptionHtml)",
        stripHtmlTags(job?.finalJobDescriptionHtml)
      );

      if (!job) {
        await queryRunner.rollbackTransaction();
        return { success: false, message: FINAL_ASSESSMENT_MSG.job_not_found };
      }
      if (!jobApp) {
        await queryRunner.rollbackTransaction();
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.job_application_not_found,
        };
      }
      console.log("Existing assessment:", existingAssessment);
      if (existingAssessment && existingAssessment.isFinalAssessmentGenerated) {
        await queryRunner.rollbackTransaction();
        const msg = existingAssessment.isAssessmentShared
          ? FINAL_ASSESSMENT_MSG.assessment_already_shared
          : FINAL_ASSESSMENT_MSG.assessment_already_exists;
        return { success: false, message: msg };
      }

      // Fetch job skills + metadata
      const jobSkills = await jobSkillsRepo
        .createQueryBuilder("js")
        .innerJoinAndSelect("js.skill", "skill")
        .select([
          "js.skillId",
          "js.type",
          "skill.title",
          "skill.shortDescription",
        ])
        .where("js.jobId = :jobId", { jobId })
        .getMany();

      if (jobSkills.length) {
        // Generate questions for all skills in a single API call
        const skillsForGeneration = jobSkills.map((js) => ({
          skillId: js.skillId,
          title: js.skill.title,
          shortDescription: js.skill.shortDescription,
          type: js.type,
        }));
        console.log("Skills for generation:", skillsForGeneration);

        const interviewSummary =
          typeof interviews[0]?.interviewSummary === "string"
            ? interviews[0].interviewSummary
            : JSON.stringify(interviews[0]?.interviewSummary);
        console.log("Interview summary for API call:", interviewSummary);

        const jobDescription = stripHtmlTags(job?.finalJobDescriptionHtml);
        console.log("Job description for API call:", jobDescription);

        console.log("Calling OpenAI to generate questions");

        // setting final assessment generation status in redis
        const cache = new Cache();

        const redisKey = REDIS_KEYS.FINAL_ASSESSMENT_GENERATION_STATUS.replace(
          "{jobApplicationId}",
          String(jobApplicationId)
        );

        // Check permission_update_status flag
        await cache.set(redisKey, "1", 60 * 10);
        setImmediate(async () => {
          const queryRunnerTwo = connection.createQueryRunner();

          await queryRunnerTwo.connect();
          await queryRunnerTwo.startTransaction();
          try {
            const generatedQuestionsMap =
              await openai.generateQuestionsForMultipleSkills(
                skillsForGeneration,
                interviewSummary,
                jobDescription
              );

            console.log(
              ">>>>>>>>>>>>>>>>generatedQuestionsMap",
              generatedQuestionsMap
            );

            // Repos via transaction manager
            const finalAssessmentRepoTwo = queryRunnerTwo.manager.getRepository(
              FinalAssessmentsModel
            );
            const faqRepo = queryRunnerTwo.manager.getRepository(
              FinalAssessmentQuestionsModel
            );
            let existingFinalAssessment = await finalAssessmentRepoTwo.findOne({
              where: { jobId, jobApplicationId },
            });
            console.log(
              ">>>>>>>>>>>>>>>>>>>>>>>>>existingFinalAssessment",
              existingFinalAssessment
            );

            if (!existingFinalAssessment) {
              const finalAssessment = new FinalAssessmentsModel();
              finalAssessment.jobId = jobId;
              finalAssessment.jobApplicationId = jobApplicationId;
              finalAssessment.isAssessmentSubmitted = false;
              finalAssessment.isAssessmentShared = false;
              finalAssessment.isFinalAssessmentGenerated = true;
              existingFinalAssessment =
                await finalAssessmentRepoTwo.save(finalAssessment);

              console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>");
              console.log("Created Final Assessment:", existingFinalAssessment);
            } else {
              console.log("Assessment already exists, skipping creation");
              existingFinalAssessment.isFinalAssessmentGenerated = true;
              await finalAssessmentRepoTwo.save(existingFinalAssessment);
              console.log("Assessment marked as generated");
            }

            console.log(
              ">>>>>>>>>>>>>>>>>>>>existingFinalAssessment 1 ",
              existingFinalAssessment
            );

            // Transform the generated questions into the required format
            const allQuestions = Object.entries(generatedQuestionsMap).flatMap(
              ([skillId, questions]) =>
                questions.map((q) => ({
                  finalAssessmentId: existingFinalAssessment.id,
                  skillId: parseInt(skillId, 10),
                  question: q.question,
                  questionType:
                    q.type === QuestionType.MCQ
                      ? QuestionType.MCQ
                      : QuestionType.TRUE_FALSE,
                  options: { options: q.options },
                  correctAnswer:
                    q.type === QuestionType.MCQ
                      ? q.correctAnswerId
                      : q.correctAnswer.toString(),
                  applicantAnswer: null,
                }))
            );

            console.log("Final Assessment allQuestions:", allQuestions);

            if (allQuestions.length) {
              console.log(
                "Saving questions to database, count:",
                allQuestions.length
              );
              const savedQuestions = await faqRepo.save(allQuestions);
              console.log(
                "Questions saved successfully, count:",
                savedQuestions.length
              );
            } else {
              console.log(
                ">>>>>>>>>>>>>>>>>>>>final_assessment_generated_failed applicationId",
                jobApplicationId,
                allQuestions
              );
            }

            await cache.set(redisKey, "0");
            await queryRunnerTwo.commitTransaction();
          } catch (err) {
            await queryRunnerTwo.rollbackTransaction();
            await cache.set(redisKey, "0");
            handleSentryError(err, "createFinalAssessment - setImmediate");
          }
        });
      } else {
        console.log(
          ">>>>>>>>>>>>>>>>>>>>final_assessment_generated_failed_no_skills applicationId",
          jobApplicationId
        );
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.final_assessment_generated_failed,
        };
      }

      // Commit all writes
      await queryRunner.commitTransaction();

      // Return successful response with processing message
      return {
        success: true,
        message: "final_assessment_processing_started",
        // data: {
        //   assessmentId: savedAssessment.id,
        //   jobId: savedAssessment.jobId,
        //   jobApplicationId: savedAssessment.jobApplicationId,
        // },
      };
    } catch (err) {
      await queryRunner.rollbackTransaction();
      handleSentryError(err, "createFinalAssessment");
      return {
        success: false,
        message: FINAL_ASSESSMENT_MSG.final_assessment_generated_failed,
      };
    } finally {
      await queryRunner.release();
    }
  };

  /**
   * Get final assessment questions grouped by skill type
   * @param finalAssessmentId - ID of the final assessment
   * @returns Questions grouped by skill type
   */
  static getFinalAssessmentQuestion = async ({
    finalAssessmentId,
    jobId,
    jobApplicationId,
    orgId,
  }: {
    finalAssessmentId: number;
    jobId: number;
    jobApplicationId: number;
    orgId: number;
  }) => {
    try {
      const {
        finalAssessmentRepo,
        finalAssessmentQuestionsRepo,
        jobSkillsRepo,
        jobRepo,
      } = await helper.getRepositories({
        finalAssessmentRepo: FinalAssessmentsModel,
        finalAssessmentQuestionsRepo: FinalAssessmentQuestionsModel,
        jobSkillsRepo: JobSkillsModel,
        jobRepo: JobsModel,
      });

      // Verify job exists and belongs to the organization
      const job = await jobRepo.findOne({
        where: {
          id: jobId,
          orgId,
          isActive: true,
        },
      });

      if (!job) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.job_not_found,
        };
      }

      // Verify assessment exists
      const assessment = await finalAssessmentRepo.findOne({
        where: {
          id: finalAssessmentId,
          jobId,
          jobApplicationId,
        },
      });

      if (!assessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }

      // Get all questions for this assessment
      const questions = await finalAssessmentQuestionsRepo
        .createQueryBuilder("question")
        .where("question.final_assessment_id = :finalAssessmentId", {
          finalAssessmentId,
        })
        .getMany();

      if (!questions || questions.length === 0) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.no_questions_found,
        };
      }

      // Get job ID from assessment to fetch skill types

      // Get all skills for this job to get their types
      const jobSkills = await jobSkillsRepo
        .createQueryBuilder("jobSkill")
        .innerJoinAndSelect("jobSkill.skill", "skill")
        .select([
          "jobSkill.id",
          "jobSkill.skillId",
          "jobSkill.type",
          "skill.id",
          "skill.title",
        ])
        .where("jobSkill.jobId = :jobId", { jobId })
        .getMany();

      // Define interface for skill info
      interface SkillInfo {
        type: string;
        title: string;
      }

      // Create a Map of skillId to skill type and title (more efficient than plain object)
      const skillMap = new Map<number, SkillInfo>(
        jobSkills.map((jobSkill) => [
          jobSkill.skillId,
          {
            type: jobSkill.type,
            title: jobSkill.skill.title,
          },
        ])
      );

      // Process questions and group by skill type in a single pass
      const questionsByType = questions.reduce(
        (acc: Record<string, { type: string; questions: any[] }>, question) => {
          const skillInfo =
            skillMap.get(question.skillId) ||
            ({
              type: "unknown",
              title: "Unknown Skill",
            } as SkillInfo);
          const skillType = skillInfo.type;

          if (!acc[skillType]) {
            acc[skillType] = {
              type: skillType,
              questions: [],
            };
          }

          acc[skillType].questions.push({
            id: question.id,
            question: question.question,
            questionType: question.questionType,
            skillId: question.skillId,
            skillTitle: skillInfo.title,
            options: question.options,
            correctAnswer: question.correctAnswer,
            applicantAnswer: question.applicantAnswer,
          });

          return acc;
        },
        {}
      );

      // Convert to array for easier frontend handling
      const questionTypeGroups = Object.values(questionsByType);

      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.questions_fetched,
        data: {
          assessmentId: finalAssessmentId,
          jobApplicationId: assessment.jobApplicationId,
          isAssessmentSubmitted: assessment.isAssessmentSubmitted,
          isAssessmentShared: assessment.isAssessmentShared,
          questionGroups: questionTypeGroups,
        },
      };
    } catch (error) {
      handleSentryError(error, "getFinalAssessmentQuestion");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
      };
    }
  };

  /**
   * Add a manual question to a final assessment
   * @param finalAssessmentId - ID of the final assessment
   * @param questionData - Question data to be added
   */
  static addManualFinalAssessmentQuestion = async (
    questionData: ICreateManualQuestionRequest
  ) => {
    try {
      // Get repositories
      const { finalAssessmentRepo, finalAssessmentQuestionsRepo, skillsRepo } =
        await helper.getRepositories({
          finalAssessmentRepo: FinalAssessmentsModel,
          finalAssessmentQuestionsRepo: FinalAssessmentQuestionsModel,
          skillsRepo: SkillsModel,
        });

      // Verify assessment exists
      const assessment = await finalAssessmentRepo.findOne({
        where: {
          id: questionData.finalAssessmentId,
        },
      });

      if (!assessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }

      // Verify skill exists
      const skill = await skillsRepo.findOne({
        where: {
          id: questionData.skillId,
        },
      });

      if (!skill) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.skill_not_found,
        };
      }
      const newQuestion = finalAssessmentQuestionsRepo.create({
        finalAssessmentId: questionData.finalAssessmentId,
        skillId: questionData.skillId,
        question: questionData.question,
        questionType: questionData.questionType as QuestionType,
        options: questionData.options,
        correctAnswer: questionData.correctAnswer,
      });

      const savedQuestion =
        await finalAssessmentQuestionsRepo.save(newQuestion);
      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.final_assessment_questions_created,
        data: {
          ...savedQuestion,
          skillTitle: skill.title,
        },
      };
    } catch (error) {
      handleSentryError(error, "addManualFinalAssessmentQuestion");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
        error: error.message,
      };
    }
  };

  /**
   * Share assessment to candidate via email
   * @param finalAssessmentId - ID of the final assessment
   * @param assessmentLink - Link to the assessment
   * @param jobApplicationId - ID of the job application
   * @param orgId - Organization ID from the request
   */
  static shareAssessmentToCandidate = async (
    finalAssessmentId: number,
    assessmentLink: string,
    jobApplicationId: number,
    orgId: number
  ) => {
    try {
      // Get repositories
      const {
        finalAssessmentRepo,
        jobApplicationRepo,
        candidateRepo,
        jobRepo,
      } = await helper.getRepositories({
        finalAssessmentRepo: FinalAssessmentsModel,
        jobApplicationRepo: JobApplicationsModel,
        candidateRepo: CandidatesModel,
        jobRepo: JobsModel,
      });

      // Verify job application exists
      const jobApplication = await jobApplicationRepo.findOne({
        where: {
          id: jobApplicationId,
        },
        relations: ["candidate", "job"],
      });

      if (!jobApplication) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.job_application_not_found,
        };
      }

      // Get job and candidate IDs from job application
      const { jobId, candidateId } = jobApplication;

      // Verify job exists and belongs to the organization
      const job = await jobRepo.findOne({
        where: {
          id: jobId,
          orgId,
          isActive: true,
        },
      });

      if (!job) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.job_not_found,
        };
      }

      // Get candidate details
      const candidate = await candidateRepo.findOne({
        where: {
          id: candidateId,
          orgId,
        },
      });

      if (!candidate) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.candidate_not_found,
        };
      }

      // Verify assessment exists
      const assessment = await finalAssessmentRepo.findOne({
        where: {
          id: finalAssessmentId,
          jobApplicationId,
          jobId,
        },
      });

      if (!assessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }

      // Update assessment as shared
      assessment.isAssessmentShared = true;
      await finalAssessmentRepo.save(assessment);

      // Send email to candidate
      const candidateEmail = candidate.email;
      const candidateName = candidate.name;
      const jobTitle = job.title;

      // Send assessment email using the utility function
      const emailResult = await sendFinalAssessmentEmail({
        candidateEmail,
        candidateName,
        jobTitle,
        assessmentLink,
      });

      if (emailResult.isError) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.failed_to_send_assessment_email,
          error: emailResult.errorMessage,
        };
      }

      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.assessment_shared_successfully,
        data: {
          candidateEmail,
          candidateName,
          jobTitle,
          assessmentLink,
        },
      };
    } catch (error) {
      handleSentryError(error, "shareAssessment");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
      };
    }
  };

  /**
   * Get final assessment by token for candidate view
   * @param token - JWT token containing finalAssessmentId
   */
  static getFinalAssessmentByCandidate = async (finalAssessmentId: number) => {
    try {
      const { finalAssessmentRepo, finalAssessmentQuestionsRepo } =
        await helper.getRepositories({
          finalAssessmentRepo: FinalAssessmentsModel,
          finalAssessmentQuestionsRepo: FinalAssessmentQuestionsModel,
        });

      // Verify assessment exists and is not submitted
      const assessment = await finalAssessmentRepo
        .createQueryBuilder("a")
        .select(["a.id", "a.jobApplicationId"])
        .where("a.id = :id AND a.isAssessmentSubmitted = false", {
          id: finalAssessmentId,
        })
        .getOne();

      console.log("assessment", assessment);
      if (!assessment) {
        const msg = (await finalAssessmentRepo.findOne({
          where: { id: finalAssessmentId },
        }))
          ? FINAL_ASSESSMENT_MSG.assessment_already_submitted
          : FINAL_ASSESSMENT_MSG.assessment_not_found;
        return { success: false, message: msg };
      }

      // Fetch questions with skill info in one query
      const raw = await finalAssessmentQuestionsRepo
        .createQueryBuilder("q")
        .where("q.finalAssessmentId = :id", { id: finalAssessmentId })
        .innerJoin(FinalAssessmentsModel, "a", "a.id = q.finalAssessmentId")
        .innerJoin(
          JobSkillsModel,
          "js",
          "js.skillId = q.skillId AND js.jobId = a.jobId"
        )
        .innerJoinAndSelect("js.skill", "skill")
        .select([
          "q.id as id",
          "q.question as question",
          "q.questionType as questionType",
          "q.skillId as skillId",
          "q.options as options",
          "q.applicantAnswer as applicantAnswer",
          "js.type as skillType",
          "skill.title as skillTitle",
        ])
        .getRawMany();
      if (raw.length === 0) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.no_questions_found,
        };
      }

      // Group by skillType and parse options
      // Create a Map to track unique question IDs to prevent duplicates
      const questionIdMap = new Map<number, string>();

      const grouped = raw.reduce(
        (acc, row) => {
          const type = row.skillType || "unknown";
          if (!acc[type]) acc[type] = { type, questions: [] };

          // Skip if we've already processed this question ID
          if (questionIdMap.has(row.id)) {
            return acc;
          }

          // Mark this question as processed for this type
          questionIdMap.set(row.id, type);

          let options;
          if (typeof row.options === "string") {
            try {
              options = JSON.parse(row.options);
            } catch {
              options = [];
            }
          } else {
            options = row.options;
          }

          acc[type].questions.push({
            id: row.id,
            question: row.question,
            questionType: row.questionType,
            skillId: row.skillId,
            skillTitle: row.skillTitle,
            options,
            applicantAnswer: row.applicantAnswer,
            // correctAnswer intentionally omitted
          });
          return acc;
        },
        {} as Record<string, { type: string; questions: any[] }>
      );

      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.questions_fetched,
        data: {
          assessmentId: finalAssessmentId,
          jobApplicationId: assessment.jobApplicationId,
          questionGroups: Object.values(grouped),
        },
      };
    } catch (error) {
      handleSentryError(error, "getFinalAssessmentByCandidate");
      return { success: false, message: API_RESPONSE_MSG.invalid_data };
    }
  };

  /**
   * Submit answers by candidate for a final assessment
   * @param finalAssessmentId - Encrypted ID of the final assessment
   * @param candidateEmail - Email of the candidate
   * @param answers - Array of candidate answers
   */
  static submitAnswerByCandidate = async (
    finalAssessmentId: number,
    candidateEmail: string,
    answers: Array<{ questionId: number; answer: string }>
  ) => {
    try {
      console.log("=== SUBMIT ANSWER BY CANDIDATE DEBUG START ===");
      console.log("Input params:", {
        finalAssessmentId,
        candidateEmail,
        answersCount: answers.length,
      });

      const {
        finalAssessmentRepo,
        finalAssessmentQuestionsRepo,
        candidateRepo,
      } = await helper.getRepositories({
        finalAssessmentRepo: FinalAssessmentsModel,
        finalAssessmentQuestionsRepo: FinalAssessmentQuestionsModel,
        candidateRepo: CandidatesModel,
      });

      // Verify assessment exists and not submitted
      const assessment = await finalAssessmentRepo
        .createQueryBuilder("fa")
        .innerJoin("jobs", "jobs", "jobs.id = fa.jobId")
        .innerJoin("job_applications", "ja", "ja.id = fa.jobApplicationId")
        .where("fa.id = :id", { id: finalAssessmentId })
        .select([
          "jobs.orgId as orgId",
          "fa.jobApplicationId as jobApplicationId",
          "fa.id as id",
          "fa.isAssessmentSubmitted as isAssessmentSubmitted",
        ])
        .getRawOne();

      console.log("Assessment query result:", assessment);

      if (!assessment) {
        console.log("❌ Assessment not found");
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }
      if (assessment.isAssessmentSubmitted) {
        console.log("❌ Assessment already submitted");
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_already_shared,
        };
      }

      // Verify candidate by email
      const candidate = await candidateRepo
        .createQueryBuilder("candidate")
        .where("candidate.orgId = :orgId", { orgId: assessment.orgId })
        .andWhere("candidate.email = :email", {
          email: candidateEmail.toLowerCase(),
        })
        .select([
          "candidate.id as id",
          "candidate.name as name",
          "candidate.orgId as orgId",
        ])
        .getRawOne();

      console.log("Candidate query result:", candidate);

      if (!candidate) {
        console.log("❌ Candidate not found");
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.candidate_not_found,
        };
      }

      // Fetch all questions and map by ID
      const questions = await finalAssessmentQuestionsRepo.find({
        where: { finalAssessmentId },
      });

      console.log("Questions found:", questions.length);

      if (!questions.length) {
        console.log("❌ No questions found");
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.no_questions_found,
        };
      }

      const questionMap = new Map<number, (typeof questions)[0]>(
        questions.map((q) => [q.id, q])
      );

      // Validate submitted answers
      const invalidIds = answers.reduce((acc, a) => {
        if (!questionMap.has(a.questionId)) {
          acc.push(a.questionId);
        }
        return acc;
      }, [] as number[]);

      if (invalidIds.length) {
        console.log("❌ Invalid question IDs:", invalidIds);
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.invalid_question_ids,
        };
      }

      // Update answers in-place
      answers.forEach(({ questionId, answer }) => {
        const q = questionMap.get(questionId)!;
        q.applicantAnswer = answer;
      });

      // Bulk save updated questions
      const res = await finalAssessmentQuestionsRepo.save(
        Array.from(questionMap.values())
      );
      console.log("✅ Questions updated successfully, count:", res.length);

      // Update assessment as submitted
      const updateRes = await finalAssessmentRepo
        .createQueryBuilder()
        .update(FinalAssessmentsModel)
        .set({
          isAssessmentSubmitted: true,
        })
        .where("id = :id", { id: finalAssessmentId })
        .execute();
      console.log(
        "✅ Assessment marked as submitted, affected rows:",
        updateRes.affected
      );

      // === NOTIFICATION DEBUGGING STARTS HERE ===
      console.log("\n=== NOTIFICATION DEBUGGING ===");
      console.log(
        "Looking for interviewers for jobApplicationId:",
        assessment.jobApplicationId
      );

      const interviewRepo =
        await DbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interview = await interviewRepo
        .createQueryBuilder("interview")
        .select("DISTINCT interview.scheduledBy", "scheduledBy")
        .andWhere("interview.jobApplicationId = :jobApplicationId", {
          jobApplicationId: assessment.jobApplicationId,
        })
        .getRawMany();

      console.log("Interviewers found:", interview);
      console.log("Number of interviewers:", interview.length);

      if (interview.length === 0) {
        console.log("⚠️ No interviewers found for this job application");
      }

      // Send notifications to each interviewer
      interview.forEach((interviewer, index) => {
        console.log(`\n--- Sending notification ${index + 1} ---`);
        console.log("Interviewer scheduledBy ID:", interviewer.scheduledBy);
        console.log("Candidate orgId:", candidate.orgId);
        console.log("Candidate name:", candidate.name);
        console.log("Assessment ID:", assessment.id);

        const notificationData = {
          type: NotificationType.FINAL_ASSESSMENT_SUBMITTED,
          title: NotificationType.FINAL_ASSESSMENT_SUBMITTED,
          description: `Final assessment for ${candidate.name} is ready. Review and finalize.`,
          relatedId: assessment.id,
        };

        console.log("Notification data:", notificationData);

        try {
          const notificationResult = NotificationServices.createNotification(
            candidate.orgId,
            interviewer.scheduledBy,
            notificationData
          );
          console.log(
            "✅ Notification sent successfully for interviewer:",
            interviewer.scheduledBy
          );
          console.log("Notification result:", notificationResult);
        } catch (notificationError) {
          console.log(
            "❌ Error sending notification to interviewer:",
            interviewer.scheduledBy
          );
          console.log("Notification error:", notificationError);
        }
      });

      console.log("=== NOTIFICATION DEBUGGING END ===\n");

      console.log("=== SUBMIT ANSWER BY CANDIDATE DEBUG END ===");
      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.assessment_submitted_successfully,
      };
    } catch (error) {
      console.log("❌ ERROR in submitAnswerByCandidate:", error);
      handleSentryError(error, "submitAnswerByCandidate");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
        error: error.message,
      };
    }
  };

  /**
   * Get assessment status based on isShare and isSubmit flags
   * @param jobId - ID of the job
   * @param jobApplicationId - ID of the job application
   * @returns Assessment status information
   */
  // this api is no longer in use

  static getAssessmentStatus = async (jobApplicationId: number) => {
    try {
      // Get repositories
      const { finalAssessmentRepo } = await helper.getRepositories({
        finalAssessmentRepo: FinalAssessmentsModel,
      });

      // Find assessment by jobId and jobApplicationId
      const assessment = await finalAssessmentRepo.findOne({
        where: {
          jobApplicationId,
        },
      });

      if (!assessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }

      // Return assessment status
      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.assessment_status_fetched,
        data: {
          assessmentId: assessment.id,
          jobApplicationId: assessment.jobApplicationId,
          isAssessmentSubmitted: assessment.isAssessmentSubmitted,
          isAssessmentShared: assessment.isAssessmentShared,
        },
      };
    } catch (error) {
      handleSentryError(error, "getAssessmentStatus");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
      };
    }
  };

  /**
   * Verify if candidate email exists in the system for a specific assessment
   * @param email - Email of the candidate
   * @param encryptedId - Encrypted ID of the final assessment
   * @returns Object indicating if email exists and is valid
   */
  static verifyCandidateEmail = async (email: string, token: string) => {
    try {
      // Decrypt the assessment ID
      console.log(email);

      const { finalAssessmentSecrete } = await getSecretKeys();
      const decoded = jwt.verify(token, finalAssessmentSecrete) as {
        finalAssessmentId: string;
      };
      // console.log("-------decoded-----",decoded)

      // Get repositories
      const { finalAssessmentRepo } = await helper.getRepositories({
        finalAssessmentRepo: FinalAssessmentsModel,
      });

      // Get assessment with related job application and candidate in a single query
      const assessment = await finalAssessmentRepo
        .createQueryBuilder("assessment")
        .leftJoinAndSelect("assessment.jobApplication", "jobApplication")
        .leftJoinAndSelect("jobApplication.candidate", "candidate")
        .where("assessment.id = :id", { id: decoded.finalAssessmentId })
        .getOne();

      if (!assessment) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_not_found,
        };
      }
      // Check if assessment is already submitted
      if (assessment.isAssessmentSubmitted) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.assessment_already_submitted,
        };
      }

      // Check if job application exists
      if (!assessment.jobApplication) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.job_application_not_found,
        };
      }

      // Check if candidate exists
      if (!assessment.jobApplication.candidate) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.candidate_not_found,
        };
      }

      // Check if the email matches the candidate's email
      if (
        assessment.jobApplication.candidate.email.toLowerCase() !==
        email.toLowerCase()
      ) {
        return {
          success: false,
          message: FINAL_ASSESSMENT_MSG.not_authenticated,
        };
      }

      return {
        success: true,
        message: FINAL_ASSESSMENT_MSG.email_verified_successfully,
        data: {
          finalAssessmentId: +decoded.finalAssessmentId,
        },
      };
    } catch (error) {
      if (error.name === "TokenExpiredError")
        return {
          message: FINAL_ASSESSMENT_MSG.assessment_has_expired,
          success: false,
        };
      handleSentryError(error, "verifyCandidateEmail");
      return {
        success: false,
        message: API_RESPONSE_MSG.invalid_data,
        error: error.message,
      };
    }
  };
}

export default FinalAssessmentServices;
