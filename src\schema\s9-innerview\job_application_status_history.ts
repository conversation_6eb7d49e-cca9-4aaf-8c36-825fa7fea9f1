import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  C<PERSON><PERSON>ate<PERSON><PERSON><PERSON>n,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
} from "typeorm";
import JobApplicationsModel from "./job_applications";

/**
 * Schema for job application status history
 * Tracks changes to job application status
 */
@Entity("job_application_status_history")
export default class JobApplicationStatusHistoryModel {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => JobApplicationsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "application_id" })
  application: JobApplicationsModel;

  @Column({ name: "application_id", nullable: false })
  applicationId: number;

  @Column({ name: "changed_by_user_id", nullable: false })
  changedByUserId: number;

  @Column({ length: 50, name: "previous_status", nullable: false })
  previousStatus: string;

  @Column({ length: 50, name: "new_status", nullable: false })
  newStatus: string;

  @Column({ length: 50, nullable: true, name: "reason" })
  reason: string;

  @CreateDateColumn({
    type: "timestamp",
    name: "changed_at",
  })
  changedAt: Date;
}
