import {
  Column,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";

/* eslint-disable no-unused-vars */

export enum WalkthroughName {
  DASHBOARD = "dashboard",
  INTERVIEW_FEEDBACK = "interview_feedback",
  USER_ROLES = "user_roles",
  USER_PERMISSIONS = "user_permissions",
  CALENDAR = "calendar",
  ONE_TO_ONE_INTERVIEW = "one_to_one_interview",
  VIDEO_CALL_INTERVIEW = "video_call_interview",
  DRAG_AND_DROP = "drag_and_drop",
  CANDIDATE_PROFILE = "candidate_profile",
  TOP_TEN_CANDIDATES = "top_ten_candidates",
}

@Entity("walkthrough_status")
export default class WalkthroughStatusModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: "enum",
    enum: WalkthroughName,
    nullable: false,
  })
  name: WalkthroughName;

  @Column({ name: "user_id", nullable: false })
  userId: number;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
  })
  updatedTs: Date;
}
