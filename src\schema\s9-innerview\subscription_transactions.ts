import {
  Entity,
  PrimaryGeneratedC<PERSON>umn,
  <PERSON>um<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Create<PERSON>ate<PERSON><PERSON>umn,
} from "typeorm";
import OrganizationSubscriptionModel from "./organization_subscriptions";

/* eslint-disable no-unused-vars */
export enum TransactionType {
  PURCHASE = "Purchase",
  REFUND = "Refund",
  UPGRADE = "Upgrade",
}

export enum PaymentStatus {
  SUCCESS = "Success",
  PENDING = "Pending",
  FAILED = "Failed",
}

export enum TransactionMethod {
  CARD = "Card",
}

@Entity("subscription_transactions")
class SubscriptionTransactionModel {
  @PrimaryGeneratedColumn()
  id: number;

  @JoinColumn({ name: "organization_subscription_id" })
  organizationSubscription: OrganizationSubscriptionModel;

  @Column({ name: "organization_subscription_id", nullable: true })
  organizationSubscriptionId: number;

  @Column({
    type: "enum",
    enum: PaymentStatus,
    name: "payment_status",
    nullable: false,
  })
  paymentStatus: PaymentStatus;

  @Column({
    name: "amount",
    type: "decimal",
    precision: 10,
    scale: 2,
    nullable: false,
  })
  amount: number;

  @Column({
    type: "enum",
    enum: TransactionType,
    name: "transaction_type",
    nullable: false,
  })
  transactionType: TransactionType;

  @Column({
    type: "enum",
    enum: TransactionMethod,
    name: "transaction_method",
    nullable: false,
  })
  transactionMethod: TransactionMethod;

  @Column({
    name: "invoice_id",
    type: "varchar",
    length: 100,
    nullable: false,
  })
  invoiceId: string;

  @Column({
    name: "invoice_url",
    type: "varchar",
    length: 255,
    nullable: true,
  })
  invoiceUrl: string;

  @Column({
    name: "transaction_date",
    type: "timestamp",
    nullable: true,
  })
  transactionDate: Date;

  @Column({ name: "organization_id", nullable: false })
  organizationId: number;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;
}

export default SubscriptionTransactionModel;
