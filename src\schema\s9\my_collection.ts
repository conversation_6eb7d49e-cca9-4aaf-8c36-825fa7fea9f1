import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>o<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "typeorm";
import UserModel from "./user";

export interface IMyCollection {
  id: number;
  name: string;
  is_protected: boolean;
  created_ts: Date;
  updated_ts: Date;
}

@Entity("my_collection")
class MyCollectionModel implements IMyCollection {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: "text", collation: "utf8mb4_unicode_ci" })
  name: string;

  @Column()
  user_id: number;

  @Column({ default: false })
  is_protected: boolean;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_ts: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  updated_ts: Date;

  // @OneToMany(
  //   () => PersonalStuffModel,
  //   (personalStuff) => personalStuff.myCollection
  // )
  // personal_stuff: PersonalStuffModel[];

  @ManyToOne(() => UserModel, (user) => user.my_collections)
  @JoinColumn({
    name: "user_id",
  })
  user: UserModel | number;
}

export default MyCollectionModel;
