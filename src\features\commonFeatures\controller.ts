import { Request, Response } from "express";
import CommonServices from "./services";
import { handleSentryError } from "../../utils/helper";

/**
 * Generate presigned url.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const generatePresignedUrl = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const data = await CommonServices.generatePresignedUrl(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "generatePresignedUrl");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Remove attachments from S3.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const removeAttachmentsFromS3 = async (req: Request, res: Response) => {
  try {
    const data = await CommonServices.removeAttachmentsFromS3(
      req.body.fileUrlArray ?? ""
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "removeAttachmentsFromS3");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
