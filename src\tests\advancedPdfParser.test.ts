// Test file for Advanced PDF Parser
// Note: This requires Je<PERSON> to be properly configured
import * as fs from 'fs';
import * as path from 'path';
import { advancedPdfParser, parsePdfWithAdvancedRetries } from '../utils/advancedPdfParser';

describe('Advanced PDF Parser', () => {
  let testPdfBuffer: Buffer;
  
  beforeEach(() => {
    // Create a simple test PDF buffer (you would use a real PDF file in practice)
    // For testing purposes, we'll create a minimal PDF structure
    testPdfBuffer = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
  });

  afterEach(() => {
    // Cleanup any temporary files if needed
  });

  describe('parsePdf', () => {
    it('should parse PDF with text extraction', async () => {
      try {
        const result = await advancedPdfParser.parsePdf(testPdfBuffer, {
          extractImages: false,
          useOcr: false
        });

        expect(result).toBeDefined();
        expect(result.extractionMethod).toBe('text');
        expect(typeof result.text).toBe('string');
      } catch (error) {
        // Expected for minimal test PDF - just ensure it doesn't crash
        expect(error).toBeDefined();
      }
    });

    it('should handle OCR extraction when text extraction fails', async () => {
      try {
        const result = await advancedPdfParser.parsePdf(testPdfBuffer, {
          extractImages: false,
          useOcr: true,
          ocrLanguage: 'eng'
        });

        expect(result).toBeDefined();
        expect(['text', 'ocr', 'hybrid']).toContain(result.extractionMethod);
      } catch (error) {
        // Expected for minimal test PDF - just ensure it doesn't crash
        expect(error).toBeDefined();
      }
    });

    it('should extract images when requested', async () => {
      try {
        const result = await advancedPdfParser.parsePdf(testPdfBuffer, {
          extractImages: true,
          useOcr: false
        });

        expect(result).toBeDefined();
        // Images array should exist (even if empty for test PDF)
        expect(Array.isArray(result.images)).toBe(true);
      } catch (error) {
        // Expected for minimal test PDF - just ensure it doesn't crash
        expect(error).toBeDefined();
      }
    });

    it('should retry on failure', async () => {
      const invalidBuffer = Buffer.from('invalid pdf content');
      
      try {
        await advancedPdfParser.parsePdf(invalidBuffer, {
          extractImages: false,
          useOcr: false,
          maxRetries: 2
        });
      } catch (error) {
        expect(error).toBeDefined();
        expect(error.message).toContain('PDF');
      }
    });
  });

  describe('extractImages', () => {
    it('should extract images from PDF', async () => {
      try {
        const images = await advancedPdfParser.extractImages(testPdfBuffer);
        expect(Array.isArray(images)).toBe(true);
      } catch (error) {
        // Expected for minimal test PDF
        expect(error).toBeDefined();
      }
    });
  });

  describe('getMetadata', () => {
    it('should extract PDF metadata', async () => {
      try {
        const metadata = await advancedPdfParser.getMetadata(testPdfBuffer);
        expect(metadata).toBeDefined();
      } catch (error) {
        // Expected for minimal test PDF
        expect(error).toBeDefined();
      }
    });
  });

  describe('parsePdfWithAdvancedRetries', () => {
    it('should provide backward compatibility', async () => {
      try {
        const result = await parsePdfWithAdvancedRetries(testPdfBuffer, 3);
        
        if (result) {
          expect(result).toHaveProperty('text');
          expect(typeof result.text).toBe('string');
        } else {
          // Null result is acceptable for test PDF
          expect(result).toBeNull();
        }
      } catch (error) {
        // Expected for minimal test PDF
        expect(error).toBeDefined();
      }
    });

    it('should handle invalid input gracefully', async () => {
      const result = await parsePdfWithAdvancedRetries(Buffer.from(''), 1);
      expect(result).toBeNull();
    });
  });

  describe('hasReadableContent', () => {
    it('should identify readable content correctly', () => {
      // Access private method for testing (in real implementation, you might make this public or test indirectly)
      const parser = advancedPdfParser as any;
      
      // Test readable content
      const readableText = "This is a sample resume with John Doe's information and work experience.";
      expect(parser.hasReadableContent(readableText)).toBe(true);
      
      // Test unreadable content
      const unreadableText = "abc";
      expect(parser.hasReadableContent(unreadableText)).toBe(false);
      
      // Test empty content
      expect(parser.hasReadableContent("")).toBe(false);
      
      // Test special characters only
      expect(parser.hasReadableContent("!@#$%^&*()")).toBe(false);
    });
  });
});

describe('Integration Tests', () => {
  describe('Real PDF Processing', () => {
    it('should handle various PDF types', async () => {
      // This test would use real PDF files in a test fixtures directory
      // For now, we'll just ensure the API structure is correct
      
      const testCases = [
        { type: 'text-based', useOcr: false },
        { type: 'scanned', useOcr: true },
        { type: 'template', useOcr: true, extractImages: true }
      ];

      for (const testCase of testCases) {
        try {
          // In a real test, you would load actual PDF files here
          const mockBuffer = Buffer.from('mock pdf content');
          
          const result = await advancedPdfParser.parsePdf(mockBuffer, {
            extractImages: testCase.extractImages || false,
            useOcr: testCase.useOcr,
            maxRetries: 1
          });

          // Test would verify specific expectations based on PDF type
          // For now, just ensure the structure is correct
          if (result) {
            expect(result).toHaveProperty('text');
            expect(result).toHaveProperty('extractionMethod');
            expect(['text', 'ocr', 'hybrid']).toContain(result.extractionMethod);
          }
        } catch (error) {
          // Expected for mock data
          expect(error).toBeDefined();
        }
      }
    });
  });

  describe('Performance Tests', () => {
    it('should complete parsing within reasonable time', async () => {
      const startTime = Date.now();
      
      try {
        const mockBuffer = Buffer.from('mock pdf');
        await parsePdfWithAdvancedRetries(mockBuffer, 1);
      } catch (error) {
        // Expected for test PDF
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should complete within 10 seconds even with retries
      expect(duration).toBeLessThan(10000);
    });
  });

  describe('Error Handling', () => {
    it('should handle corrupted PDF gracefully', async () => {
      const corruptedBuffer = Buffer.from('This is not a PDF file at all!');
      
      const result = await parsePdfWithAdvancedRetries(corruptedBuffer, 1);
      expect(result).toBeNull();
    });

    it('should handle empty buffer', async () => {
      const emptyBuffer = Buffer.alloc(0);
      
      const result = await parsePdfWithAdvancedRetries(emptyBuffer, 1);
      expect(result).toBeNull();
    });

    it('should handle very large files', async () => {
      // Create a large buffer (simulating a large PDF)
      const largeBuffer = Buffer.alloc(10 * 1024 * 1024); // 10MB
      largeBuffer.write('%PDF-1.4', 0); // Make it look like a PDF
      
      try {
        const result = await parsePdfWithAdvancedRetries(largeBuffer, 1);
        // Should either succeed or fail gracefully
        expect(result === null || typeof result === 'object').toBe(true);
      } catch (error) {
        // Expected for mock large file
        expect(error).toBeDefined();
      }
    });
  });
});
