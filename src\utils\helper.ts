import http from "http";
import { Server, Socket } from "socket.io";
import {
  createClient,
  ListenLiveClient,
  LiveTranscriptionEvents,
} from "@deepgram/sdk";
import * as jwt from "jsonwebtoken";
import { Repository } from "typeorm";
import pdf from "pdf-parse";
import { advancedPdfParser, parsePdfWithAdvancedRetries } from "./advancedPdfParser";
import * as Sentry from "@sentry/node";
import dbConnection from "../db/dbConnection";
import {
  ATTEMPT,
  PDF_PARSING_MAX_ATTEMPTS,
  PASSWORD_REGEX,
  REDIS_KEYS,
  SOCKET_ROUTES,
} from "./constants";
import { getSecretKeys } from "../config/awsConfig";
import Cache from "../db/cache";
import InterviewServices from "../features/interview/services";
import ActivityLogModel from "../schema/s9-innerview/activity_logs";
import OrganizationModel from "../schema/s9/organization";

export const generateRandomPassword = () => {
  const lower = "abcdefghijklmnopqrstuvwxyz";
  const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const digits = "0123456789";
  const symbols = "!@#$%^&*()-_=+[]{}|;:,.<>?";
  const all = lower + upper + digits + symbols;

  const getRandom = (chars) => chars[Math.floor(Math.random() * chars.length)];

  // Start with one from each required group
  const password = [
    getRandom(lower),
    getRandom(upper),
    getRandom(digits),
    getRandom(symbols),
  ];

  // Fill the rest up to a random length between 8–16
  const targetLength = Math.floor(Math.random() * 9) + 8;
  while (password.length < targetLength) {
    password.push(getRandom(all));
  }

  // Proper shuffle (Fisher-Yates)
  // eslint-disable-next-line no-plusplus
  for (let i = password.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [password[i], password[j]] = [password[j], password[i]];
  }

  const final = password.join("");

  // Ensure it matches your regex
  return PASSWORD_REGEX.test(final) ? final : generateRandomPassword();
};

export async function getRepositories<T extends Record<string, any>>(
  modelMap: T
): Promise<{ [K in keyof T]: Repository<InstanceType<T[K]>> }> {
  const entries = await Promise.all(
    Object.entries(modelMap).map(async ([key, model]) => [
      key,
      await dbConnection.getS9InnerViewDatabaseRepository(model),
    ])
  );
  return Object.fromEntries(entries) as {
    [K in keyof T]: Repository<InstanceType<T[K]>>;
  };
}

// Retry Mechanism for PDF parsing - Enhanced with advanced parsing
export const parsePdfWithRetries = async (
  buffer: Buffer,
  maxAttempts = PDF_PARSING_MAX_ATTEMPTS,
  attempt = ATTEMPT
): Promise<{ text: string } | null> => {
  try {
    // First try the advanced parser which handles template resumes and OCR
    const advancedResult = await parsePdfWithAdvancedRetries(buffer, maxAttempts);
    if (advancedResult && advancedResult.text.trim() !== "") {
      return advancedResult;
    }

    // Fallback to basic pdf-parse if advanced parser fails
    const pdfParsed = await pdf(buffer);
    if (pdfParsed.text.trim() === "") {
      return null;
    }
    return pdfParsed;
  } catch (error) {
    console.log("Error parsing PDF =========>", error);
    handleSentryError(error, "parsePdfWithRetries");

    if (attempt + 1 < maxAttempts) {
      return parsePdfWithRetries(buffer, maxAttempts, attempt + 1); // Retry
    }
  }

  // All attempts failed
  return null;
};

// New function for advanced PDF parsing with image extraction
export const parsePdfAdvanced = async (
  buffer: Buffer,
  options: {
    extractImages?: boolean;
    useOcr?: boolean;
    ocrLanguage?: string;
  } = {}
) => {
  try {
    return await advancedPdfParser.parsePdf(buffer, {
      extractImages: options.extractImages || false,
      useOcr: options.useOcr !== false, // Default to true
      ocrLanguage: options.ocrLanguage || 'eng',
      maxRetries: PDF_PARSING_MAX_ATTEMPTS
    });
  } catch (error) {
    handleSentryError(error, "parsePdfAdvanced");
    throw error;
  }
};

export const setUpDeepgram = async (cache: Cache, interviewId: number) => {
  const keys = await getSecretKeys();
  let keepAlive: ReturnType<typeof setInterval> | null = null;
  let connection: ListenLiveClient | null = null;

  console.log("inside setUpDeepgram");

  // Cleanup function
  const cleanup = () => {
    console.log("Cleaning up Deepgram connection...");
    if (keepAlive) {
      console.log("inside keepAlive check");
      clearInterval(keepAlive);
      keepAlive = null;
    }
    if (connection) {
      console.log("inside connection check");
      try {
        connection.off(LiveTranscriptionEvents.Open, () => {});
        connection.off(LiveTranscriptionEvents.Close, () => {});
        connection.off(LiveTranscriptionEvents.Error, () => {});
        connection.off(LiveTranscriptionEvents.Transcript, () => {});
        connection.requestClose();
      } catch (e) {
        console.error("Error during cleanup:", e);
        handleSentryError(e, "DeepgramCleanup");
      } finally {
        connection = null;
        console.log("inside finally=====>>>");
      }
    }
  };

  try {
    // STEP 1: Create a Deepgram client using the API key
    const deepgram = createClient(keys.deepgram_secret_key);

    // STEP 2: Create a live transcription connection
    connection = deepgram.listen.live({
      model: "nova-3",
      language: "en-US",
      smart_format: true,
      interim_results: true,
      punctuate: true,
      diarize: true,
      alive: true,
    });

    // Setup keep alive
    keepAlive = setInterval(() => {
      if (connection?.getReadyState() === 1) {
        connection?.keepAlive();
        console.log("Deepgram connection keep alive");
      }
    }, 8000);

    // STEP 3: Set up event handlers
    connection.on(LiveTranscriptionEvents.Open, async () => {
      console.log("Deepgram connection opened");
    });

    connection.on(LiveTranscriptionEvents.Close, () => {
      console.log("Deepgram connection closed");
      // cleanup();
    });

    connection.on(LiveTranscriptionEvents.Error, (err) => {
      console.log("Deepgram connection error", err);
      // cleanup();
    });

    connection.on(LiveTranscriptionEvents.Metadata, (data) => {
      console.log("Metadata:", data);
    });

    connection.on(LiveTranscriptionEvents.Transcript, async (data) => {
      const { transcript } = data.channel.alternatives[0];
      console.log("Transcript received:", transcript);

      console.log("is_final======", data.is_final);

      if (transcript && data.is_final) {
        try {
          // Get current transcript
          const currentFullTranscript =
            (await cache.get(
              `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
            )) || "";

          // Append new transcript with spacing/punctuation
          const updatedTranscript = currentFullTranscript
            ? `${currentFullTranscript} ${transcript}`
            : transcript;

          console.log("updatedTranscript", updatedTranscript);

          // Save transcript to Redis if interviewId is available
          if (interviewId) {
            console.log(
              `Saving transcript for interview in deepgram: ${interviewId}`
            );
            await cache.set(
              `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`,
              updatedTranscript
            );
          }
        } catch (e) {
          console.error("Error processing transcript:", e);
          handleSentryError(e, "processTranscript");
        }
      }
    });

    // Return cleanup function along with connection
    return {
      connection,
      cleanup,
    };
  } catch (error) {
    console.error("Failed to set up Deepgram:", error);
    handleSentryError(error, "setUpDeepgram");
    // cleanup();
    return {
      connection,
      cleanup,
    };
  }
};

/**
 * Socket.io Server Manager Class
 * Handles socket.io server initialization and event handling
 */
export class SocketIOManager {
  private io: Server;

  private candidateIo: Server;

  private cache: Cache;

  private lastProcessedQuestionTime: number;

  private lastProcessedSkillTime: number;

  private deepgramConnection: ListenLiveClient;

  private interviewerId: string;

  private candidateId: string;

  private deepgramCleanup: () => void;

  /**
   * Initialize Socket.io Server Manager
   * @param httpServer - HTTP Server to attach Socket.io to
   * @param cache - Cache instance for storing transcripts
   */
  constructor(
    httpServer: http.Server<
      typeof http.IncomingMessage,
      typeof http.ServerResponse
    >
  ) {
    this.cache = new Cache();
    this.lastProcessedQuestionTime = 0;
    this.lastProcessedSkillTime = 0;
    this.deepgramConnection = null;
    this.interviewerId = null;
    this.candidateId = null;

    // Initialize Socket.io server
    this.io = new Server(httpServer, {
      path: SOCKET_ROUTES.CONDUCT_INTERVIEW,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.candidateIo = new Server(httpServer, {
      path: SOCKET_ROUTES.CANDIDATE_CONDUCT_INTERVIEW,
      cors: {
        origin: "*",
        methods: ["GET", "POST"],
      },
    });

    this.setupEventHandlers();
  }

  /**
   * Setup Socket.io event handlers
   */
  private async setupEventHandlers(): Promise<void> {
    this.io.on("connection", async (socket: Socket) => {
      console.log("New user connected : interviewer", socket.connected);

      const interviewId = +socket.handshake.query.interviewId;
      if (interviewId) {
        const { connection, cleanup } = await setUpDeepgram(
          this.cache,
          interviewId
        );
        this.deepgramConnection = connection;
        this.deepgramCleanup = cleanup;
      }

      // Handle disconnect event
      socket.on("disconnect", (reason) => {
        this.handleDisconnect(socket, reason);
      });

      // Handle message event
      socket.on("message", async (payload, callback) => {
        console.log("payload interviewer", payload);
        this.handleMessage(socket, payload, callback);
      });

      socket.on("addFollowUpSkill", async (payload, callback) => {
        console.log("payload addFollowUpSkill", payload);
        const response = await InterviewServices.addFollowUpSkill(payload);

        console.log("response addFollowUpSkill", response);

        if (response?.success) {
          const followUpSkillGenerationKey =
            REDIS_KEYS.MAX_FOLLOW_UP_SKILL_GENERATION_LIMIT.replace(
              "{interviewId}",
              String(payload.interviewId)
            );
          const skillGenerationCount = await this.cache.get(
            `${followUpSkillGenerationKey}`
          );
          // update skill generation count
          const newCount = skillGenerationCount
            ? Number(skillGenerationCount) + 1
            : 1;
          await this.cache.set(
            `${followUpSkillGenerationKey}`,
            String(newCount),
            60 * 60 * 24
          );
        }

        callback({ ...response });
      });
    });

    this.candidateIo.on("connection", async (socket: Socket) => {
      console.log("New user connected : candidate", socket.connected);

      // Handle disconnect event
      socket.on("disconnect", async (reason) => {
        const candidateKey = REDIS_KEYS.INTERVIEW_CANDIDATE_KEY.replace(
          "{interviewId}",
          String(socket.handshake.query.interviewId)
        ).replace("{candidateId}", String(this.candidateId));
        await this.cache.del(`${candidateKey}`);

        console.log("candidateKey deleted", await this.cache.get(candidateKey));

        console.log("Disconnected : candidate");
        console.log("Reason: candidate", reason);
        this.candidateId = null;
      });

      // Handle message event
      socket.on("message", async (payload: { candidateId: string }) => {
        const { candidateId } = payload;
        this.candidateId = candidateId;

        console.log("payload candidate", payload);
        if (socket.connected && payload) {
          console.log("candidateId", candidateId);
          const candidateKey = REDIS_KEYS.INTERVIEW_CANDIDATE_KEY.replace(
            "{interviewId}",
            String(socket.handshake.query.interviewId)
          ).replace("{candidateId}", String(candidateId));
          await this.cache.set(`${candidateKey}`, candidateId, 30);
        }
      });
    });
  }

  /**
   * Handle socket disconnect event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param reason - Disconnect reason
   * @param details - Disconnect details
   */
  private async handleDisconnect(
    socket: Socket,
    reason: string
  ): Promise<void> {
    console.log("Disconnected... interviewer");
    console.log("Reason: interviewer", reason);

    if (this.deepgramCleanup) this.deepgramCleanup();

    const interviewId = +socket.handshake.query.interviewId;
    const interviewerKey = REDIS_KEYS.INTERVIEW_INTERVIEWER_KEY.replace(
      "{interviewId}",
      String(interviewId)
    ).replace("{interviewerId}", String(this.interviewerId));
    await this.cache.del(`${interviewerKey}`);

    console.log("interviewerKey======", interviewerKey);

    console.log("interviewerKey deleted", await this.cache.get(interviewerKey));

    const transcripts = await this.cache.get(
      `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`
    );

    if (interviewId) {
      // Save transcript with interviewId as key if available
      console.log(
        `Saving transcript for interview on disconnect: ${interviewId}`
      );
      this.cache.set(
        `${REDIS_KEYS.INTERVIEW_TRANSCRIPT_KEY}${interviewId}`,
        transcripts || ""
      );
    } else {
      // Fallback to socket.id if interviewId is not available
      console.log("No interviewId found, using socket.id as fallback");
      this.cache.set(socket.id, transcripts || "");
    }

    this.interviewerId = null;

    this.deepgramConnection?.requestClose();
    this.deepgramConnection?.removeAllListeners();
  }

  /**
   * Handle socket message event
   * @param socket - Socket instance
   * @param connection - Deepgram connection
   * @param payload - Message payload
   * @param callback - Message callback
   */
  private async handleMessage(
    socket: Socket,
    payload: any,
    callback: Function
  ): Promise<null> {
    try {
      let userId = null;
      try {
        console.log("Inside socket middleware");
        const keys = await getSecretKeys();
        const jwtToken = socket.handshake.headers.authorization;
        const token = jwtToken?.split(" ")[1];

        if (!jwtToken) {
          return callback({
            success: false,
            message: "token_req",
            code: 401,
          });
        }

        const verify = jwt.verify(token, keys.token_key) as jwt.JwtPayload;
        userId = verify?.id;

        if (!verify) {
          return callback({
            success: false,
            message: "invalid_token",
            code: 401,
          });
        }
      } catch (error) {
        if (error.name === "TokenExpiredError") {
          return callback({
            success: false,
            message: "token_expired",
            code: 401,
          });
        }
        console.error("socket middleware error:", error);
        handleSentryError(error, "socketMiddleware");
        return callback({
          success: false,
          message: "invalid_token",
          code: 401,
        });
      }
      console.log("user id=========>>>>>", userId);

      // Method to process time-based events
      const processTimeBasedEvents = async (currentTimeInSeconds: number) => {
        // Check if 5 minutes (300 seconds) have passed since last processing
        const fiveMinutesInSeconds = 300;
        // Check if 8 minutes (480 seconds) have passed since last processing
        const eightMinutesInSeconds = 480;

        const currentTimeBlockForQuestion = Math.floor(
          currentTimeInSeconds / fiveMinutesInSeconds
        );
        const lastTimeBlockForQuestion = Math.floor(
          this.lastProcessedQuestionTime / fiveMinutesInSeconds
        );

        const currentTimeBlockForSkill = Math.floor(
          currentTimeInSeconds / eightMinutesInSeconds
        );
        const lastTimeBlockForSkill = Math.floor(
          this.lastProcessedSkillTime / eightMinutesInSeconds
        );

        console.log("currentTimeBlockForQuestion", currentTimeBlockForQuestion);
        console.log("lastTimeBlockForQuestion", lastTimeBlockForQuestion);

        console.log("currentTimeBlockForSkill", currentTimeBlockForSkill);
        console.log("lastTimeBlockForSkill", lastTimeBlockForSkill);

        // If we've moved to a new 5-minute block
        if (currentTimeBlockForQuestion > lastTimeBlockForQuestion) {
          const interviewId = +socket.handshake.query.interviewId;
          const question = await InterviewServices.generateFollowUpQuestions({
            jobSkillId: payload.jobSkillId,
            type: payload.type,
            interviewId,
            jobId: payload.jobId,
          });
          console.log(
            `5-minute interval reached at ${currentTimeInSeconds} seconds`
          );
          // Update the last processed time
          this.lastProcessedQuestionTime = currentTimeInSeconds;
          console.log(typeof question, "question===>>>", question);
          return callback({
            success: true,
            data: JSON.parse(question).questionText,
            code: 200,
          });
        }

        // If we've moved to a new 8-minute block
        if (currentTimeBlockForSkill > lastTimeBlockForSkill) {
          const interviewId = +socket.handshake.query.interviewId;
          const followUpSkillGenerationKey =
            REDIS_KEYS.MAX_FOLLOW_UP_SKILL_GENERATION_LIMIT.replace(
              "{interviewId}",
              String(interviewId)
            );
          const skillGenerationCount = await this.cache.get(
            `${followUpSkillGenerationKey}`
          );
          console.log(
            typeof skillGenerationCount,
            "skillGenerationCount",
            skillGenerationCount
          );

          // do not generate skill if limit is exceeded
          if (skillGenerationCount && Number(skillGenerationCount) >= 3) {
            console.log("Skill generation limit exceeded");
            return null;
          }
          const jobApplicationId = +socket.handshake.query.jobApplicationId;
          console.log(
            socket.handshake.query.jobApplicationId,
            "jobApplicationId",
            jobApplicationId
          );
          const skillData = await InterviewServices.generateFollowUpSkill({
            type: payload.type,
            interviewId,
            jobApplicationId,
          });
          console.log(
            `8-minute interval reached at ${currentTimeInSeconds} seconds`
          );
          // Update the last processed time
          this.lastProcessedSkillTime = currentTimeInSeconds;
          console.log(typeof skillData, "skillData===>>>", skillData);
          const skill =
            typeof skillData === "string" && skillData?.length
              ? JSON.parse(skillData)
              : skillData;
          return callback({
            success: true,
            data: skill,
            code: 200,
          });
        }

        // Update the last processed time
        this.lastProcessedSkillTime = currentTimeInSeconds;
        this.lastProcessedQuestionTime = currentTimeInSeconds;
        return null;
      };
      console.log("payload interviewer", payload);

      if (socket.connected) {
        const { interviewId } = socket.handshake.query;
        this.interviewerId = String(userId);
        const interviewerKey = REDIS_KEYS.INTERVIEW_INTERVIEWER_KEY.replace(
          "{interviewId}",
          String(interviewId)
        ).replace("{interviewerId}", String(userId));
        await this.cache.set(`${interviewerKey}`, String(userId), 60);

        // process time calculation
        if (payload?.time) {
          processTimeBasedEvents(payload.time);
        }

        // setup deepgram connection only when payload is received
        if (payload?.blob) {
          if (this.deepgramConnection?.getReadyState() === 1) {
            this.deepgramConnection?.send(payload.blob);
            console.log("Sent audio data to Deepgram");
          } else if (this.deepgramConnection?.getReadyState() >= 2) {
            console.log("Deepgram connection lost, reconnecting...");
            this.deepgramConnection?.requestClose();
            this.deepgramConnection?.removeAllListeners();
            const { connection, cleanup } = await setUpDeepgram(
              this.cache,
              +interviewId
            );
            this.deepgramConnection = connection;
            this.deepgramCleanup = cleanup;
          }
        }
      }
      return null;
    } catch (error) {
      console.error("socket connection error:", error);
      handleSentryError(error, "socketMiddleware");
      return null;
    }
  }
}

/**
 * Add an entry to the activity_logs table
 * @param params - Activity log parameters
 */
export async function addActivityLog(params: {
  orgId: number;
  logType: string;
  userId: number;
  entityId: number;
  entityType?: string;
  oldValue?: string;
  newValue?: string;
  comments: string;
}) {
  try {
    const s9InnerviewDataSource = await dbConnection.getS9InnerviewDataSource();
    const activityLogRepo =
      s9InnerviewDataSource.getRepository(ActivityLogModel);
    const log = activityLogRepo.create({
      orgId: params.orgId ?? null,
      logType: params.logType,
      userId: params.userId ?? null,
      entityId: params.entityId ?? null,
      entityType: params.entityType ?? null,
      oldValue: params.oldValue ?? null,
      newValue: params.newValue ?? null,
      comments: params.comments ?? null,
    });
    await activityLogRepo.save(log);
    return true;
  } catch (error) {
    console.error("Error adding activity log:", error);
    handleSentryError(error, "addActivityLog");
    return false;
  }
}
export const stripHtmlTags = (html: string) => {
  // 1. Decode HTML entities like &lt; &gt; etc.
  const htmlDecoded = html
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&amp;/g, "&")
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/&nbsp;/g, " ");

  // 2. Remove HTML tags
  const plainText = htmlDecoded.replace(/<[^>]*>/g, "");

  // 3. Trim and clean up multiple newlines
  return plainText.replace(/\n\s*\n/g, "\n").trim();
};

/**
 * Centralized error handler to standardize error logging across the application.
 * This function handles logging to console and capturing in Sentry with appropriate context.
 * It does not return anything - logging only.
 *
 * @param error - The error object that was caught
 * @param functionName - The name of the function where the error occurred (for tracing)
 * @param context - Optional additional context data to help with debugging
 *
 * @example
 * // Usage in a catch block:
 * try {
 *   // Some code that might throw
 * } catch (error) {
 *   handleSentryError(error, 'sendVerificationEmail', { userId: user.id });
 *   return { error: 'Email sending failed' };
 * }
 */
export const handleSentryError = (
  error: unknown,
  functionName: string,
  context?: Record<string, any>
): void => {
  try {
    // Ensure we have an Error object
    const errorObject =
      error instanceof Error ? error : new Error(String(error));

    // Extract error details
    const errorMessage = errorObject.message || "Unknown error occurred";

    // Get the current environment
    const environment = process.env.NODE_ENV || "development";

    // Create a detailed message for console logging
    const detailedMessage = `Error in ${functionName}: ${errorMessage}`;

    // Log to console with appropriate formatting
    console.error("handleSentryError message >>>>>>", detailedMessage, error);
    console.error(
      "handleSentryError Complete Error in",
      functionName,
      "Error is :",
      error
    );

    // Capture in Sentry with function name and context
    Sentry.captureException(errorObject, {
      tags: {
        functionName,
        environment,
      },
      extra: {
        context,
        functionName,
        environment,
      },
    });
  } catch (e) {
    console.error("Error capturing Sentry exception:", e);
  }
};

// export const googleAPI = async (data: GoogleAPIParams): Promise<AxiosPromise> => {
//   const keys = await getSecretKeys();
//   const result: AxiosRequestConfig = {
//     url: data.url,
//     method: data.method,
//     headers: {
//       "User-Agent": "Super Agent/0.0.1",
//       "Content-Type": "application/x-www-form-urlencoded",
//     },
//     params: {
//       region: "",
//       key: keys.google_api_key,
//       ...data.params,
//     },
//   };
//   const response = await axios(result);
//   return response;
// };
export const getOrganizationLogo = async (orgId: number): Promise<string> => {
  try {
    const s9DataSource = await dbConnection.getS9DataSource();
    const organizationRepo = s9DataSource.getRepository(OrganizationModel);
    const organization = await organizationRepo.findOne({
      where: { id: orgId },
    });
    return organization?.logo;
  } catch (error) {
    console.error("Error fetching organization logo:", error);
    handleSentryError(error, "getOrgLogo");
    return "";
  }
};
export const getTruncatedName = (fullName: string) => {
  const parts = fullName.trim().split(/\s+/); // split on spaces, remove extra spaces

  if (parts.length === 1) {
    // only one word → return as is
    return parts[0];
  }

  if (parts.length >= 2) {
    // more than one word → FirstName + LastInitial
    return `${parts[0]} ${parts[1][0]}.`;
  }

  return ""; // fallback
};
