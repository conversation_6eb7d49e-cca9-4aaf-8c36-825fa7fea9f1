import { handleSentryError } from "./helper";
import {
  IQuestion,
  QuestionFormat,
} from "../features/finalAssessment/interface";
import { makeOpenAIRequest } from "./openai";
import { GPT_MODEL_TURBO } from "./constants";

type SkillDataWithName = {
  skillId: number;
  skillName: string;
  shortDescription?: string;
  type?: string;
};

/**
 * Generates fallback questions for a specific skill when the main generation process fails
 * @param skill The skill to generate questions for
 * @param numQuestions Number of questions to generate
 * @param jobContext Optional job description context
 * @returns Array of generated questions
 */
export async function generateFallbackQuestions(
  skill: SkillDataWithName,
  numQuestions: number,
  jobContext?: string
): Promise<IQuestion[]> {
  console.log(
    `Generating ${numQuestions} fallback questions for skill ID ${skill.skillId} (${skill.skillName})`
  );

  try {
    const prompt = `Generate ${numQuestions} high-quality interview questions to assess a candidate's knowledge of "${skill.skillName}" skill.
    
    ${skill.shortDescription ? `The skill is described as: "${skill.shortDescription}"` : ""}
    ${skill.type ? `This is a ${skill.type} skill.` : ""}
    ${jobContext ? `\n\nJob Context: ${jobContext}` : ""}
    
    IMPORTANT REQUIREMENTS:
    - Create scenario-based questions that test practical application of knowledge
    - Generate a mix of multiple-choice (MCQ) and true/false questions
    - For MCQs, ensure all options are technically plausible
    - All questions must reflect real workplace scenarios
    
    Format the response as a JSON object with a 'questions' array, where each question has:
    
    For MCQ questions:
    - 'type': "mcq"
    - 'question': the question text
    - 'options': array of option objects with 'id' (A, B, C, or D) and 'text' (the option text)
    - 'correctAnswerId': the ID of the correct option (A, B, C, or D)
    
    For true/false questions:
    - 'type': "true_false"
    - 'question': the question text
    - 'options': array of option objects with 'id' (true or false) and 'text' (the option text)
    - 'correctAnswer': boolean (true or false)`;

    const content = await makeOpenAIRequest(
      GPT_MODEL_TURBO,
      [
        {
          role: "system",
          content:
            "You are an expert interviewer who creates targeted questions to assess specific skills.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      {
        temperature: 0.7,
        maxTokens: 1000,
        responseFormat: { type: "json_object" },
      }
    );

    try {
      const parsedContent = JSON.parse(content);
      const questions = parsedContent.questions || [];

      // Validate the structure of each question
      const validQuestions = questions.filter((q: any) => {
        // Check common fields
        if (!q.question || !q.type) return false;

        // Validate based on question type
        if (q.type === QuestionFormat.MCQ) {
          return (
            Array.isArray(q.options) &&
            q.options.length === 4 &&
            q.correctAnswerId
          );
        }

        if (q.type === QuestionFormat.TRUE_FALSE) {
          return typeof q.correctAnswer === "boolean";
        }

        return false;
      });

      console.log(
        `Generated ${validQuestions.length} valid fallback questions for skill ID ${skill.skillId}`
      );
      return validQuestions.slice(0, numQuestions); // Ensure we only return the requested number
    } catch (parseError) {
      handleSentryError(parseError, "generateFallbackQuestions");
      console.error(
        `Failed to parse fallback questions for skill ${skill.skillId}: ${parseError.message}`
      );
      return generateDefaultQuestions(skill, numQuestions);
    }
  } catch (error) {
    handleSentryError(error, "generateFallbackQuestions");
    console.error(
      `Failed to generate fallback questions for skill ${skill.skillId}: ${error.message}`
    );
    return generateDefaultQuestions(skill, numQuestions);
  }
}

/**
 * Generates default questions as a last resort when all other methods fail
 * @param skill The skill to generate questions for
 * @param numQuestions Number of questions to generate
 * @returns Array of default questions
 */
function generateDefaultQuestions(
  skill: SkillDataWithName,
  numQuestions: number
): IQuestion[] {
  console.log(
    `Generating ${numQuestions} DEFAULT questions for skill ID ${skill.skillId} (${skill.skillName})`
  );

  const questions: IQuestion[] = [];

  // Generate required number of questions
  for (let i = 0; i < numQuestions; i += 1) {
    if (i % 2 === 0) {
      // Generate MCQ question
      questions.push({
        type: "mcq" as const,
        question: `In a project scenario, what is the best approach to implement ${skill.skillName}?`,
        options: [
          {
            id: "A",
            text: `Apply ${skill.skillName} principles directly to the problem`,
          },
          {
            id: "B",
            text: `Research best practices for ${skill.skillName} before implementation`,
          },
          {
            id: "C",
            text: `Consult with team members who have experience with ${skill.skillName}`,
          },
          {
            id: "D",
            text: `Start with a small prototype to test ${skill.skillName} concepts`,
          },
        ],
        correctAnswerId: "B",
      });
    } else {
      // Generate true/false question
      questions.push({
        type: "true_false" as const,
        question: `When working with ${skill.skillName}, it's best practice to document your approach before implementation.`,
        options: [
          { id: "true", text: "True" },
          { id: "false", text: "False" },
        ],
        correctAnswer: true,
      });
    }
  }

  return questions;
}

/**
 * Process a batch of skills to generate questions
 * @param skills Batch of skills to process
 * @param jobDescription Optional job description
 * @param interviewSummary Optional interview summary
 * @param numQuestionsPerSkill Number of questions per skill
 * @returns Record of skill IDs to questions
 */
export async function processSkillBatch(
  skills: SkillDataWithName[],
  jobDescription?: string,
  interviewSummary?: string,
  numQuestionsPerSkill: number = 2
): Promise<Record<number, IQuestion[]>> {
  const skillsPrompt = skills
    .map(
      (
        skill,
        index
      ) => `${index + 1}. Skill: "${skill.skillName}" (${skill.type || "Not specified"})
   Description: "${skill.shortDescription || "No description available"}"`
    )
    .join("\n");

  // Build context sections
  let contextSections = "";

  if (jobDescription) {
    contextSections += `\n\nJOB DESCRIPTION CONTEXT:
${jobDescription}`;
  }

  if (interviewSummary) {
    contextSections += `\n\nPREVIOUS INTERVIEW SUMMARY:
${interviewSummary}`;
  }

  const prompt = `You are an expert technical interviewer with 10+ years of experience in candidate assessment. Generate high-quality, targeted interview questions based on the job description, experience level, and candidate history.

  ### MANDATORY SKILL COVERAGE REQUIREMENT
  - Generate EXACTLY ${numQuestionsPerSkill} questions for EVERY skill in the provided list
  - DO NOT skip any skill IDs, regardless of perceived relevance to the job description
  - If insufficient information is available for a specific skill, create reasonable questions based SOLELY on the skill name
  - Total questions MUST equal (${skills.length} skills × ${numQuestionsPerSkill} = ${skills.length * numQuestionsPerSkill} questions)
  - CRITICAL: Ensure ALL JSON STRINGS ARE PROPERLY TERMINATED with closing quotes
  
  ### QUALITY STANDARDS
  - **NO DIRECT KNOWLEDGE QUESTIONS**: All questions must be scenario-based
  - **PLAUSIBLE DISTRRACTORS**: For MCQs, ensure all options are technically plausible
  - **REAL-WORLD CONTEXT**: All questions must reflect actual workplace scenarios
  - **NO SKILL OMISSION**: Generate questions for EVERY skill in the provided list
  - **NO REPETITION**: Check against interview history to avoid previously asked questions
  
  ### SKILLS TO GENERATE QUESTIONS FOR:
  ${skillsPrompt}${contextSections}
  
  ### OUTPUT FORMAT INSTRUCTIONS
  - Format the response as a JSON object with a 'skillQuestions' object
  - Each key is the skill number (1, 2, 3, etc.) and the value is an array of EXACTLY ${numQuestionsPerSkill} questions
  - Maintain the EXACT structure shown below with no additional fields, no missing fields, and no modifications to field names
  - CRITICAL: ENSURE ALL STRINGS ARE PROPERLY TERMINATED WITH CLOSING QUOTES
  
  ### QUESTION STRUCTURE (MUST MATCH EXACTLY)
  
  For MCQ questions:
  - 'type': "mcq"
  - 'question': the question text (must include realistic scenario context)
  - 'options': array of option objects with 'id' (A, B, C, or D) and 'text' (the option text)
  - 'correctAnswerId': the ID of the correct option (A, B, C, or D)
  
  For true/false questions:
  - 'type': "true_false"
  - 'question': the question text (must include realistic scenario context)
  - 'options': array of option objects with 'id' (true or false) and 'text' (the option text)
  - 'correctAnswer': boolean (true or false)
  
  ### CRITICAL RULES
  - NEVER skip any skill IDs (generate questions for ALL skills in the batch)
  - NEVER generate fewer than ${numQuestionsPerSkill} questions for ANY skill
  - NEVER include direct knowledge questions (all questions must be scenario-based)
  - ALWAYS ensure ALL JSON STRINGS ARE PROPERLY TERMINATED WITH CLOSING QUOTES
  - BEFORE FINALIZING YOUR RESPONSE, VALIDATE THE JSON STRUCTURE FOR PROPER STRING TERMINATION`;

  try {
    // Use the common helper method for OpenAI API calls with retry logic
    const content = await makeOpenAIRequest(
      GPT_MODEL_TURBO,
      [
        {
          role: "system",
          content:
            "You are an expert interviewer who creates targeted questions to assess specific skills.",
        },
        {
          role: "user",
          content: prompt,
        },
      ],
      {
        temperature: 0.7,
        maxTokens: 4000, // Increased for multiple skills
        responseFormat: { type: "json_object" },
      }
    );

    console.log(`Received response for batch of ${skills.length} skills`);

    try {
      const parsedContent = JSON.parse(content);
      const skillQuestions = parsedContent.skillQuestions || {};
      const result: Record<number, IQuestion[]> = {};

      // Map the numbered responses back to skillIds
      skills.forEach((skill, index) => {
        const skillNumber = (index + 1).toString();
        const questions = skillQuestions[skillNumber] || [];

        console.log(
          `Processing ${questions.length} questions for skill ID ${skill.skillId} (${skill.skillName})`
        );

        // Validate and filter questions
        const validQuestions = questions.filter((q: any) => {
          // Check common fields
          if (!q.question || !q.type) {
            console.log(
              `Invalid question for skill ${skill.skillId}: missing question or type`
            );
            return false;
          }

          // Validate based on question type
          if (q.type === QuestionFormat.MCQ) {
            const valid =
              Array.isArray(q.options) &&
              q.options.length === 4 &&
              q.correctAnswerId;
            if (!valid) console.log(`Invalid MCQ for skill ${skill.skillId}`);
            return valid;
          }

          if (q.type === QuestionFormat.TRUE_FALSE) {
            const valid = typeof q.correctAnswer === "boolean";
            if (!valid)
              console.log(`Invalid TRUE/FALSE for skill ${skill.skillId}`);
            return valid;
          }

          console.log(
            `Unknown question type for skill ${skill.skillId}: ${q.type}`
          );
          return false;
        });

        // Log any validation issues
        if (validQuestions.length < numQuestionsPerSkill) {
          console.log(
            `WARNING: Skill ${skill.skillId} (${skill.skillName}) has only ${validQuestions.length}/${numQuestionsPerSkill} valid questions`
          );
        }

        result[skill.skillId] = validQuestions;
      });

      return result;
    } catch (parseError) {
      handleSentryError(parseError, "processSkillBatch");
      console.error(`Error parsing OpenAI response: ${parseError.message}`);
      return {};
    }
  } catch (error) {
    handleSentryError(error, "processSkillBatch");
    console.error(`Error in OpenAI request: ${error.message}`);
    return {};
  }
}
