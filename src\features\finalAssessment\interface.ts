import { QuestionType } from "../../utils/constants";

/**
 * Interface for Final Assessment data
 */
export interface IFinalAssessmentData {
  jobId: number;
  jobApplicationId: number;
  orgId: number;
  overallSuccessProbability?: number;
  skillSummary?: object;
  developmentRecommendations?: object;
  isAssessmentSubmitted?: boolean;
  isAssessmentShared?: boolean;
  // questions field removed as they will be auto-generated
}

/**
 * Interface for Final Assessment Question data
 */
export interface IFinalAssessmentQuestion {
  question: string;
  questionType: QuestionType;
  skillId?: number | null;
  options?: object | null;
  correctAnswer?: string | null;
  applicantAnswer?: string | null;
  finalAssessmentId?: number;
}

/**
 * Interface for Generate Questions Based On Skill Type request
 */
export interface IGenerateQuestionsRequest {
  jobId: number;
  jobApplicationId: number;
}

/**
 * Interface for Skill Info with Type and Description
 */
export interface ISkillInfo {
  id: number;
  skillId: number;
  type: string;
  title: string;
  shortDescription: string;
}

/**
 * Question format types as string literals
 */
export type QuestionFormatType = "mcq" | "true_false";

/**
 * Question format type constants
 */
export const QuestionFormat = {
  MCQ: "mcq" as QuestionFormatType,
  TRUE_FALSE: "true_false" as QuestionFormatType,
};

/**
 * Interface for MCQ Option
 */
export interface IMCQOption {
  id: string;
  text: string;
}

/**
 * Interface for MCQ Question
 */
export interface IMCQQuestion {
  type: "mcq";
  question: string;
  options: IMCQOption[];
  correctAnswerId: string;
}

/**
 * Interface for True/False Option
 */
export interface ITrueFalseOption {
  id: string;
  text: string;
}

/**
 * Interface for True/False Question
 */
export interface ITrueFalseQuestion {
  type: "true_false";
  question: string;
  options: ITrueFalseOption[];
  correctAnswer: boolean;
}

/**
 * Union type for all question formats
 */
export type IQuestion = IMCQQuestion | ITrueFalseQuestion;

/**
 * Interface for Generated Question
 */
export interface IGeneratedQuestion {
  skillId: number;
  skillTitle: string;
  skillType: string;
  questions: IQuestion[];
}

/**
 * Interface for Generated Questions by Type
 */
export interface IGeneratedQuestionsByType {
  [key: string]: IGeneratedQuestion[];
}

/**
 * Interface for Manual Assessment Question Creation Request
 */
export interface ICreateManualQuestionRequest {
  finalAssessmentId: number;
  question: string;
  questionType: string;
  skillId: number;
  options: {
    options: Array<{
      id: string;
      text: string;
    }>;
  };
  correctAnswer: string;
}

export default IFinalAssessmentData;
