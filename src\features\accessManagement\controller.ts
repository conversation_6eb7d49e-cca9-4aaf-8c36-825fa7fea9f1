import { Request, Response } from "express";
import AccessManagementServices from "./services";
import { handleSentryError } from "../../utils/helper";
/**
 * Get list of user roles
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getCommonUserRoles = async (req: Request, res: Response) => {
  try {
    const data = await AccessManagementServices.getCommonUserRoles(req.orgId);

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getCommonUserRoles");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get list of user roles
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getUserRolesPagination = async (req: Request, res: Response) => {
  try {
    const query = {
      ...req.query,
    };
    const data = await AccessManagementServices.getUserRolesPagination(
      query,
      req.orgId
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getUserRolesPagination");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add a new user role
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addUserRole = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const data = await AccessManagementServices.addUserRole({
      ...body,
      organizationId: req.orgId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "addUserRole");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update an existing user role
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateUserRole = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };
    const { roleId } = req.params;

    const data = await AccessManagementServices.updateUserRole(roleId, {
      ...body,
      organizationId: req.orgId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateUserRole");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Delete a user role
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const deleteUserRole = async (req: Request, res: Response) => {
  try {
    const { roleId } = req.params;

    const data = await AccessManagementServices.deleteUserRole(
      roleId,
      req.orgId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "deleteUserRole");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get role permissions
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getRolePermissions = async (req: Request, res: Response) => {
  try {
    const queryParams = {
      ...req.query,
    };
    const data = await AccessManagementServices.getRolePermissions({
      ...queryParams,
      orgId: req.orgId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getRolePermissions");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update role permissions
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const updateRolePermissions = async (req: Request, res: Response) => {
  try {
    const { permissionIds } = req.body;
    const { roleId } = req.params;

    const data = await AccessManagementServices.updateRolePermissions(+roleId, {
      permissionIds,
      organizationId: req.orgId,
      userId: req.userId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateRolePermissions");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get permissions for a specific role by ID
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const getRolePermissionsByRoleId = async (
  req: Request,
  res: Response
) => {
  try {
    const { roleId } = req.params;

    const data = await AccessManagementServices.getRolePermissionsByRoleId(
      +roleId,
      req.orgId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getRolePermissionsByRoleId");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add role department and permission for admin
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const addRoleDepartmentAndPermissionForAdmin = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data =
      await AccessManagementServices.addRoleDepartmentAndPermissionForAdmin(
        body,
        req.userId
      );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "addRoleDepartmentAndPermissionForAdmin");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get User Permissions.
 * Retrieves user permissions based on access token
 *
 * @param req - The HTTP request object with user token in authorization header
 * @param res - The HTTP response object
 */
export const getUserPermissions = async (req: Request, res: Response) => {
  try {
    // The user ID is already extracted from the token in the auth middleware
    const { roleId } = req;

    // Get user permissions from service
    const data = await AccessManagementServices.getUserPermissions(
      roleId,
      req.query?.forceFetch === "true"
    );

    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getUserPermissions");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
