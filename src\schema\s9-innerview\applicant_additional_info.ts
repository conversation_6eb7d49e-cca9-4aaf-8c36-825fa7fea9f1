import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
} from "typeorm";

type ImageData = {
  urls: string[];
};

@Entity("applicant_additional_info")
export default class ApplicantAdditionalInfoModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    length: 255,
    nullable: false,
    name: "description",
  })
  description: string;

  @Column({
    name: "job_application_id",
    nullable: false,
  })
  jobApplicationId: number;

  @Column({
    type: "json",
    nullable: true,
    name: "images",
  })
  images: ImageData;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}
