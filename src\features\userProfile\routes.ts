import { getMyProfile, updateMyProfile } from "./controller";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import employeeManagementRoutes from "../employeeManagement/routes";
import { schemaValidation } from "../../middleware/validateSchema";
import updateUserProfileValidation from "./validations";

const userProfileRoutes = employeeManagementRoutes;

/**
 * @swagger
 * /user-profile/get-my-profile:
 *   get:
 *     summary: Get my profile information
 *     description: Retrieves the current user's profile details including image, first name, and last name
 *     tags:
 *       - User Profile
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the request was successful
 *                 message:
 *                   type: string
 *                   example: User profile fetched successfully
 *                   description: Status message
 *                 data:
 *                   type: object
 *                   properties:
 *                     image:
 *                       type: string
 *                       description: User profile image URL
 *                     firstName:
 *                       type: string
 *                       description: User's first name
 *                     lastName:
 *                       type: string
 *                       description: User's last name
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Authentication required"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: User profile not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User profile not found"
 *                 code:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
userProfileRoutes.get(ROUTES.USER_PROFILE.GET_MY_PROFILE, auth, getMyProfile);

/**
 * @swagger
 * /user-profile/update-my-profile:
 *   put:
 *     summary: Update my profile information
 *     description: Updates user profile details including first name, last name, and profile image
 *     tags:
 *       - User Profile
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - firstName
 *               - lastName
 *             properties:
 *               firstName:
 *                 type: string
 *                 description: User's first name
 *                 example: John
 *               lastName:
 *                 type: string
 *                 description: User's last name
 *                 example: Doe
 *               image:
 *                 type: string
 *                 nullable: true
 *                 description: User's profile image (base64 encoded or URL)
 *                 example: null
 *     responses:
 *       200:
 *         description: User profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "user_profile_updated"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid input data"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User not authenticated"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
userProfileRoutes.put(
  ROUTES.USER_PROFILE.UPDATE_MY_PROFILE,
  auth,
  schemaValidation(updateUserProfileValidation),
  updateMyProfile
);

export default userProfileRoutes;
