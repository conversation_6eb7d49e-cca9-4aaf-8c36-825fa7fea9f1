import envConfig from "../config/envConfig";
import { S9_INNERVIEW_ROUTES, STRATUM9_LOGO_URL, USER_TYPE } from "./constants";
import sendMail from "./sendgrid";

const config = envConfig();

const signupEmailTemplate = (data: {
  email: string;
  name: string;
  type?: string;
  password?: string;
}) => {
  const { email, type, password, name } = data;
  try {
    const subject =
      type === USER_TYPE.new
        ? "Account Activation - S9 InnerView"
        : "Your Access Credentials for S9 InnerView";

    const existingUserTemplate = `
        <!DOCTYPE html>
        <html lang="en">

        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Access Credentials for S9 InnerView</title>
        </head>

        <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
            <!-- Main Container -->
            <table width="100%" border="0" cellspacing="0" cellpadding="0"
                style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
                <!-- Logo Section -->
                <tr>
                    <td align="center" style="padding:20px 0 0; background-color: #fff;">
                     <img src="${STRATUM9_LOGO_URL}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                
                    </td>
                </tr>

                <!-- Heading Section -->
                <tr>
                    <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                        Your Access Credentials for S9 InnerView
                    </td>
                </tr>

                <!-- Email Content -->
                <tr>
                    <td style="padding: 30px;">
                        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                                style="color: #3182ce; font-weight: 600;">${name}</span>,</p>

                        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                            We hope this message finds you well.
                        </p>

                        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                            As part of our ongoing efforts to ensure seamless access to our internal systems, we are sharing a
                            gentle reminder of your login credentials for the S9 InnerView platform.
                        </p>

                        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                            Please find your access details below:
                        </p>

                        <table width="100%" border="0" cellspacing="0" cellpadding="0"
                            style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                            <tr>
                                <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                    <strong>Platform:</strong> S9 InnerView<br>
                                    <strong>Email:</strong> ${email}<br>
                                    <strong>Password:</strong> Use Existing Password <br>
                                    <strong>Login URL:</strong> <a href=${`${config.s9_innerview_website_url}/${S9_INNERVIEW_ROUTES.LOGIN}`}
                                        style="color: #3182ce; text-decoration: none;">${config.s9_innerview_website_url}/${S9_INNERVIEW_ROUTES.LOGIN}</a>
                                </td>
                            </tr>
                        </table>

                        <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                            Kindly ensure that these credentials are stored securely and not shared. If you experience any
                            difficulties accessing the platform, please don't hesitate to reach out to us.
                        </p>

                        <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                            Thank you for your continued collaboration and commitment.
                        </p>

                        <p style="margin: 20px 0 0 0; font-size: 16px; line-height: 1.5;">
                            Warm regards,<br>
                            <strong>STRATUM 9 Team</strong>
                        </p>
                    </td>
                </tr>

                <!-- Footer -->
                <tr>
                    <td
                        style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                        © ${new Date().getFullYear()} STRATUM 9. All rights reserved.
                    </td>
                </tr>
            </table>
        </body>

        </html>
    `;

    const newUserTemplate = `
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Account Activation - S9 InnerView</title>
    </head>

    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
        <!-- Main Container -->
        <table width="100%" border="0" cellspacing="0" cellpadding="0"
            style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
            <!-- Logo Section -->
            <tr>
                <td align="center" style="padding:20px 0 0; background-color: #fff;">
                           <img src="${STRATUM9_LOGO_URL}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
                

                </td>
            </tr>

            <!-- Heading Section -->
            <tr>
                <td style="padding: 20px 30px 10px 30px; font-size: 24px; font-weight: bold; color: #333;" align="center">
                    Welcome to S9 InnerView – Your Account Is Ready
                </td>
            </tr>

            <!-- Email Content -->
            <tr>
                <td style="padding: 30px;">
                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                            style="color: #3182ce; font-weight: 600;">${name}</span>,</p>

                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        We're excited to welcome you to S9 InnerView, your dedicated workspace for streamlined hiring,
                        high-performance interviews, and candidate insights—all powered by the STRATUM 9 platform.
                    </p>

                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        Your user account has been successfully set up. Please find your login credentials below:
                    </p>

                    <table width="100%" border="0" cellspacing="0" cellpadding="0"
                        style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                        <tr>
                            <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                                <strong>Platform:</strong> S9 InnerView<br>
                                <strong>Email:</strong>  ${email}<br>
                                <strong>Temporary Password:</strong> ${password}<br>
                                <strong>Login URL:</strong> <a href=${`${config.s9_innerview_website_url}/${S9_INNERVIEW_ROUTES.LOGIN}`}
                                    style="color: #3182ce; text-decoration: none;">${config.s9_innerview_website_url}/${S9_INNERVIEW_ROUTES.LOGIN}</a>
                            </td>
                        </tr>
                    </table>

                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        <span style="font-weight: bold;">🔐 Security Tip:</span> Kindly ensure to change the password.
                        Please choose a strong and secure password to protect your account.
                    </p>

                    <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                        We're thrilled to have you onboard and look forward to your contributions. Should you require any
                        support or onboarding assistance, feel free to reach out to our team.
                    </p>

                    <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                        Best regards,<br>
                        <strong>STRATUM 9 Team</strong>
                    </p>
                </td>
            </tr>

            <!-- Footer -->
            <tr>
                <td
                    style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                    © ${new Date().getFullYear()} STRATUM 9. All rights reserved.
                </td>
            </tr>
        </table>
    </body>

    </html>`;

    const htmlContent =
      type === USER_TYPE.new ? newUserTemplate : existingUserTemplate;

    const response = sendMail({
      email: email.toLowerCase(),
      subject,
      textContent: subject,
      htmlContent,
    });

    return response;
  } catch (error) {
    console.log("Error in hiringEmailTemplate: ", error);
    return error;
  }
};

export default signupEmailTemplate;
