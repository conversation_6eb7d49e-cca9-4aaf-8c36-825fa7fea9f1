import { Entity, PrimaryGeneratedColumn, Column } from "typeorm";

export interface IOrganizationBranches {
  id: number;
  orgId: number;
  branchName: string;
  branchCode: string;
  createdTs: Date;
  updatedTs: Date;
}

@Entity("organization_branches")
class OrganizationBranchesModel implements IOrganizationBranches {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    name: "branch_name",
    length: 255,
  })
  branchName: string;

  @Column({
    name: "branch_code",
    length: 255,
  })
  branchCode: string;

  @Column({
    name: "org_id",
    nullable: false,
  })
  orgId: number;

  @Column({
    type: "timestamp",
    name: "created_ts",
    default: () => "CURRENT_TIMESTAMP",
  })
  createdTs: Date;

  @Column({
    type: "timestamp",
    name: "updated_ts",
    default: () => "CURRENT_TIMESTAMP",
  })
  updatedTs: Date;
}

export default OrganizationBranchesModel;
