import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  generatePresignedUrlValidation,
  removeAttachmentsFromS3Validation,
} from "./vaildation";
import { schemaValidation } from "../../middleware/validateSchema";
import { generatePresignedUrl, removeAttachmentsFromS3 } from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";

const commonRoutes = express.Router();

/**
 * @swagger
 * components:
 *   schemas:
 *     PresignedUrlRequest:
 *       type: object
 *       properties:
 *         filePath:
 *           type: string
 *           description: Optional path where file will be stored in S3
 *           example: "job-interviews/resume.pdf"
 *         fileFormat:
 *           type: string
 *           description: Optional file format/extension
 *           example: "application/pdf"
 *     PresignedUrlResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *         message:
 *           type: string
 *           example: "presigned_url_generated"
 *         data:
 *           type: string
 *           description: Generated pre-signed URL for file upload
 *           example: "https://s9-interview-assets.s3.us-east-1.amazonaws.com/job-interviews/resume.pdf?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=AKIAYRRL4JV2YVYJAPGW%2F20250918%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250918T111623Z&X-Amz-Expires=360000&X-Amz-Signature=e3d1a9bd096eee5dfa4fc3d6c7440098eb1285a7b1965f30b4f3d40816bcdbd5&X-Amz-SignedHeaders=host&x-amz-checksum-crc32=AAAAAA%3D%3D&x-amz-sdk-checksum-algorithm=CRC32&x-id=PutObject"
 *         code:
 *           type: integer
 *           example: 200
 *     RemoveAttachmentsRequest:
 *       type: object
 *       required:
 *         - fileUrlArray
 *       properties:
 *         fileUrlArray:
 *           type: string
 *           description: JSON string containing array of file URLs/keys to delete
 *           example: "[\"uploads/profile/file1.jpg\", \"uploads/profile/file2.jpg\"]"
 */

/**
 * @swagger
 * /generate-presignedurl:
 *   post:
 *     summary: Generate pre-signed URL for S3 file upload
 *     description: Generates a pre-signed URL to allow secure file uploads to S3
 *     tags: [Common]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PresignedUrlRequest'
 *     responses:
 *       200:
 *         description: Pre-signed URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/PresignedUrlResponse'
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
commonRoutes.post(
  ROUTES.COMMON.GENERATE_PRE_SIGNED_URL,
  // auth,
  schemaValidation(generatePresignedUrlValidation),
  HandleErrors(generatePresignedUrl)
);

/**
 * @swagger
 * /remove-attachments-from-s3:
 *   post:
 *     summary: Remove files from S3
 *     description: Deletes files from S3 storage by their keys
 *     tags: [Common]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RemoveAttachmentsRequest'
 *     responses:
 *       200:
 *         description: Files removed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the operation was successful
 *                 message:
 *                   type: string
 *                   example: "success"
 *                   description: Response message
 *                 code:
 *                   type: integer
 *                   example: 200
 *                   description: HTTP status code
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
commonRoutes.post(
  ROUTES.COMMON.REMOVE_ATTACHMENTS_FROM_S3,
  auth,
  schemaValidation(removeAttachmentsFromS3Validation),
  HandleErrors(removeAttachmentsFromS3)
);

export default commonRoutes;
