import Joi from "joi";
import {
  ManualCandidateUploadRequest,
  CandidateData,
  GetAllPendingJobApplicationsRequest,
  ChangeApplicationStatusRequest,
} from "./interface";
import { DEFAULT_LIMIT } from "../../utils/constants";
import { Status } from "../../schema/s9-innerview/job_applications";
import { Gender } from "../../schema/s9-innerview/candidates";

const candidateDataSchema = Joi.object<CandidateData>({
  name: Joi.string().max(50).required().description("Name of the candidate"),

  email: Joi.string().required().description("Email of the candidate"),

  gender: Joi.string()
    .valid(...Object.values(Gender))
    .required()
    .description("Gender of the candidate"),

  additional_details: Joi.string()
    .optional()
    .allow("")
    .description("Additional details about the candidate"),

  resume_file: Joi.string().required().description("Link to the resume file"),

  assessment_file: Joi.string()
    .optional()
    .allow("")
    .description("Link to the assessment file"),
});

export const manualCandidateUploadSchema =
  Joi.object<ManualCandidateUploadRequest>({
    job_id: Joi.number()
      .required()
      .description("Job ID associated with the applications"),

    candidates: Joi.array()
      .items(candidateDataSchema)
      .min(1)
      .required()
      .description("Array of candidate data"),
  });

export const getAllPendingJobApplicationsSchema =
  Joi.object<GetAllPendingJobApplicationsRequest>({
    job_id: Joi.string()
      .required()
      .description("Job ID to filter applications"),

    limit: Joi.string()
      .required()
      .min(1)
      .max(DEFAULT_LIMIT)
      .default(DEFAULT_LIMIT)
      .description("Number of records to return per page"),

    offset: Joi.string()
      .required()
      .min(0)
      .default(0)
      .description("Number of records to skip for pagination"),

    status: Joi.string()
      .required()
      .valid("Pending", "All")
      .description("Filter applications by status"),
  });

export const changeApplicationStatusParamsSchema =
  Joi.object<ChangeApplicationStatusRequest>({
    job_id: Joi.number()
      .required()
      .description("Job ID associated with the application"),

    candidate_id: Joi.number()
      .required()
      .description("Candidate ID associated with the application"),

    hiring_manager_id: Joi.number()
      .required()
      .description("ID of the hiring manager changing the status"),

    status: Joi.string()
      .valid(Status.APPROVED, Status.REJECTED, Status.ON_HOLD)
      .required()
      .description("New status for the application"),
    hiring_manager_reason: Joi.string()
      .required()
      .max(300)
      .description("Reason for the status change"),
  });
