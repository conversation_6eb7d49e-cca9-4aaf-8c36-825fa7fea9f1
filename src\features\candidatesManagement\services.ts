import { InvokeCommand, LambdaClient } from "@aws-sdk/client-lambda";
import { In, Repository } from "typeorm";
import dbConnection from "../../db/dbConnection";
import CandidatesModel from "../../schema/s9-innerview/candidates";
import { ResponseObject } from "../../interface/commonInterface";
import JobApplicationsModel, {
  ApplicationRankStatus,
  Status,
} from "../../schema/s9-innerview/job_applications";
import {
  ACTIVITY_LOG_TYPE,
  CANDIDATE_APPLICATION_MSG,
  DEFAULT_LIMIT,
  ENTITY_TYPE,
  REDIS_KEYS,
  SUBSCRIPTION_MSG,
} from "../../utils/constants";
import AuthServices from "../auth/services";
import ApplicantAdditionalInfoModel from "../../schema/s9-innerview/applicant_additional_info";
import { ApplicantAdditionalInfo } from "../resumeScreen/interface";
import InterviewModel from "../../schema/s9-innerview/interview";
import UserModel from "../../schema/s9/user";
import {
  FlattenedSkillScore,
  // GroupedSkillScore,
  SkillEvaluationRaw,
} from "./interface";
import NotificationServices from "../notification/services";
import { NotificationType } from "../../schema/s9-innerview/notifications";
import Role from "../../schema/s9-innerview/roles";
import FinalAssessmentsModel from "../../schema/s9-innerview/final_assessments";
// eslint-disable-next-line import/prefer-default-export
import { clientConfig } from "../../config/awsConfig";
import * as helper from "../../utils/helper";
import envConfig from "../../config/envConfig";
import { handleSentryError } from "../../utils/helper";
import Cache from "../../db/cache";

const CONFIG = envConfig();

export class CandidateApplicationService {
  static async getAllCandidates(
    orgId: number,
    jobId: number,
    isActive?: boolean,
    searchStr: string = "",
    offset: number = 0,
    limit: number = DEFAULT_LIMIT
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const candidatesRepo = dataSource.getRepository(CandidatesModel);

      const query = candidatesRepo
        .createQueryBuilder("candidate")
        .innerJoin(
          "job_applications",
          "jobApplication",
          "jobApplication.candidate_id = candidate.id"
        )
        .leftJoin(
          (subQuery) =>
            subQuery
              .select([
                "interview.jobApplicationId AS job_application_id",
                "MAX(interview.roundNumber) AS current_round",
              ])
              .from("interview", "interview")
              .groupBy("interview.jobApplicationId"),
          "interviewRounds",
          "interviewRounds.job_application_id = jobApplication.id"
        )
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = :isActive", { isActive });

      if (jobId) {
        query.andWhere("jobApplication.jobId = :jobId", { jobId });
      }

      if (searchStr.trim().length > 0) {
        query.andWhere("LOWER(candidate.name) LIKE LOWER(:searchStr)", {
          searchStr: `%${searchStr.trim()}%`,
        });
      }

      const data = await query
        .andWhere("jobApplication.isTopApplication = false")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .offset(offset)
        .limit(limit)
        .select([
          "candidate.id AS candidateId",
          "candidate.name AS candidateName",
          "candidate.email AS candidateEmail",
          "candidate.imageUrl AS candidateImageUrl",
          "jobApplication.id AS applicationId",
          "jobApplication.status AS applicationStatus",
          "jobApplication.source AS applicationSource",
          "jobApplication.created_ts AS applicationCreatedTs",
          "jobApplication.updated_ts AS applicationUpdatedTs",
          "jobApplication.isActive AS isActive",
          "jobApplication.jobId AS jobId",
          "jobApplication.hiringManagerId AS hiringManagerId",
          "jobApplication.hiringManagerReason AS hiringManagerReason",
          "jobApplication.applicationRankStatus AS applicationRankStatus",
          "jobApplication.aiReason AS aiReason",
          "jobApplication.aiDecision AS aiDecision",
          "JSON_UNQUOTE(JSON_EXTRACT(jobApplication.atsScore, '$.total_ats_score')) AS atsScore",
          "COALESCE(interviewRounds.current_round, 0) AS currentRound",
        ])
        .getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.candidates_fetched,
        data,
      };
    } catch (error: any) {
      handleSentryError(error, "getAllCandidates");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_all_candidates_failed,
        error: error.message,
      };
    }
  }

  static async archiveActiveApplication(
    applicationId: number,
    orgId: number,
    status: boolean,
    reason?: string
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const jobApplication = await jobAppRepo
        .createQueryBuilder("jobApplication")
        .innerJoinAndSelect("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", { applicationId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }

      if (jobApplication.isActive === status) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.already_updated,
        };
      }

      jobApplication.isActive = status;
      jobApplication.isTopApplication = false; // Reset top application status when archiving
      jobApplication.applicationRankStatus = ApplicationRankStatus.NO_CHANGES;
      jobApplication.updatedTs = new Date();

      if (status === false && typeof reason === "string") {
        jobApplication.hiringManagerReason = reason.trim();
      } else if (status === true) {
        jobApplication.hiringManagerReason = null;
      }

      await jobAppRepo.save(jobApplication);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
      };
    } catch (error: any) {
      handleSentryError(error, "archiveActiveApplication");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error: error.message,
      };
    }
  }

  // get top candidates

  static getTopCandidates = async (orgId: number, jobId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const query = jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .leftJoin(
          (subQuery) =>
            subQuery
              .select([
                "interview.jobApplicationId AS job_application_id",
                "MAX(interview.roundNumber) AS current_round",
              ])
              .from("interview", "interview")
              .groupBy("interview.jobApplicationId"),
          "interviewRounds",
          "interviewRounds.job_application_id = jobApplication.id"
        )
        .where("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.jobId = :jobId", { jobId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("jobApplication.isTopApplication = true")
        .orderBy(
          "CAST(JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS DECIMAL)",
          "DESC"
        )
        .select([
          "candidate.name AS candidateName",
          "candidate.email AS candidateEmail",
          "candidate.imageUrl AS candidateImageUrl",
          "jobApplication.id AS applicationId",
          "jobApplication.applicationRankStatus AS applicationRankStatus",
          "candidate.id AS candidateId",
          "jobApplication.status AS applicationStatus",
          "jobApplication.source AS applicationSource",
          "jobApplication.created_ts AS applicationCreatedTs",
          "jobApplication.updated_ts AS applicationUpdatedTs",
          "jobApplication.hiring_manager_reason AS hiringManagerReason",
          "jobApplication.ai_reason AS aiReason",
          "jobApplication.ai_decision AS aiDecision",
          "jobApplication.job_id AS jobId",
          "jobApplication.isTopApplication AS isTopApplication",
          "JSON_UNQUOTE(JSON_EXTRACT(jobApplication.ats_score, '$.total_ats_score')) AS atsScore",
          "COALESCE(interviewRounds.current_round, 0) AS currentRound",
        ]);

      const data = await query.getRawMany();

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.top_candidates_retrieved,
        data,
      };
    } catch (error: any) {
      handleSentryError(error, "getTopCandidates");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_top_candidates_failed,
        error: error.message,
      };
    }
  };

  // promote or demote candidates

  static promoteDemoteCandidate = async (
    candidateId: number,
    applicationId: number,
    orgId: number,
    action: ApplicationRankStatus.PROMOTED | ApplicationRankStatus.DEMOTED
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobAppRepo = dataSource.getRepository(JobApplicationsModel);

      const application = await jobAppRepo
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", { applicationId })
        .andWhere("jobApplication.candidateId = :candidateId", { candidateId })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      if (!application) {
        throw new Error(
          CANDIDATE_APPLICATION_MSG.candidate_application_not_found
        );
      }

      if (application.status !== Status.APPROVED) {
        return {
          success: false,
          message:
            CANDIDATE_APPLICATION_MSG.can_promote_or_demote_only_after_resume_screening,
        };
      }

      const isTopApplication = action === ApplicationRankStatus.PROMOTED;
      const applicationRankStatus = isTopApplication
        ? ApplicationRankStatus.PROMOTED
        : ApplicationRankStatus.DEMOTED;

      application.isTopApplication = isTopApplication;
      application.applicationRankStatus = applicationRankStatus;

      await jobAppRepo.save(application);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_success,
      };
    } catch (error: any) {
      handleSentryError(error, "promoteDemoteCandidate");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_rank_status_failed,
        error: error.message,
      };
    }
  };

  static getCandidateDetails = async (
    jobApplicationId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const interviewRepo = dataSource.getRepository(InterviewModel);

      // make below query first from job_application table
      const query = dataSource
        .getRepository(JobApplicationsModel)
        .createQueryBuilder("jobApplication")
        .leftJoinAndSelect(
          "candidates",
          "candidate",
          "jobApplication.candidateId = candidate.id"
        )
        .innerJoinAndSelect(
          "jobApplication.job",
          "job",
          "job.id = jobApplication.jobId"
        )
        .leftJoinAndSelect(
          "job.department",
          "department",
          "department.id = job.departmentId"
        )
        .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
        .andWhere("job.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .select([
          "candidate.name as candidateName",
          "candidate.email as candidateEmail",
          "jobApplication.id as jobApplicationId",
          "jobApplication.jobId as jobId",
          "job.title as jobTitle",
          "jobApplication.status as status",
          "jobApplication.resume_file as resumeLink",
          "jobApplication.hiring_manager_id as hiringManagerId",
          "candidate.imageUrl AS imageUrl",
          "department.name AS department",
        ]);
      // const query = dataSource
      //   .getRepository(CandidatesModel)
      //   .createQueryBuilder("candidate")
      //   .leftJoinAndSelect(
      //     "job_applications",
      //     "jobApplication",
      //     "jobApplication.candidateId = candidate.id"
      //   )
      //   .leftJoinAndSelect(
      //     "jobApplication.job",
      //     "job",
      //     "job.id = jobApplication.jobId"
      //   )

      //   .leftJoinAndSelect(
      //     "job.department",
      //     "department",
      //     "department.id = job.departmentId"
      //   )
      //   // .leftJoinAndSelect(
      //   //   "jobApplication.statusHistory",
      //   //   "statusHistory"
      //   // )
      //   .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
      //   .andWhere("candidate.orgId = :orgId", { orgId })
      //   .select([
      //     "candidate.name as candidateName",
      //     "jobApplication.id as jobApplicationId",
      //     "jobApplication.jobId as jobId",
      //     "job.title as jobTitle",
      //     "jobApplication.status as status",
      //     "jobApplication.resume_file as resumeLink",
      //     "jobApplication.hiring_manager_id as hiringManagerId",
      //     "candidate.imageUrl AS imageUrl",
      //     "department.name AS department",
      //   ]);
      // .setParameter('clearedStatus', Status.APPROVED);

      const result = await query.getRawOne();

      if (!result) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found,
        };
      }

      const finalAssessmentRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(
          FinalAssessmentsModel
        );
      const finalAssessment = await finalAssessmentRepo.findOne({
        where: {
          jobApplicationId,
        },
        select: ["id", "skillSummary", "isFinalAssessmentGenerated"],
      });

      console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>");
      console.log("Final Assessment:", finalAssessment);

      const interviewInfo = await interviewRepo.findOne({
        where: {
          jobApplicationId: result.jobApplicationId,
        },
        order: {
          id: "DESC",
        },
      });
      const interviewerInfo = await AuthServices.getUserByUserId(
        result.hiringManagerId
      );

      const redisKey = REDIS_KEYS.FINAL_ASSESSMENT_GENERATION_STATUS.replace(
        "{jobApplicationId}",
        String(jobApplicationId)
      );
      const cache = new Cache();
      const generationStatus = await cache.get(redisKey);

      console.log(">>>>>>>>>>>>>>>>>>>>>>>>>>>");
      console.log("Final Assessment Generation Status:", generationStatus);

      return {
        success: true,
        data: {
          ...result,
          interviewerName:
            interviewerInfo && interviewerInfo?.first_name
              ? `${interviewerInfo.first_name || ""} ${interviewerInfo.last_name || ""}`
              : null,
          interviewerImage: interviewerInfo?.image,
          roundNumber: interviewInfo?.roundNumber,
          isFinalAssessmentGenerated:
            finalAssessment && finalAssessment.isFinalAssessmentGenerated,
          /* eslint-disable no-unneeded-ternary */
          isFinalSummaryGenerated:
            finalAssessment &&
            finalAssessment.skillSummary &&
            finalAssessment.skillSummary.finalSummary &&
            finalAssessment.skillSummary.finalSummary.length > 0
              ? true
              : false,
          assessmentId: finalAssessment?.id,
          isFinalAssessmentGenerating:
            generationStatus && generationStatus === "1",
        },
      };
    } catch (error: unknown) {
      console.log("Error in getCandidateDetails:", error);

      handleSentryError(error, "getCandidateDetails");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.fetch_candidate_details_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static addApplicantAdditionalInfo = async (
    orgId: number,
    body: ApplicantAdditionalInfo
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      const jobApplication = await dataSource
        .getRepository(JobApplicationsModel)
        .createQueryBuilder("jobApplication")
        .innerJoin("jobApplication.candidate", "candidate")
        .where("jobApplication.id = :applicationId", {
          applicationId: body.applicationId,
        })
        .andWhere("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.job_application_not_found,
        };
      }
      // Step 1: Check if candidate exists for given orgId and candidateId
      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: {
            id: jobApplication.candidateId,
            orgId,
          },
        });

      if (!candidate) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_not_found_for_org,
        };
      }

      // Step 2: Save additional info
      const repo = dataSource.getRepository(ApplicantAdditionalInfoModel);

      const newInfo = new ApplicantAdditionalInfoModel();
      newInfo.description = body.description;
      newInfo.jobApplicationId = body.applicationId;
      newInfo.images = body.images ? { urls: [body.images] } : null;

      const savedInfo = await repo.save(newInfo);
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.additional_info_saved,
        data: savedInfo,
      };
    } catch (error: unknown) {
      handleSentryError(error, "addApplicantAdditionalInfo");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.save_additional_info_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  static updateJobApplicationStatus = async (
    jobApplicationId: number,
    status: string,
    orgId: number,
    userId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);
      const finalAssessmentRepo = dataSource.getRepository(
        FinalAssessmentsModel
      );

      // Check if job application exists
      const jobApplication = await jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .innerJoinAndSelect("jobApplication.job", "job")
        .where("jobApplication.id = :jobApplicationId", { jobApplicationId })
        .andWhere("jobApplication.isActive = true")
        .andWhere("job.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getOne();

      if (!jobApplication) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.candidate_application_not_found,
        };
      }

      const finalAssessment = await finalAssessmentRepo.findOne({
        where: {
          jobApplicationId,
        },
      });

      if (!finalAssessment) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.final_assessment_not_exists,
        };
      }

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviews = await interviewRepo
        .createQueryBuilder("interview")
        .select([
          `DISTINCT interview.scheduledBy as scheduledBy`,
          "interview.isEnded as isEnded",
          "interview.isFeedbackFilled as isFeedbackFilled",
        ])
        .where("interview.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .getRawMany();

      if (!interviews || !interviews.length) {
        return {
          success: false,
          message:
            CANDIDATE_APPLICATION_MSG.no_interview_found_to_update_status,
        };
      }

      const notEndedInterviews = interviews.filter((i) => !i.isEnded);

      if (notEndedInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interviews_not_completed,
        };
      }

      const pendingFeedbackInterviews = interviews.filter(
        (i) => !i.isFeedbackFilled
      );

      if (pendingFeedbackInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interview_feedback_pending,
        };
      }

      // Update the status
      // Convert string to Status enum value
      jobApplication.status = status as Status;

      // Set hired date if status is HIRED
      if (status === Status.HIRED) {
        jobApplication.hiredDate = new Date();
      }

      // Save the updated job application
      const updatedJobApplication =
        await jobApplicationRepo.save(jobApplication);

      const candidate = await dataSource
        .getRepository(CandidatesModel)
        .findOne({
          where: { id: jobApplication.candidateId },
        });

      const notificationStatus =
        status === Status.FINAL_REJECT
          ? NotificationType.CANDIDATE_REJECTED
          : NotificationType.CANDIDATE_HIRED;

      const description =
        status === Status.FINAL_REJECT
          ? ` Unfortunately! We’ve decided not to proceed with ${candidate?.name}'s application at this time for ${jobApplication.job?.title} `
          : ` Hiring complete! ${candidate?.name} is officially hired for ${jobApplication.job?.title} `;

      interviews.forEach(async (interviewItem) => {
        NotificationServices.createNotification(
          orgId,
          interviewItem.scheduledBy,
          {
            type: notificationStatus,
            title: notificationStatus,
            description,
            relatedId: jobApplication.job?.id,
          }
        );
      });

      // Add activity log for hire/reject
      if (status === Status.HIRED || status === Status.FINAL_REJECT) {
        try {
          await helper.addActivityLog({
            orgId,
            logType:
              status === Status.HIRED
                ? ACTIVITY_LOG_TYPE.HIRE_CANDIDATE
                : ACTIVITY_LOG_TYPE.REJECT_CANDIDATE,
            userId,
            entityId: jobApplicationId,
            entityType: ENTITY_TYPE.JOB_APPLICATION,
            oldValue: null,
            newValue: status,
            comments:
              status === Status.HIRED
                ? `Candidate ${candidate?.name} hired.`
                : `Candidate ${candidate?.name} rejected.`,
          });
        } catch (activityLogError) {
          console.log(
            "Error adding activity log for hire/reject:",
            activityLogError
          );
          handleSentryError(activityLogError, "updateApplicationStatus");
        }
      }

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_success,
        data: updatedJobApplication,
      };
    } catch (error: unknown) {
      console.log("error>>>>>>>>>>>>", error);
      handleSentryError(error, "updateApplicationStatus");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.update_application_status_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate interview history
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's interview history
   */
  static getCandidateInterviewHistory = async (
    interviewerId: number,
    jobApplicationId: number,
    orgId: number,
    roleId: number
  ): Promise<ResponseObject> => {
    try {
      // Optimized interview data query with efficient aggregation
      // Note: Cannot JOIN users table as it's in a different database

      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviewHistory = await interviewRepo
        .createQueryBuilder("i")
        .innerJoin("jobs", "job", "job.id = i.jobId")
        .innerJoin(
          "job_applications",
          "jobApplication",
          "jobApplication.id = i.jobApplicationId"
        )
        .leftJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interviewId = i.id"
        )
        .leftJoin("skills", "s", "s.id = ise.skillId")
        .where("i.jobApplicationId = :jobApplicationId", { jobApplicationId })
        .andWhere("job.org_id = :orgId", { orgId })
        .andWhere("i.isFeedbackFilled = true")
        .andWhere("jobApplication.isActive = true")
        .groupBy(
          "i.round_number, i.interviewer_id, i.hard_skill_marks, i.interview_summary, i.interviewer_performance_ai_analysis"
        )
        .select([
          "i.id AS interviewId",
          "i.round_number AS roundNumber",
          "i.interviewer_id AS interviewerId",
          "i.hard_skill_marks AS hardSkillMarks",
          "i.interview_summary AS interviewSummary",
          "i.interviewer_performance_ai_analysis AS interviewerPerformanceAiAnalysis",
          "i.interview_end_time AS endTime",
          "i.is_allowed_for_next_round AS isAllowedForNextRound",
          // "ise.skill_marks AS skillMarks",
          // "ise.skill_id AS skillId",
          // "s.title AS skillTitle",
          `(SELECT COALESCE(
              JSON_OBJECTAGG(s2.title, ise2.skill_marks),
              '{}'
            )
            FROM interview_skill_evaluations ise2
            LEFT JOIN skills s2 ON s2.id = ise2.skill_id
            WHERE ise2.interview_id = i.id
            AND s2.title IS NOT NULL
            AND s2.title != ''
          ) AS skillScores`,
        ])
        .orderBy("i.round_number", "ASC")
        .getRawMany();

      // const query = dataSource
      //   .createQueryBuilder()
      //   .select([
      //     "i.id AS interviewId",
      //     "i.round_number AS roundNumber",
      //     "i.interviewer_id AS interviewerId",
      //     "i.hard_skill_marks AS hardSkillMarks",
      //     "i.interview_summary AS interviewSummary",
      //     "i.interviewer_performance_ai_analysis AS interviewerPerformanceAiAnalysis",
      //     "i.interview_end_time AS endTime",
      //     // Use JSON_OBJECTAGG for efficient skill aggregation
      //     "JSON_OBJECTAGG(s.title, ise.skill_marks) AS skillScores",
      //   ])
      //   .from("job_applications", "ja")
      //   .innerJoin("interview", "i", "i.job_application_id = ja.id")
      //   .innerJoin(
      //     "interview_skill_evaluations",
      //     "ise",
      //     "ise.interview_id = i.id"
      //   )
      //   .innerJoin("jobs", "job", "job.id = ja.job_id")
      //   .innerJoin("skills", "s", "s.id = ise.skill_id")
      //   .andWhere("job.org_id = :orgId", { orgId })
      //   .andWhere("ja.id = :jobApplicationId", { jobApplicationId })
      //   .groupBy(
      //     "i.round_number, i.interviewer_id, i.hard_skill_marks, i.interview_summary, i.interviewer_performance_ai_analysis"
      //   )
      //   .orderBy("i.round_number", "ASC");

      // Execute optimized interview data query
      // const interviewHistory = await query.getRawMany();

      // Early return if no interview history found
      if (!interviewHistory || !interviewHistory.length) {
        return {
          success: true,
          message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
          data: [],
        };
      }

      // Extract unique interviewer IDs efficiently using Set to avoid duplicates
      const interviewerIds = [
        ...new Set(interviewHistory.map((item) => item.interviewerId)),
      ];

      // Get current user's account type to check admin privileges
      const roleRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(Role);
      const currentUser = await roleRepo.findOne({
        where: { id: roleId },
        select: ["isDefaultRole"],
      });

      console.log("currentUser", currentUser);

      const isAdmin = currentUser.isDefaultRole;

      // Optimized user lookup query - single query for all interviewer IDs
      // Using the separate database connection for users table
      const userRepo = await dbConnection.getS9DatabaseRepository(UserModel);
      const users = await userRepo.find({
        where: { id: In(interviewerIds) },
        select: ["id", "first_name", "last_name", "image"],
      });

      // Create efficient lookup map for O(1) user data access
      const userMap = new Map(
        users.map((user) => [
          user.id,
          {
            name: `${user.first_name || ""} ${user.last_name || ""}`.trim(),
            image: user.image || "",
          },
        ])
      );

      // Optimized data transformation with minimal processing
      const formattedHistory = interviewHistory.map((item) => {
        // eslint-disable-next-line prefer-destructuring
        // Parse JSON fields only once and handle null cases efficiently
        // eslint-disable-next-line prefer-destructuring
        let interviewSummary = item.interviewSummary;
        // eslint-disable-next-line prefer-destructuring
        let skillScores = item.skillScores;
        let interviewerAiAnalysis = null;

        // Efficient JSON parsing with error handling
        try {
          if (typeof interviewSummary === "string" && interviewSummary) {
            interviewSummary = JSON.parse(interviewSummary);
          } else {
            interviewSummary = { highlights: [] }; // Default to empty highlights if parsing fails
          }
        } catch (e) {
          handleSentryError(e, "getInterviewHistory");
          console.warn(
            `Failed to parse interview summary for round ${item.roundNumber}:`,
            e
          );
          interviewSummary = { highlights: [] };
        }

        try {
          if (typeof skillScores === "string" && skillScores) {
            skillScores = JSON.parse(skillScores);
          }
        } catch (e) {
          handleSentryError(e, "getInterviewHistory");
          console.warn(
            `Failed to parse skill scores for round ${item.roundNumber}:`,
            e
          );
          skillScores = {};
        }

        // Parse AI analysis based on admin privileges or interviewer match
        if (item.interviewerPerformanceAiAnalysis) {
          try {
            const fullAnalysis =
              typeof item.interviewerPerformanceAiAnalysis === "string"
                ? JSON.parse(item.interviewerPerformanceAiAnalysis)
                : item.interviewerPerformanceAiAnalysis;

            if (isAdmin || item.interviewerId === interviewerId) {
              // Admin or own interview: send full data including highlights
              interviewerAiAnalysis = fullAnalysis;
            } else {
              // Non-admin viewing other interviewer: send only interviewerPerformance data, exclude highlights
              interviewerAiAnalysis = { highlights: [] };
            }
          } catch (e) {
            handleSentryError(e, "getInterviewHistory");
            console.warn(
              `Failed to parse AI analysis for round ${item.roundNumber}:`,
              e
            );
            interviewerAiAnalysis = { highlights: [] };
          }
        }

        // Get user information from the efficient lookup map
        const userInfo = userMap.get(item.interviewerId);

        // console.log("item", item);

        return {
          roundNumber: item.roundNumber,
          interviewerId: item.interviewerId,
          interviewerPerformanceAiAnalysis: interviewerAiAnalysis,
          interviewerName: userInfo?.name || "",
          interviewerImage: userInfo?.image || "",
          hardSkillMarks: item.hardSkillMarks,
          interviewSummary,
          skillScores: skillScores || {},
          endTime: item.endTime,
        };
      });
      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.interview_history_retrieved,
        data: formattedHistory,
      };
    } catch (error) {
      handleSentryError(error, "getInterviewHistory");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_interview_history_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get Application Final Summary for a candidate
   * @param candidateId - The ID of the candidate
   * @returns A response object with the candidate's skill specific assessment and final assessment data
   */

  static getApplicationFinalSummary = async (
    jobApplicationId: number,
    orgId: number
  ): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      // get data from final_assessments table using job_application_id column
      const finalAssessmentRepo = dataSource.getRepository(
        FinalAssessmentsModel
      );

      // we are saving final_summary Data in final assessment table
      const finalSummaryData = await finalAssessmentRepo
        .createQueryBuilder("finalAssessment")
        .innerJoin("finalAssessment.jobApplication", "jobApplication")
        .where("finalAssessment.jobApplicationId = :jobApplicationId", {
          jobApplicationId,
        })
        .innerJoin("jobApplication.candidate", "candidate")
        .andWhere("candidate.orgId = :orgId", { orgId })
        .andWhere("jobApplication.isActive = true")
        .getRawOne();

      // Create query builder for final assessment data
      // const finalAssessmentQuery = dataSource
      //   .createQueryBuilder()
      //   .select([
      //     "ja.id AS job_application_id",
      //     "fa.development_recommendations",
      //     "fa.skill_summary",
      //     "fa.overall_success_probability",
      //   ])
      //   .from("job_applications", "ja")
      //   .leftJoin("final_assessments", "fa", "fa.job_application_id = ja.id")
      //   .where("ja.id = :jobApplicationId", { jobApplicationId });

      // const finalAssessmentData = await finalAssessmentQuery.getRawOne();

      // Transform final assessment data - handle potential null values from LEFT JOIN

      if (
        !finalSummaryData ||
        !finalSummaryData.finalAssessment_skill_summary ||
        finalSummaryData.finalAssessment_skill_summary === "null"
      ) {
        console.log(
          "No final summary data found jobApplicationId",
          jobApplicationId
        );
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.application_final_summary_failed,
          data: {
            formattedFinalAssessment: {},
            candidateProfileSkillScoreData: {},
          },
        };
      }

      console.log("finalSummaryData========>", finalSummaryData);
      const formattedFinalSummary =
        JSON.parse(finalSummaryData.finalAssessment_skill_summary).finalSummary
          .length > 0
          ? {
              jobApplicationId,
              developmentRecommendations:
                finalSummaryData.finalAssessment_development_recommendations
                  ? JSON.parse(
                      finalSummaryData.finalAssessment_development_recommendations
                    )
                  : null,
              skillSummary: finalSummaryData.finalAssessment_skill_summary
                ? JSON.parse(finalSummaryData.finalAssessment_skill_summary)
                : null,
              overallSuccessProbability:
                finalSummaryData.finalAssessment_overall_success_probability ||
                null,
              behaviouralScores:
                finalSummaryData.finalAssessment_behavioural_scores
                  ? JSON.parse(
                      finalSummaryData.finalAssessment_behavioural_scores
                    ).behaviouralScores
                  : null,
            }
          : {};

      console.log("formattedFinalSummary========>", formattedFinalSummary);

      const CandidateProfileSkillScoreDataResponse =
        await CandidateApplicationService.getCandidateProfileSkillScoreData(
          jobApplicationId
        );

      console.log(
        "CandidateProfileSkillScoreDataResponse========>",
        CandidateProfileSkillScoreDataResponse
      );

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_retrieved,
        data: {
          formattedFinalSummary,
          candidateProfileSkillScoreData:
            CandidateProfileSkillScoreDataResponse.data,
        },
      };
    } catch (error) {
      handleSentryError(error, "getFinalSummary");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.application_final_summary_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Get candidate profile skill score data with optimized performance
   *
   * Recommended Database Indexes for optimal performance:
   * 1. CREATE INDEX idx_job_applications_candidate_id ON job_applications(candidate_id);
   * 2. CREATE INDEX idx_interview_job_application_ended ON interview(job_application_id, is_ended);
   * 3. CREATE INDEX idx_interview_skill_evaluations_interview_interviewer ON interview_skill_evaluations(interview_id, interviewer_id);
   * 4. CREATE INDEX idx_skills_id_title ON skills(id, title);
   *
   * These indexes will significantly improve query performance by:
   * - Optimizing the candidate lookup in job_applications
   * - Speeding up the interview filtering by job_application_id and is_ended status
   * - Accelerating the skill evaluations join and grouping operations
   * - Enhancing the skills table lookup performance
   */

  static getCandidateProfileSkillScoreData = async (
    jobApplicationId: number
  ): Promise<ResponseObject> => {
    // Type definitions for better type safety
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();

      // Optimized query with better field ordering and explicit casting
      const skillScoreQuery = dataSource
        .createQueryBuilder()
        .select([
          "i.interviewer_id AS interviewer_id",
          "i.hard_skill_marks AS hard_skill_marks",
          // Handle case where no skill evaluations exist
          "CASE WHEN COUNT(ise.id) > 0 THEN " +
            "JSON_ARRAYAGG(" +
            "JSON_OBJECT(" +
            "'skill_name', s.title, " +
            "'skill_marks', ise.skill_marks, " +
            "'probability_of_success_in_this_skill', ise.probability_of_success_in_this_skill, " +
            "'strengths', ise.strengths, " +
            "'potentials_gaps', ise.potentials_gaps" +
            ")" +
            ") " +
            "ELSE JSON_ARRAY() END AS skills",
        ])
        .from("interview", "i")
        // Optimized JOIN order: most selective conditions first

        .leftJoin(
          "interview_skill_evaluations",
          "ise",
          "ise.interview_id = i.id"
        )
        .leftJoin("skills", "s", "s.id = ise.skill_id")
        .where("i.job_application_id = :jobApplicationId", { jobApplicationId })
        .innerJoin("job_applications", "ja", "ja.id = i.job_application_id")
        .andWhere("ja.isActive = true")
        .andWhere("i.isFeedbackFilled = true")
        // Optimized GROUP BY with proper ordering for index usage
        .groupBy("ise.interviewer_id, i.hard_skill_marks")
        .orderBy("ise.interviewer_id", "ASC"); // Add ordering for consistent results

      const rawSkillScores: SkillEvaluationRaw[] =
        await skillScoreQuery.getRawMany();
      // Early return with specific error message
      console.log("rawSkillScores", JSON.stringify(rawSkillScores, null, 2));
      if (!rawSkillScores.length) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.no_skill_score_data_found,
          data: null,
        };
      }

      let totalHardSkillMarks = 0;
      // Parse JSON once and store in memory-efficient structure
      const flattenedSkillScores: FlattenedSkillScore[] = [];

      rawSkillScores.forEach((item) => {
        totalHardSkillMarks += +item.hard_skill_marks;
        console.log("item.hard_skill_marks", item.hard_skill_marks);
        console.log("item.item.skills", item.skills);

        const skills =
          typeof item.skills === "string"
            ? JSON.parse(item.skills)
            : item.skills;

        console.log("skills", skills);

        flattenedSkillScores.push(
          ...skills.map((skill) => ({
            skill_name: skill.skill_name,
            skill_marks: skill.skill_marks,
            strengths: skill.strengths,
            potentials_gaps: skill.potentials_gaps,
            probability_of_success_in_this_skill:
              skill.probability_of_success_in_this_skill,
          }))
        );
      });

      // Memory-efficient flattening with pre-calculated array size
      // const flattenedSkillScores: FlattenedSkillScore[] =
      //   groupedSkillScores.flatMap((interviewer) =>
      //     interviewer.skills.map((skill) => ({
      //       skill_name: skill.skill_name,
      //       skill_marks: skill.skill_marks,
      //       strengths: skill.strengths,
      //       potentials_gaps: skill.potentials_gaps,
      //       probability_of_success_in_this_skill:
      //         skill.probability_of_success_in_this_skill,
      //     }))
      //   );

      // Optimized career score calculation with validation
      const totalCareerSkillScore =
        rawSkillScores.length > 0
          ? Math.ceil(totalHardSkillMarks / rawSkillScores.length)
          : 0;

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.skill_specific_assessment_retrieved,
        data: {
          careerBasedSkillsScore: totalCareerSkillScore,
          skillsScores: flattenedSkillScores,
        },
      };
    } catch (error) {
      handleSentryError(error, "getSkillSpecificAssessment");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_skill_specific_assessment_failed,
        error:
          error instanceof Error
            ? error.message
            : CANDIDATE_APPLICATION_MSG.unknown_error,
      };
    }
  };

  /**
   * Generates final summary for a candidate application
   *
   * This method triggers the generation of a comprehensive final summary for a candidate
   * based on their interview performance, skill assessments, and overall evaluation data.
   * Currently returns a placeholder success response.
   *
   * @param {string} candidateId - The unique identifier of the candidate
   * @param {string} jobApplicationId - The unique identifier of the job application
   * @param {number} orgId - The organization ID for authorization
   * @returns {Promise<ResponseObject>} Promise resolving to success confirmation
   */
  static async generateFinalSummary(
    jobApplicationId: number,
    orgId: number,
    userId: number
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      // TODO: Implement actual final summary generation logic
      // This could include:
      // - Aggregating interview data from all rounds
      // - Calculating overall success probability
      // - Generating AI-powered insights and recommendations
      // - Storing the generated summary in the database

      // For now, return a placeholder success response

      // check interview schedule exists or not
      const interviewRepo =
        await dbConnection.getS9InnerViewDatabaseRepository(InterviewModel);

      const interviews = await interviewRepo.find({
        where: {
          jobApplicationId,
        },
      });

      console.log(">>>>>>>>>>>>>>>>>>>>>>>>>interviews", interviews);

      // Validate minimum 1 interview round is completed
      if (!interviews || interviews.length < 1) {
        return {
          success: false,
          message:
            CANDIDATE_APPLICATION_MSG.minimum_one_interview_round_required,
        };
      }

      const notEndedInterviews = interviews.filter((i) => !i.isEnded);

      console.log("notEndedInterviews>>>>>>>>>>>>", notEndedInterviews);

      if (notEndedInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interviews_not_completed,
        };
      }

      const pendingFeedbackInterviews = interviews.filter(
        (i) => !i.isFeedbackFilled
      );

      console.log(
        "pendingFeedbackInterviews>>>>>>>>>>>>",
        pendingFeedbackInterviews
      );

      if (pendingFeedbackInterviews.length > 0) {
        return {
          success: false,
          message: CANDIDATE_APPLICATION_MSG.interview_feedback_pending,
        };
      }

      const finalAssessment = await dataSource
        .getRepository(FinalAssessmentsModel)
        .findOne({
          where: {
            jobApplicationId,
          },
        });

      console.log("finalAssessment>>>>>>>>>>>>", finalAssessment);
      console.log(
        "typeof finalAssessment.skillSummary",
        typeof finalAssessment
      );

      if (
        finalAssessment &&
        finalAssessment.skillSummary &&
        finalAssessment.skillSummary?.finalSummary
      ) {
        console.log(
          ">>>>>>>>>>>>>>>skillSummary.finalSummary",
          finalAssessment.skillSummary?.finalSummary
        );

        return {
          success: true,
          message: CANDIDATE_APPLICATION_MSG.final_summary_already_generated,
        };
      }

      // return;
      console.log("continue============>");

      const lambdaClient = new LambdaClient({
        ...clientConfig,
      });
      const command = new InvokeCommand({
        FunctionName: CONFIG.generate_int_final_summary_lambda,
        Payload: JSON.stringify({
          jobApplicationId,
          orgId,
          userId,
        }),
      });
      const lambdaResponse = await lambdaClient.send(command);
      console.log("lambdaResponse", lambdaResponse);
      const lambdaPayload = JSON.parse(
        new TextDecoder().decode(lambdaResponse.Payload)
      );

      console.log("lambdaPayload===============>", lambdaPayload);
      if (lambdaPayload.success === false) {
        return {
          success: false,
          message: lambdaPayload.message,
        };
      }

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.final_summary_generated_successfully,
      };
    } catch (error) {
      handleSentryError(error, "generateFinalSummary");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.generate_final_summary_failed,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  }

  static getAllHiredCandidate = async (orgId: number) => {
    try {
      if (!orgId) {
        return {
          success: false,
          message: SUBSCRIPTION_MSG.organization_not_found,
        };
      }

      console.log("Fetching hired candidates for orgId:", orgId);

      // Get database connections
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const s9DataSource = await dbConnection.getS9DataSource();

      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);
      const userRepo = s9DataSource.getRepository(UserModel);
      // Fetch hired candidates along with interviewers' data and final assessment in one optimized query
      const hiredCandidates = await jobApplicationRepo
        .createQueryBuilder("jobApplication")
        .where("jobApplication.status = :status", { status: Status.HIRED })
        .innerJoin("jobApplication.candidate", "candidate")
        .andWhere("candidate.org_id = :orgId", { orgId })
        .innerJoin("jobs", "job", "job.id = jobApplication.jobId")
        .innerJoin(
          "interview",
          "interview",
          "interview.jobApplicationId = jobApplication.id"
        )
        .leftJoin(
          "final_assessments",
          "finalAssessment",
          "finalAssessment.job_application_id = jobApplication.id"
        )
        .select([
          "candidate.name as candidateName",
          "candidate.imageUrl AS candidateImageUrl",
          "job.title as jobTitle",
          "interview.interviewer_id as interviewerId",
          "interview.roundNumber as roundNumber",
          "finalAssessment.overall_success_probability as overallSuccessProbability",
          "jobApplication.hired_date as hiredDate",
          "jobApplication.id as jobApplicationId",
        ])
        .getRawMany();

      console.log("hiredCandidates======>", hiredCandidates);

      if (hiredCandidates.length === 0) {
        return {
          success: true,
          message: "No hired candidates found for this organization.",
          data: [],
        };
      }

      // Extract unique interviewer IDs
      const interviewerIds = [
        ...new Set(
          hiredCandidates.map((item) => item.interviewerId).filter((id) => id)
        ),
      ];

      // Fetch interviewer details if available
      const interviewersMap =
        await CandidateApplicationService.getInterviewersData(
          interviewerIds,
          userRepo
        );

      // Group hired candidates by job title and candidate name
      const candidateMap =
        CandidateApplicationService.groupCandidatesByJobTitleAndInterviewers(
          hiredCandidates,
          interviewersMap
        );

      console.log("candidate Map========>", candidateMap);

      return {
        success: true,
        message: CANDIDATE_APPLICATION_MSG.get_hired_candidate_success,
        data: Array.from(candidateMap.values()),
      };
    } catch (error) {
      handleSentryError(error, "getAllHiredCandidate");
      return {
        success: false,
        message: CANDIDATE_APPLICATION_MSG.get_hired_candidate_failed,
        error:
          error instanceof Error ? error.message : "Unknown error occurred",
      };
    }
  };

  // Function to get interviewers' details
  static getInterviewersData = async (
    interviewerIds: number[],
    userRepo: Repository<UserModel>
  ) => {
    if (interviewerIds.length === 0) return new Map();

    const interviewers = await userRepo
      .createQueryBuilder("user")
      .where("user.id IN (:...ids)", { ids: interviewerIds })
      .andWhere("user.is_deleted = :isDeleted", { isDeleted: false })
      .select([
        "user.id as id",
        "user.first_name as firstName",
        "user.last_name as lastName",
        "user.email as email",
        "user.image as image",
      ])
      .getRawMany();

    return new Map(
      interviewers.map((interviewer) => [
        interviewer.id,
        {
          id: interviewer.id,
          name: `${interviewer.firstName} ${interviewer.lastName}`.trim(),
          email: interviewer.email,
          image: interviewer.image,
        },
      ])
    );
  };

  // Function to group candidates by job title and interviewers
  static groupCandidatesByJobTitleAndInterviewers = (
    hiredCandidates: any[],
    interviewersMap: Map<number, any>
  ) => {
    const candidateMap = new Map();

    hiredCandidates.forEach((item) => {
      const candidateKey = `${item.candidateName}_${item.jobTitle}`;
      const interviewer = interviewersMap.get(item.interviewerId) || {
        id: item.interviewerId,
        name: "Unknown Interviewer",
        email: null,
        image: null,
      };

      const interviewerWithRound = {
        ...interviewer,
        roundNumber: item.roundNumber,
      };

      if (candidateMap.has(candidateKey)) {
        candidateMap.get(candidateKey).interviewers.push(interviewerWithRound);
      } else {
        candidateMap.set(candidateKey, {
          candidateName: item.candidateName,
          candidateImageUrl: item.candidateImageUrl,
          jobTitle: item.jobTitle,
          overallSuccessProbability: item.overallSuccessProbability || null,
          hiredDate: item.hiredDate || null,
          jobApplicationId: item.jobApplicationId,
          interviewers: [interviewerWithRound],
        });
      }
    });

    return candidateMap;
  };
}

export default CandidateApplicationService;
