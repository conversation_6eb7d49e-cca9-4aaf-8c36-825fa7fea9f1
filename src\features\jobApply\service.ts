/* eslint-disable dot-notation */
import CryptoJS from "crypto-js";

import { Brackets } from "typeorm";
import { getSecretKeys } from "../../config/awsConfig";
import dbConnection from "../../db/dbConnection";
import envConfig from "../../config/envConfig";
import { JobsModel } from "../../schema/s9-innerview/jobs";
import {
  API_RESPONSE_MSG,
  DEFAULT_LIMIT,
  DEFAULT_OFFSET,
  DEFAULT_SORT,
  IS_ACTIVE,
  MESSAGE_TYPE,
  REDIS_EXPIRY,
  STATUS,
} from "../../utils/constants";
import { CandidateUploadResult } from "../resumeScreen/interface";
import ResumeScreenServices from "../resumeScreen/services";
import OrganizationModel from "../../schema/s9/organization";
import CandidatesModel from "../../schema/s9-innerview/candidates";

import JobApplicationsModel from "../../schema/s9-innerview/job_applications";
import { ResponseObject } from "../../interface/commonInterface";
import sendJobApplyVerificationMail from "../../utils/jobApplyVerificationEmail";
import Cache from "../../db/cache";
import { handleSentryError } from "../../utils/helper";

class JobApply {
  /**
   * Generate embeddable script tag for job portal
   * @param orgId Organization ID to encrypt
   * @returns Simple script tag that can be embedded on any website
   */
  static generateJobPortalScript = async (orgId: number) => {
    try {
      // First, check if the organization exists in the database
      const orgSource = await dbConnection.getS9DataSource();
      const organization = await orgSource
        .getRepository(OrganizationModel)
        .findOne({
          where: { id: orgId },
        });

      if (!organization) {
        return {
          success: false,
          message: API_RESPONSE_MSG.organization_not_found,
          code: 404,
        };
      }

      // Get encryption keys
      const keys = await getSecretKeys();

      // Encrypt the organization ID
      const orgIdString = orgId.toString();
      const encrypted = CryptoJS.AES.encrypt(
        orgIdString,
        keys.orgId_encryption_key
      ).toString();

      // Get URLs from environment configuration
      const CONFIG = envConfig();
      const scriptTagUrl = CONFIG.job_apply_script_api_url.replace(
        ":encryptedOrgId",
        encodeURIComponent(encrypted)
      );
      const scriptTag = `<script src="${scriptTagUrl}"></script>`;
      return {
        success: true,
        message: API_RESPONSE_MSG.script_tag_generated_successfully,
        code: 200,
        data: scriptTag,
      };
    } catch (error) {
      console.error("Error generating job portal script:", error);
      handleSentryError(error, "generateJobPortalScript");
      return null;
    }
  };

  /**
   * Get jobs for iframe display - Returns JavaScript that creates an iframe
   * @param req Express request object
   * @param res Express response object
   * @returns JavaScript code that creates an iframe
   */
  static loadIframe = async (req, res) => {
    console.log("enter loadIframe");

    try {
      const { orgId } = req.query;

      if (!orgId) {
        return res
          .status(400)
          .send("<html><body><h1>Invalid Organization ID</h1></body></html>");
      }

      console.log("enter loadIframe---111");

      // Get URLs from environment configuration
      const CONFIG = envConfig();
      // const backendUrl = CONFIG.job_apply_iframe_api_url;

      // Use the backend URL from config instead of hardcoding
      const iframeUrl = CONFIG.job_apply_iframe_api_url.replace(
        ":encryptedOrgId",
        encodeURIComponent(orgId)
      );

      // Create script that creates an iframe with all proper attributes
      const scriptContent = `
      (function() {
        // Create iframe element with proper attributes
        const iframe = document.createElement('iframe');
        iframe.src = '${iframeUrl}';
        iframe.id = 'stratum9JobPortal';
        iframe.style.position = 'fixed';
        iframe.style.top = '0';
        iframe.style.left = '0';
        iframe.style.width = '100vw';
        iframe.style.height = '100vh';
        iframe.style.border = 'none';
        iframe.style.margin = '0';
        iframe.style.padding = '0';
        iframe.style.zIndex = '9999';
        iframe.setAttribute('loading', 'lazy');
        iframe.setAttribute('allowfullscreen', 'true');
        iframe.setAttribute('allow', 'clipboard-write');
        iframe.setAttribute('title', 'Stratum9 Job Portal');

        // Remove scrollbars from body and html
        document.documentElement.style.overflow = 'hidden';
        document.body.style.overflow = 'hidden';
        document.body.style.margin = '0';
        document.body.style.padding = '0';

        // Find the script element that loaded this code
        const scriptTag = document.currentScript || (function() {
          const scripts = document.getElementsByTagName('script');
          return scripts[scripts.length - 1];
        })();

        // Insert the iframe right after the script tag
        if (scriptTag && scriptTag.parentNode) {
          scriptTag.parentNode.insertBefore(iframe, scriptTag.nextSibling);
        } else {
          // Fallback: append to body
          document.body.appendChild(iframe);
        }

        // Set up message passing
        iframe.onload = function() {
          iframe.contentWindow.postMessage({ id: '${orgId}' }, '*');
        };
      })();
        `;

      // Set all necessary headers to prevent CORS, CSP, and CORB issues
      res.setHeader("Content-Type", "application/javascript; charset=UTF-8");

      // CORS headers - use wildcard but no credentials
      res.setHeader("Access-Control-Allow-Origin", "*");
      res.setHeader("Access-Control-Allow-Methods", "GET, OPTIONS");
      res.setHeader(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept"
      );
      res.setHeader("Access-Control-Max-Age", "86400");

      // Security headers
      res.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
      res.setHeader("X-Content-Type-Options", "nosniff");
      res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");

      return res.send(scriptContent);
    } catch (error) {
      handleSentryError(error, "generateIframeJS");
      console.error("Error generating iframe JS:", error);
      res.setHeader("Content-Type", "application/javascript");
      return res
        .status(500)
        .send(
          "console.error('An error occurred while loading the job portal');"
        );
    }
  };

  /**
   * Serve content for iframe based on encrypted organization ID
   * @param req Express request object
   * @param res Express response object
   * @returns Either job portal content or error message
   */
  static IframeContent = async (req, res) => {
    try {
      const { orgId } = req.query;

      if (!orgId) {
        return res
          .status(400)
          .send(
            "<html><body><h1>Organization ID is required</h1></body></html>"
          );
      }

      const keys = await getSecretKeys();

      try {
        const idStr = orgId.toString();
        const decodedId = decodeURIComponent(idStr);
        const fixedId = decodedId.replace(/ /g, "+");

        // Now decrypt with the corrected Base64 string
        const bytes = CryptoJS.AES.decrypt(fixedId, keys.orgId_encryption_key);
        const decryptedText = bytes.toString(CryptoJS.enc.Utf8);

        if (
          !decryptedText ||
          decryptedText.trim() === "" ||
          Number.isNaN(parseInt(decryptedText, 10))
        ) {
          console.error("Invalid decrypted organization ID");
          return res
            .status(400)
            .send(
              "<html><body><h1>Invalid organization identifier</h1></body></html>"
            );
        }

        // Check if the organization exists in the database
        const orgSource = await dbConnection.getS9DataSource();
        const organization = await orgSource
          .getRepository(OrganizationModel)
          .findOne({
            where: { id: parseInt(decryptedText, 10) },
          });

        if (organization) {
          const CONFIG = envConfig();
          const encodedIdParam = encodeURIComponent(fixedId);
          const localPortalUrl = CONFIG.job_apply_website_url.replace(
            ":encryptedOrgId",
            encodedIdParam
          );

          res.setHeader("Access-Control-Allow-Origin", "*");
          res.setHeader("Cross-Origin-Resource-Policy", "cross-origin");
          res.setHeader(
            "Content-Security-Policy",
            "default-src * data: blob: filesystem: about: ws: wss: 'unsafe-inline' 'unsafe-eval'; " +
              "script-src * data: blob: 'unsafe-inline' 'unsafe-eval'; " +
              "connect-src * data: blob: 'unsafe-inline'; " +
              "img-src * data: blob: 'unsafe-inline'; " +
              "frame-src * data: blob:; " +
              "frame-ancestors *; " +
              "style-src * data: blob: 'unsafe-inline';"
          );

          const orgName = organization?.name || "Stratum9";
          const iframeHtml = `
          <!DOCTYPE html>
          <html lang="en">
          <head>
            <meta charset="UTF-8">
            <meta http-equiv="X-UA-Compatible" content="IE=edge">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${orgName} - Job Portal</title>
            <style>
              body, html { margin: 0; padding: 0; height: 100%; overflow: hidden; }
              iframe { width: 100%; height: 100%; border: none; }
            </style>
          </head>
          <body>
            <iframe src="${localPortalUrl}" allow="clipboard-write" allowfullscreen crossorigin="anonymous"></iframe>
          </body>
          </html>
        `;

          return res.send(iframeHtml);
        }
      } catch (error) {
        console.error("Error during decryption or database query:", error);
        return res
          .status(400)
          .send(
            "<html><body><h1>Invalid URL encoding in request</h1></body></html>"
          );
      }

      return res
        .status(404)
        .send(`<html><body><h1>Invalid Organization ID</h1></body></html>`);
    } catch (e) {
      handleSentryError(e, "generateIframeContent");
      console.error("Error in IframeContent:", e);
      return res
        .status(500)
        .send(
          `<html><body><h1>Error loading content</h1><p>${e.message}</p></body></html>`
        );
    }
  };

  /**
   * Get job list for an organization with pagination, filtering, and sorting
   */
  static getOrganizationJobList = async (query) => {
    try {
      let { organizationId } = query;
      const {
        offset = DEFAULT_OFFSET,
        limit = DEFAULT_LIMIT,
        searchTerm = "",
        filterTerm = "",
        sortOrder = DEFAULT_SORT,
      } = query;

      const keys = await getSecretKeys();

      const orgIdSecretKey = keys.orgId_encryption_key;
      const decryptedOrgId = CryptoJS.AES.decrypt(
        decodeURIComponent(organizationId.trim()),
        orgIdSecretKey
      );
      organizationId = decryptedOrgId.toString(CryptoJS.enc.Utf8);

      const jobSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = jobSource.getRepository(JobsModel);
      const orgSource = await dbConnection.getS9DataSource();

      const organization = await orgSource
        .getRepository(OrganizationModel)
        .findOne({
          where: { id: organizationId },
          select: ["id", "name"],
        });

      if (!organization) {
        return {
          success: false,
          message: API_RESPONSE_MSG.organization_not_found,
          code: 404,
        };
      }

      const queryBuilder = jobRepo
        .createQueryBuilder("job")
        .select([
          "job.id",
          "job.title",
          "job.isActive",
          "job.orgId",
          "job.location",
          "job.perksBenefits",
          "job.aboutCompany",
          "job.finalJobDescription",
          "job.roleOverview",
          "job.responsibilities",
        ])
        // Combine conditions with andWhere instead of multiple where
        .where("job.org_id = :organizationId", { organizationId })
        .andWhere("job.isActive = :isActive", { isActive: STATUS.ACTIVE });

      // Apply search term filter on job title if provided
      if (searchTerm) {
        queryBuilder.andWhere("LOWER(job.title) LIKE LOWER(:searchTerm)", {
          searchTerm: `%${searchTerm.toLowerCase()}%`,
        });
      }

      // Apply additional filter term if provided
      if (filterTerm) {
        queryBuilder.andWhere(
          new Brackets((qb) => {
            qb.where("LOWER(job.title) LIKE LOWER(:filterTerm)", {
              filterTerm: `%${filterTerm.trim().toLowerCase()}%`,
            })
              .orWhere("LOWER(job.description) LIKE LOWER(:filterTerm)", {
                filterTerm: `%${filterTerm.trim().toLowerCase()}%`,
              })
              .orWhere("LOWER(job.location) LIKE LOWER(:filterTerm)", {
                filterTerm: `%${filterTerm.trim().toLowerCase()}%`,
              });
          })
        );
      }

      // Apply sorting
      queryBuilder.orderBy("job.createdTs", sortOrder);

      // Apply pagination
      queryBuilder.skip(offset).take(limit);

      // Execute query
      const jobs = await queryBuilder.getMany();
      if (!jobs.length) {
        return {
          success: false,
          message: API_RESPONSE_MSG.no_jobs_found,
          code: 404,
        };
      }

      const jobsWithOrgs = jobs.map((job) => ({
        ...job,
        organizationName: organization.name,
      }));

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        code: 200,
        data: jobsWithOrgs,
      };
    } catch (error) {
      handleSentryError(error, "getOrganizationJobList");
      console.error("Error fetching organization job list:", error);
      return {
        success: false,
        message: error.message,
        code: 500,
      };
    }
  };

  /**
   * Apply for a job - Create a new job application
   * @param requestData Job application request data
   * @returns Response with job application details
   */

  static applyJob = async ({
    candidateName,
    gender,
    resume,
    email,
    jobId,
    otp,
  }): Promise<ResponseObject> => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const jobRepo = dataSource.getRepository(JobsModel);
      const candidateRepo = dataSource.getRepository(CandidatesModel);
      const jobApplicationRepo = dataSource.getRepository(JobApplicationsModel);

      const cache = new Cache();

      const getOtpFromRedis = await cache.get(
        `jobApplyOtp:${jobId}:${email.toLowerCase()}`
      );

      if (!getOtpFromRedis) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_expired,
        };
      }

      if (getOtpFromRedis !== otp.toString()) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_otp,
        };
      }

      // Check if job exists and is active
      const job = await jobRepo.findOne({
        where: { id: jobId, isActive: IS_ACTIVE },
      });

      if (!job) {
        return {
          success: false,
          message: API_RESPONSE_MSG.job_not_found,
        };
      }

      // Check if candidate exists
      const candidate = await candidateRepo.findOne({
        where: { email: email.toLowerCase(), orgId: job.orgId },
      });

      const results: CandidateUploadResult[] = [];
      // If candidate does not exist, create a new one
      if (!candidate) {
        await ResumeScreenServices.processNewCandidate(
          {
            name: candidateName,
            email,
            gender,
            resume_file: resume,
          },
          {
            organization_id: job.orgId,
            hiring_manager_id: null,
            job_id: jobId,
          },
          results,
          dataSource,
          candidateRepo,
          jobApplicationRepo
        );
      } else {
        await ResumeScreenServices.processExistingCandidateApplication(
          candidate,
          {
            name: candidateName,
            email,
            gender,
            resume_file: resume,
          },
          {
            organization_id: job.orgId,
            hiring_manager_id: null,
            job_id: jobId,
          },
          results,
          dataSource,
          jobApplicationRepo
        );
      }

      if (results && results.length > 0 && results[0].success) {
        return {
          success: true,
          message: API_RESPONSE_MSG.application_submitted_successfully,
        };
      }

      return {
        success: false,
        message: API_RESPONSE_MSG.failed_to_submit_job_application,
      };
    } catch (error) {
      handleSentryError(error, "applyJob");
      console.error("Error applying for job:", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed_to_submit_job_application,
        error: error.message,
      };
    }
  };

  /**
   * Send OTP to job applied candidate
   * @param data - Object containing email and jobId
   * @returns An object with success status, message, and data (if applicable)
   */
  static sendOtpToJobAppliedCandidate = async ({
    email,
    jobId,
    orgId,
  }: {
    email: string;
    jobId: number;
    orgId: string;
  }) => {
    try {
      const keys = await getSecretKeys();
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const candidateRepository = dataSource.getRepository(CandidatesModel);
      const orgSource = await dbConnection.getS9DataSource();
      // Find the organization

      const candidateWithApplication = await candidateRepository
        .createQueryBuilder("candidate")
        .leftJoinAndSelect(
          JobApplicationsModel,
          "job_application",
          "job_application.candidateId = candidate.id AND job_application.jobId = :jobId",
          { jobId }
        )
        .where("LOWER(candidate.email) = :email", {
          email: email.toLowerCase(),
        })
        .getOne();

      console.log("candidateWithApplication---->>>>", candidateWithApplication);

      const idStr = orgId.toString();
      const decodedId = decodeURIComponent(idStr);
      const fixedId = decodedId.replace(/ /g, "+");

      // Now decrypt with the corrected Base64 string
      const bytes = CryptoJS.AES.decrypt(fixedId, keys.orgId_encryption_key);
      const decryptedText = bytes.toString(CryptoJS.enc.Utf8);
      const organization = await orgSource
        .getRepository(OrganizationModel)
        .findOne({
          where: { id: parseInt(decryptedText, 10) },
        });

      if (candidateWithApplication) {
        return {
          success: false,
          message: API_RESPONSE_MSG.already_applied_for_this_job,
        };
      }

      console.log("organization----->>>", organization);

      const { otp, response } = await sendJobApplyVerificationMail({
        email,
        orgName: organization.name,
        logo: organization.logo || null,
      });
      // Encrypt the OTP using a secret key
      if ((await response).message === MESSAGE_TYPE.SENT) {
        // Store OTP in Redis with 5 minutes expiry, key: jobId+email
        const cache = new Cache();

        await cache.set(
          `jobApplyOtp:${jobId}:${email.toLowerCase()}`,
          otp.toString(),
          REDIS_EXPIRY.APPLY_JOB_CANDIDATE_OTP_EXPIRY_TIME
        );

        return {
          success: true,
          message: API_RESPONSE_MSG.verification_code_sent,
          data: null,
        };
      }

      // Return a default response if message was not sent
      return {
        success: false,
        message: API_RESPONSE_MSG.otp_sending_failed,
        data: null,
      };
    } catch (error) {
      handleSentryError(error, "sendOtpToJobAppliedCandidate");
      return {
        success: false,
        message: API_RESPONSE_MSG.something_went_wrong,
        error: error.message,
      };
    }
  };

  /**
   * Verify OTP for job applied candidate
   * @param data - Object containing email, otp, and jobId
   * @returns An object with success status, message, and data (if applicable)
   */

  static verifyJobAppliedCandidateOtp = async (data: {
    email: string;
    otp: string;
    jobId: number;
  }) => {
    try {
      const { email, otp, jobId } = data;

      if (!otp || !email || !jobId) {
        return {
          success: false,
          message: API_RESPONSE_MSG.invalid_data,
        };
      }

      const cache = new Cache();

      const getOtpFromRedis = await cache.get(
        `jobApplyOtp:${jobId}:${email.toLowerCase()}`
      );

      if (!getOtpFromRedis) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_expired,
        };
      }

      if (getOtpFromRedis !== otp.toString()) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_otp,
        };
      }
      return {
        success: true,
        message: API_RESPONSE_MSG.otp_verified,
      };
    } catch (error) {
      handleSentryError(error, "verifyJobAppliedCandidateOtp");
      return {
        success: false,
        message: API_RESPONSE_MSG.something_went_wrong,
        error: error.message,
      };
    }
  };
}

export default JobApply;
