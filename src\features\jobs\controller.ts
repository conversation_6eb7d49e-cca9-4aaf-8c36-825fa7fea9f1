import { Request, Response } from "express";

import { JobServices } from "./services";
import { IJobGenerationRequest } from "./interface";
import {
  API_RESPONSE_MSG,
  DEFAULT_LIMIT,
  PDF_CONTENT_TYPE,
} from "../../utils/constants";
import { handleSentryError, parsePdfWithRetries, parsePdfAdvanced } from "../../utils/helper";

/**
/**
 * Generate job skills from the job description using GPT
 * Extracts top skills in different categories:
 * - Top 5 career based skills
 * - Top 10 role specific performance based skills
 * - Top 5 culture specific performance based skills
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const generateJobSkills = async (
  req: Request,
  res: Response
): Promise<void> => {
  try {
    // Include all job details from the request body
    const requestData: IJobGenerationRequest = req.body;
    // Pass all the job details to the service
    const data = await JobServices.generateJobSkills({
      ...requestData,
      user_id: req.userId,
      org_id: req.orgId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "generateJobSkills");
    res.status(500).json({
      success: false,
      message: error.message || "An error occurred while generating job skills",
      code: 500,
    });
  }
};
/**
 * Retrieves dashboard counts for jobs associated with a specific organization and user
 *
 * @param req - Express request object containing organizationId and userId as query parameters
 * @param res - Express response object
 * @returns JSON response with total and active job counts
 *
 * @example
 * // Success response
 * {
 *   success: true,
 *   message: "Dashboard counts fetched successfully",
 *   data: {
 *     totalJobs: 10,
 *     activeJobs: 5
 *   },
 *   code: 200
 * }
 */
/**
 * Controller to handle fetching dashboard statistics for a user in an organization.
 *
 * Extracts `organizationId` and `userId` from query parameters and validates them.
 * Delegates to `JobServices.getDashboardCounts` to retrieve total jobs, active jobs,
 * and on-hold job applications. Returns the result as a JSON response.
 *
 * @param req - Express request object containing query parameters `organizationId` or `org_id`,
 *              and `userId` or `user_id`.
 * @param res - Express response object used to send the JSON response.
 * @returns JSON response with success status, dashboard data or error message.
 */
export const getDashboardCounts = async (req: Request, res: Response) => {
  try {
    const counts = await JobServices.getDashboardCounts(req.orgId, req.userId);

    return res.status(200).json({
      success: true,
      message: API_RESPONSE_MSG.success,
      data: counts,
      code: 200,
    });
  } catch (error: any) {
    handleSentryError(error, "getDashboardCounts");
    return res.status(500).json({
      success: false,
      message: API_RESPONSE_MSG.failed,
      code: 500,
    });
  }
};

/**
 * Updates the active status of a job
 *
 * @param req - Express request object containing job id as route parameter and status in request body
 * @param res - Express response object
 * @returns JSON response indicating success or failure of the operation
 *
 * @example
 * // Success response
 * {
 *   success: true,
 *   message: "Job with id 123 activated successfully",
 *   code: 200
 * }
 *
 * @example
 * // Error response - Job not found
 * {
 *   success: false,
 *   message: "Job with id 999 not found",
 *   code: 404
 * }
 */
export const updateJob = async (req: Request, res: Response) => {
  try {
    const jobId = Number(req.params.id);
    if (Number.isNaN(jobId)) {
      return res.status(400).json({
        success: false,
        message: "Invalid job id",
        code: 400,
      });
    }

    const result = await JobServices.updateJob(
      jobId,
      req.orgId,
      req.body.status
    );

    if (result.success) {
      return res.status(200).json({ ...result, code: 200 });
    }

    return res.status(404).json({ ...result, code: 404 });
  } catch (error: any) {
    handleSentryError(error, "updateJob");
    return res.status(500).json({
      success: false,
      message: error.message || "Failed to deactivate job",
      code: 500,
    });
  }
};

/**
 * Get job metadata: id, title, created_ts
 */
/**
 * Controller to fetch paginated job metadata (id, title, created timestamp, and active status).
 *
 * Parses query parameters `page`, `limit`, `searchStr`, and `isActive` to construct a call to
 * `JobServices.getAllJobsMeta`. The function handles optional filtering by job title and
 * active status, and supports pagination.
 *
 * @param req - Express request object containing query parameters:
 *   - `page` (optional): The current page index (defaults to 0).
 *   - `limit` (optional): Number of items per page (defaults to 10).
 *   - `searchStr` (optional): Search keyword for job titles (case-insensitive).
 *   - `isActive` (optional): Boolean string to filter by job active status.
 *
 * @param res - Express response object used to send the JSON response.
 * @returns JSON response with job metadata or an error message.
 */
export const getAllJobsMeta = async (req: Request, res: Response) => {
  try {
    const { page, limit, searchStr = "", isActive } = req.query;

    // Parse and validate
    const offset = Number(page) || 0;
    const parsedLimit = Number(limit) || DEFAULT_LIMIT;
    const parsedSearch = String(searchStr);

    // Parse isActive to boolean if it exists
    let parsedIsActive: boolean | undefined;
    if (isActive !== undefined) {
      parsedIsActive = isActive === "true";
    }

    const data = await JobServices.getAllJobsMeta(
      req.orgId,
      offset,
      parsedSearch,
      parsedIsActive,
      parsedLimit
    );

    res.status(200).json({ ...data, code: 200 });
  } catch (error: any) {
    handleSentryError(error, "getAllJobsMeta");
    res.status(500).json({
      success: false,
      message: error.message || "An error occurred while fetching job metadata",
      code: 500,
    });
  }
};

/**
 * Process uploaded PDF job description to extract data and get pre-signed URL
 *
 * This function handles the file upload, parses the PDF content, extracts structured
 * job information using GPT, and returns the data to be used in form fields.
 *
 * @param {Request} req - The HTTP request object with the uploaded PDF file
 * @param {Response} res - The HTTP response object
 */
export const getJobDescUploadUrl = async (
  req: Request,
  res: Response
): Promise<Response> => {
  try {
    // Check if file was uploaded
    if (!req.file || !req.file.buffer) {
      return res.status(400).json({
        success: false,
        message: API_RESPONSE_MSG.no_pdf_file_uploaded,
        code: 400,
      });
    }
    const { orgId } = req;
    const timestamp = Date.now(); // Add timestamp to make filenames unique
    const filePath = `${orgId}/job-description/${req.file.originalname}_${timestamp}.pdf`;
    try {
      const bufferCopy = Buffer.from(req.file.buffer);

      // Try advanced PDF parsing first for better template resume support
      let pdfParsed;
      try {
        const advancedResult = await parsePdfAdvanced(bufferCopy, {
          extractImages: false, // Don't extract images for job descriptions
          useOcr: true,         // Use OCR for template resumes
          ocrLanguage: 'eng'
        });

        if (advancedResult && advancedResult.text.trim()) {
          pdfParsed = {
            text: advancedResult.text,
            metadata: advancedResult.metadata,
            extractionMethod: advancedResult.extractionMethod
          };
          console.log(`PDF parsed using ${advancedResult.extractionMethod} method`);
        }
      } catch (advancedError) {
        console.log("Advanced parsing failed, falling back to basic parsing:", advancedError);
      }

      // Fallback to basic parsing if advanced parsing failed
      if (!pdfParsed) {
        pdfParsed = await parsePdfWithRetries(bufferCopy);
      }

      if (!pdfParsed || !pdfParsed.text.trim()) {
        return res.status(422).json({
          success: false,
          message: API_RESPONSE_MSG.job_description_processed_failed,
          error: "pdf parsing failed - unable to extract text from PDF",
          code: 422,
        });
      }

      // Process the PDF and get form field data using GPT
      const result = await JobServices.gettingPreSignedUrlAndData(
        filePath,
        pdfParsed,
        PDF_CONTENT_TYPE
      );
      console.log("result=============>", result);
      // Return the data to the frontend
      return res.status(200).json({
        success: true,
        data: {
          formFields: result.formFields || {}, // Extracted form fields from GPT,
          jd_link: result.jd_link,
        },
        message: API_RESPONSE_MSG.job_description_processed_successfully,
        code: 200,
      });
    } catch (pdfError) {
      handleSentryError(pdfError, "getJobDescUploadUrl 1");
      return res.status(422).json({
        success: false,
        message: API_RESPONSE_MSG.job_description_processed_failed,
        error: pdfError.message,
        code: 422,
      });
    }
  } catch (error) {
    handleSentryError(error, "getJobDescUploadUrl 2");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Get all skills from the database categorized by type
 * Retrieves all available skills from the database with caching
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 * @returns {Promise<void>} - Returns a JSON response with skills data
 */
export const getSkills = async (req: Request, res: Response) => {
  try {
    const data = await JobServices.getSkills();
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getSkills");
    res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Generate job requirement based on all job details and skills
 * This endpoint receives the combined job details and skills data
 * and simply prints it out for now
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
export const generateJobRequirement = async (req: Request, res: Response) => {
  try {
    // Get all job details and skills from the request body
    const requestData = req.body;
    const data = await JobServices.generateJobRequirement({
      ...requestData,
      user_id: req.userId,
      org_id: req.orgId,
    });
    // Just print the request data for now

    // Send a success response
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "generateJobRequirement");
    res.status(500).json({
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

export const saveJobDetails = async (req: Request, res: Response) => {
  try {
    const requestData = req.body;
    const data = await JobServices.saveJobDetails({
      ...requestData,
      user_id: req.userId,
      org_id: req.orgId,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "saveJobDetails");
    res.status(500).json({
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Get job HTML description by job ID
 * Retrieves the HTML description for a specific job
 *
 * @param {Request} req - The HTTP request object containing the job ID as a parameter
 * @param {Response} res - The HTTP response object
 */
export const getJobHtmlDescription = async (req: Request, res: Response) => {
  try {
    const jobId = Number(req.query.id);

    const data = await JobServices.getJobHtmlDescription(jobId, req.orgId);

    if (!data.success) {
      return res.status(404).json({
        ...data,
        code: 404,
      });
    }

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getJobHtmlDescription");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Update job description by job ID
 * Updates the HTML description for a specific job
 *
 * @param {Request} req - The HTTP request object containing the job ID and HTML description as parameters
 * @param {Response} res - The HTTP response object
 */
export const updateJobDescription = async (req: Request, res: Response) => {
  try {
    const htmlData = req.body;
    const data = await JobServices.updateJobDescription(htmlData, req.orgId);

    if (!data.success) {
      return res.status(404).json({
        ...data,
        code: 404,
      });
    }

    return res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateJobDescription");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

export const generatePdf = async (req: Request, res: Response) => {
  try {
    const { editorContent, jobTitle } = req.body;
    const data = await JobServices.generatePdf(editorContent, jobTitle);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "generatePdf");
    res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};
