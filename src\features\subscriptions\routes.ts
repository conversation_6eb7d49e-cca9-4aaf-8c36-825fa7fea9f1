import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  getCurrentSubscription,
  getAllPlans,
  cancelSubscription,
  getAllTransactions,
  buySubscription,
  getJobPostingQuota,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import { authorizedForManageSubscriptions } from "../../middleware/isAuthorized";
import { buySubscriptionValidation } from "./validation";
import { schemaValidation } from "../../middleware/validateSchema";

const subscriptionRoutes = express.Router();

/**
 * @swagger
 * /subscription/current:
 *   get:
 *     summary: Get Current Subscription
 *     description: Returns the current active subscription details for the authenticated organization.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Current subscription retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: current_subscription_retrieved_successfully
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     orgSubscriptionId:
 *                       type: number
 *                       example: 631
 *                     startDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-08-29T13:12:03.000Z"
 *                     expiryDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-29T13:12:03.000Z"
 *                     nextBillingDate:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-29T13:12:03.000Z"
 *                     status:
 *                       type: string
 *                       enum: [active, canceled, past_due, unpaid, cancel_at_period_end]
 *                       example: "cancel_at_period_end"
 *                     subscriptionPlanId:
 *                       type: number
 *                       example: 4
 *                     subscriptionPlanName:
 *                       type: string
 *                       example: "Enterprise"
 *                     subscriptionPlanDescription:
 *                       type: string
 *                       example: "Access to all job postings and resume uploads."
 *                     subscriptionPlanPaymentType:
 *                       type: string
 *                       enum: [Free, Paid]
 *                       example: "Paid"
 *                     pricingId:
 *                       type: number
 *                       example: 3
 *                     price:
 *                       type: string
 *                       example: "140.00"
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       403:
 *         description: Forbidden - Insufficient permissions to access this resource
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden"
 *                 code:
 *                   type: number
 *                   example: 403
 *       404:
 *         description: No active subscription found for this organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No active subscription found for this organization"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.CURRENT,
  auth,
  HandleErrors(getCurrentSubscription)
);

/**
 * @swagger
 * /subscription/all:
 *   get:
 *     summary: Get All Available Subscription Plans
 *     description: Returns a list of all available subscription plans with their features and pricing options.
 *     tags:
 *       - Subscription Routes
 *     responses:
 *       200:
 *         description: Available plans retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "all_available_plans_retrieved_successfully"
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       subscriptionPlanId:
 *                         type: number
 *                         example: 2
 *                       subscriptionPlanName:
 *                         type: string
 *                         example: "Professional Plan"
 *                       subscriptionPlanDescription:
 *                         type: string
 *                         example: "Access to limited job postings and resume uploads."
 *                       subscriptionPlanBenefits:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             slug:
 *                               type: string
 *                               example: "job_postings"
 *                             text:
 *                               type: string
 *                               example: "Up to 100"
 *                             value:
 *                               type: number
 *                               example: 100
 *                             feature:
 *                               type: string
 *                               example: "Job Postings"
 *                             is_active:
 *                               type: boolean
 *                               example: true
 *                             description:
 *                               type: string
 *                               example: "Create, manage, and publish job postings across various platforms."
 *                       subscriptionPlanPaymentType:
 *                         type: string
 *                         enum: [Free, Paid]
 *                         example: "Paid"
 *                       pricingId:
 *                         type: number
 *                         nullable: true
 *                         example: 1
 *                       price:
 *                         type: string
 *                         nullable: true
 *                         example: "100.00"
 *       500:
 *         description: Server error while retrieving plans
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.ALL_PLANS,
  auth,
  authorizedForManageSubscriptions,
  HandleErrors(getAllPlans)
);

/**
 * @swagger
 * /subscription/cancel:
 *   post:
 *     summary: Cancel Subscription
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties: {}
 *             required: []
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Old plan successfully canceled. You will be upgraded to the new plan."
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     updatedSubscription:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: number
 *                           example: 673
 *                         organizationId:
 *                           type: number
 *                           example: 358
 *                         subscriptionId:
 *                           type: number
 *                           example: 4
 *                         subscriptionPricingId:
 *                           type: number
 *                           example: 3
 *                         subscriptionType:
 *                           type: string
 *                           example: "Monthly"
 *                         isActive:
 *                           type: boolean
 *                           example: true
 *                         startDate:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-09-15T09:58:11.000Z"
 *                         expiryDate:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-10-15T09:58:11.000Z"
 *                         nextBillingDate:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-10-15T09:58:11.000Z"
 *                         stripeSubscriptionId:
 *                           type: string
 *                           example: "sub_1S7ZAmC6js4EKovGf5fot3AU"
 *                         status:
 *                           type: string
 *                           example: "cancel_at_period_end"
 *                         cancellationDate:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-09-17T13:25:11.000Z"
 *                         createdTs:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-09-15T09:58:11.000Z"
 *                         updatedTs:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-09-17T13:25:12.433Z"
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.CANCEL,
  auth,
  authorizedForManageSubscriptions,
  // schemaValidation(cancelSubscriptionValidation),
  HandleErrors(cancelSubscription)
);

/**
 * @swagger
 * /subscription/transactions:
 *   get:
 *     summary: Get Transaction Details
 *     description: Returns all transaction details for the authenticated user's organization
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Transaction details retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     transactions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: number
 *                             example: 627
 *                           payment_status:
 *                             type: string
 *                             enum: [Success, Pending, Failed]
 *                             example: "Success"
 *                           amount:
 *                             type: string
 *                             example: "140.00"
 *                           transaction_type:
 *                             type: string
 *                             enum: [Purchase, Refund, Upgrade]
 *                             example: "Purchase"
 *                           transaction_method:
 *                             type: string
 *                             example: "Card"
 *                           transaction_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2025-07-13T16:00:00.000Z"
 *                           invoice_id:
 *                             type: string
 *                             example: "in_1234567890"
 *                           invoice_url:
 *                             type: string
 *                             example: "https://dashboard.stripe.com/invoices/in_1234567890"
 *                           plan_name:
 *                             type: string
 *                             example: "Enterprise"
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         total:
 *                           type: number
 *                           example: 158
 *                         offset:
 *                           type: number
 *                           example: 0
 *                         limit:
 *                           type: number
 *                           example: 15
 *                         hasMore:
 *                           type: boolean
 *                           example: true
 *                 code:
 *                   type: number
 *                   example: 200
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       404:
 *         description: No active subscription found for this organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No active subscription found for this organization"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.TRANSACTIONS,
  auth,
  HandleErrors(getAllTransactions)
);

/**
 * @swagger
 * /subscription/buy-subscription:
 *   post:
 *     summary: Buy Subscription Plan
 *     description: Initiates a subscription purchase by validating plan details, ensuring Stripe customer exists, and creating a checkout session. This is a streamlined API that combines validation, customer creation, and checkout session creation into a single endpoint.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - planId
 *               - pricingId
 *             properties:
 *               planId:
 *                 type: integer
 *                 description: The ID of the subscription plan to purchase
 *                 example: 2
 *               pricingId:
 *                 type: integer
 *                 description: The ID of the pricing option (monthly/yearly)
 *                 example: 1
 *     responses:
 *       200:
 *         description: Subscription purchase initiated successfully - checkout session created
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Subscription purchase initiated successfully. Please complete the checkout process."
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     sessionId:
 *                       type: string
 *                       description: Stripe checkout session ID
 *                       example: "cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
 *                     checkoutUrl:
 *                       type: string
 *                       description: Stripe checkout session URL to redirect user
 *                       example: "https://checkout.stripe.com/c/pay/cs_test_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0"
 *                     planName:
 *                       type: string
 *                       description: Name of the selected subscription plan
 *                       example: "Professional Plan"
 *                     price:
 *                       type: number
 *                       description: Price of the selected plan in cents
 *                       example: 4999
 *                     stripeCustomerId:
 *                       type: string
 *                       description: Stripe customer ID for the organization
 *                       example: "cus_1234567890abcdef"
 *       400:
 *         description: Bad request - Invalid plan ID, pricing ID, or validation failed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   examples:
 *                     invalid_plan:
 *                       value: "Invalid plan ID or plan not found"
 *                     invalid_pricing:
 *                       value: "Invalid pricing ID or pricing not found"
 *                     same_plan:
 *                       value: "You are already subscribed to this plan"
 *                     inactive_plan:
 *                       value: "Selected plan is not active"
 *                 data:
 *                   type: null
 *                   example: null
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       404:
 *         description: Organization not found or no Stripe customer exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Organization not found or no Stripe customer exists"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error during subscription purchase process
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
subscriptionRoutes.post(
  ROUTES.SUBSCRIPTION.BUY_SUBSCRIPTION,
  auth,
  authorizedForManageSubscriptions,
  schemaValidation(buySubscriptionValidation),
  HandleErrors(buySubscription)
);

/**
 * @swagger
 * /subscription/job-posting-quota:
 *   get:
 *     summary: Get Job Posting Quota
 *     description: Returns the current job posting quota information for the authenticated organization, including remaining quota, subscription plan details, and quota status.
 *     tags:
 *       - Subscription Routes
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Job posting quota retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Job posting quota retrieved successfully"
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     currentQuota:
 *                       type: number
 *                       description: Current quota limit (-1 for unlimited, 0 for exhausted, >0 for available)
 *                       example: 5
 *                     status:
 *                       type: string
 *                       enum: [available, exhausted, unlimited]
 *                       example: "available"
 *                       description: Current quota status
 *       400:
 *         description: Job posting quota exhausted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "You have reached your job posting limit. Please upgrade your plan."
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     currentQuota:
 *                       type: number
 *                       example: 0
 *                     status:
 *                       type: string
 *                       example: "exhausted"
 *       401:
 *         description: Unauthorized - Missing or invalid authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       404:
 *         description: No subscription benefits found for this organization
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No subscription benefits found for this organization"
 *                 data:
 *                   type: null
 *                   example: null
 *       500:
 *         description: Server error while retrieving job posting quota
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to retrieve job posting quota"
 *                 data:
 *                   type: null
 *                   example: null
 */
subscriptionRoutes.get(
  ROUTES.SUBSCRIPTION.JOB_POSTING_QUOTA,
  auth,
  HandleErrors(getJobPostingQuota)
);

export default subscriptionRoutes;
