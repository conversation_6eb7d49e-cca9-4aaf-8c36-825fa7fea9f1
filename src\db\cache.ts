/* eslint-disable class-methods-use-this */
/* eslint-disable no-return-await */
import { createClient, RedisClientType } from "redis";
import { REDIS_EXPIRY } from "../utils/constants";
import { getSecretKeys } from "../config/awsConfig";
import { handleSentryError } from "../utils/helper";

export default class Cache {
  /**
   * The redis client instance for the cache
   */
  private static redisClient: RedisClientType | null = null;

  private static enableCaching: boolean;

  constructor() {
    Cache.enableCaching = true;
  }

  /**
   * Initialize the redis client and set it to the class instance variable
   */
  private static async init() {
    try {
      // if (this.redisClient) return;

      const keys = await getSecretKeys();

      Cache.redisClient = createClient({
        url: keys.redis_db_endpoint,
      });

      Cache.redisClient.on("error", (err) =>
        // eslint-disable-next-line no-console
        console.log("Redis Client Error: ", err)
      );

      await Cache.redisClient.connect();
    } catch (error) {
      handleSentryError(error, "Cache init");
      // eslint-disable-next-line no-console
      console.log("Error in configuring redis client =>>>>>>>>", error);
    }
  }

  /**
   * Set a key value pair in the cache with an expiry time in seconds (default 1 hour)
   * @param {string} key - The key to set in the cache (string)
   * @param {string} value - The value to set in the cache (string)
   * @param {number} expiry - The expiry time in seconds (default 1 hour)
   */
  async set(key: string, value: string, expiry: number = REDIS_EXPIRY.DEFAULT) {
    if (!Cache.enableCaching) return;

    if (Cache.redisClient) {
      await Cache.redisClient.set(key, value, { EX: expiry });
    } else {
      await Cache.init();
      await this.set(key, value, expiry);
    }
  }

  /**
   * Get a value from the cache by key
   * @param {string} key - The key to get from the cache (string)
   * @returns {Promise<string | null>} - The value from the cache (string) or null if the key does not exist
   */
  async get(key: string): Promise<string | null> {
    if (!Cache.enableCaching) return null;

    if (Cache.redisClient) {
      return Cache.redisClient.get(key);
    }
    await Cache.init();
    return this.get(key);
  }

  /**
   * Delete a key from the cache by key
   * @param {string} key - The key to delete from the cache (string)
   * @returns {Promise<void>} - A promise that resolves when the key is deleted from the cache
   */
  async del(key: string): Promise<void> {
    if (!Cache.enableCaching) return;

    if (Cache.redisClient) {
      await Cache.redisClient.del(key);
    } else {
      await Cache.init();
      await this.del(key);
    }
  }

  /**
   * Remove a specific value from a list stored at the given key
   * @param {string} key - The key of the list in Redis
   * @param {string} value - The value to remove from the list
   * @param {number} count - The number of occurrences to remove (0 for all occurrences)
   */
  async lRem(key: string, value: string, count: number = 0): Promise<void> {
    if (!Cache.enableCaching) return;

    if (Cache.redisClient) {
      await Cache.redisClient.lRem(key, count, value);
    } else {
      await Cache.init();
      await this.lRem(key, value, count);
    }
  }

  /**
   * Push a value to the left of the list stored at the given key
   * @param {string} key - The key of the list in Redis
   * @param {string} value - The value to push to the list
   */
  async lPush(key: string, value: string): Promise<void> {
    if (!Cache.enableCaching) return;

    if (Cache.redisClient) {
      await Cache.redisClient.lPush(key, value);
    } else {
      await Cache.init();
      await this.lPush(key, value);
    }
  }

  /**
   * Get the entire list stored at the given key
   * @param {string} key - The key of the list in Redis
   * @returns {Promise<string[]>} - The list of strings stored at the given key
   */
  async lRange(key: string): Promise<string[]> {
    if (!Cache.enableCaching) return [];

    if (Cache.redisClient) {
      return Cache.redisClient.lRange(key, 0, -1);
    }
    await Cache.init();
    return this.lRange(key);
  }
}
