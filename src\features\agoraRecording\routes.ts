/**
 * Agora Recording Routes
 * Defines API endpoints for Agora Cloud Recording and Speech-to-Text
 */
import express from "express";
import { sanitizeBody } from "../../middleware/sanitize";
import HandleErrors from "../../middleware/handleError";
import {
  createAgoraToken,
  startMeetingRecording,
  stopMeetingRecording,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import {
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  createTokenSchema,
  startRecordingSchema,
  stopRecordingSchema,
} from "./validation";

const router = express.Router();

/**
 * @swagger
 * /agora-recordings/meetings/start:
 *   post:
 *     summary: Start recording a meeting
 *     description: Starts cloud recording for a specific meeting
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - channelName
 *               - interviewId
 *             properties:
 *               channelName:
 *                 type: string
 *                 description: Agora channel name for the meeting
 *                 example: "interview_cxdffgd_302944"
 *               interviewId:
 *                 type: string
 *                 description: ID of the interview
 *                 example: "402"
 *     responses:
 *       200:
 *         description: Recording started successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Recording started successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     resourceId:
 *                       type: string
 *                       description: Resource ID acquired from Agora
 *                       example: "IslAfVvHGTa87QcHHxnSkO7wtfjcM8FjKXv804acDhu3YAbNh9wBJK8LWCSY9kWvZvez0OmyBECsrlc0De_zxMKgj7RybuYDAHJKiiR-1JaskfNQ7_smsqnt1kCi5cBCzy3MjC9qcxRP0KODTKZ-XscY4CE9Y4iiUl-hqcUIpsSNwoQG_xlTLQn0n8gAsPGd4mWYvIB2Ki9WoO2AtWXUtYvJ8NFH14PNDHyfeqaO2y0"
 *                     sid:
 *                       type: string
 *                       description: Session ID for the recording
 *                       example: "8b2dbb8654439ff66d3092ae24b315c2"
 *                     uid:
 *                       type: number
 *                       description: User ID for the recording
 *                       example: 829246
 *                     mode:
 *                       type: string
 *                       description: Recording mode
 *                       example: "mix"
 *                     channelName:
 *                       type: string
 *                       description: Channel name being recorded
 *                       example: "interview_cxdffgd_302944"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request parameters"
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error while starting recording
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error while starting recording"
 *                 code:
 *                   type: number
 *                   example: 500
 */
router.post(
  ROUTES.AGORA.START_MEETING_RECORDING,
  sanitizeBody(),
  schemaValidation(startRecordingSchema),
  HandleErrors(startMeetingRecording)
);

/**
 * @swagger
 * /agora-recordings/meetings/stop:
 *   post:
 *     summary: Stop recording a meeting
 *     description: Stops an active cloud recording for a specific meeting
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - resourceId
 *               - sid
 *               - channelName
 *               - uid
 *             properties:
 *               resourceId:
 *                 type: string
 *                 description: Resource ID acquired from Agora
 *                 example: "1s4suCjYvr1LK7IjX8R0nY8Oghwp-yCEXOi-qBS1_k8LPchxXf9qkvexFCDwCdBqmu4duHvH92OI1_TICUUqp1B_eyr982YpGkyOUHfKRf82qYM3CFW81WsWpTtJSJuvwvJcHTYUV0RoFtRHdz95316a7twsNWpG10RKHP2YYEh4_t47Ud1RrcbZd1WhXk38DwhVBGrpwpBCpkNZTodk71J6UjlXp1DbnbvThVadI3A"
 *               sid:
 *                 type: string
 *                 description: Session ID for the recording
 *                 example: "f884a1b8c540e715153c01a78a49d442"
 *               uid:
 *                 type: string
 *                 description: Unique ID for the recording service
 *                 example: "276856"
 *               mode:
 *                 type: string
 *                 enum: [mix, individual]
 *                 default: mix
 *                 description: Recording mode
 *                 example: "mix"
 *               channelName:
 *                 type: string
 *                 description: Channel name being recorded
 *                 example: "interview_cxdffgd_302944"
 *     responses:
 *       200:
 *         description: Recording stopped successfully
 *       400:
 *         description: Missing required parameters
 *       500:
 *         description: Server error while stopping recording
 */
router.post(
  ROUTES.AGORA.STOP_MEETING_RECORDING,
  sanitizeBody(),
  schemaValidation(stopRecordingSchema),
  HandleErrors(stopMeetingRecording)
);

/**
 * @swagger
 * /agora-recordings/create-token:
 *   get:
 *     summary: Create Agora token
 *     description: Creates a token for Agora Cloud Recording service
 *     parameters:
 *       - in: query
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the interview to create token for
 *         example: "402"
 *       - in: query
 *         name: personType
 *         required: true
 *         schema:
 *           type: string
 *         description: Type of person (interviewer or candidate)
 *         example: "interviewer"
 *       - in: query
 *         name: channelName
 *         required: true
 *         schema:
 *           type: string
 *         description: Name of the channel to create token for
 *         example: "interview_cxdffgd_302944"
 *       - in: query
 *         name: interviewerId
 *         required: false
 *         schema:
 *           type: string
 *         description: ID of the interviewer (required for interviewer type)
 *         example: "909"
 *     responses:
 *       200:
 *         description: Token created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "token_generated_successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     interviewId:
 *                       type: integer
 *                       description: ID of the interview
 *                       example: 402
 *                     channelName:
 *                       type: string
 *                       description: Name of the Agora channel
 *                       example: "interview_cxdffgd_302944"
 *                     uid:
 *                       type: integer
 *                       description: User ID for the token
 *                       example: 285645
 *                     token:
 *                       type: string
 *                       description: Generated Agora token
 *                       example: "007eJxTYFC79cL8YvHEiIpzVQc5vX46ONxefVnFRnjVc/kprU/VTnMpMBgnGZkkGSUnGxuaGpqYGFtYmJiZGyeZpqYYGRokWpqliH84ncHAfEaIuSGVgZGBkYGFgZEBxGcCk8xgkgVMSjBk5pWkFpVlppbHJ1ekpKWlp8QbGxhZmpiwMRhZmJqZmAIAj6oqDg=="
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request parameters"
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error while creating token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error while creating token"
 *                 code:
 *                   type: number
 *                   example: 500
 */
router.get(
  ROUTES.AGORA.CREATE_TOKEN,
  sanitizeBody(),
  queryValidation(createTokenSchema),
  HandleErrors(createAgoraToken)
);

export default router;
