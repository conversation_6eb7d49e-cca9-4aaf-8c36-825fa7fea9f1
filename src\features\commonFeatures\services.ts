/* eslint-disable no-restricted-syntax */
import { deleteFileFromS3, gettingPreSignedUrl } from "../../utils/fileUpload";
import { API_RESPONSE_MSG } from "../../utils/constants";
import { getSecretKeys } from "../../config/awsConfig";
import { IGeneratePresignedUrlData } from "./interface";
import { handleSentryError } from "../../utils/helper";

/**
 * Common services for handling common operations.
 */
class CommonServices {
  static generatePresignedUrl = async (data: IGeneratePresignedUrlData) => {
    try {
      const { filePath, fileFormat } = data;
      const response = await gettingPreSignedUrl(filePath, fileFormat);

      return {
        success: true,
        data: response,
        message: API_RESPONSE_MSG.presigned_url_generated,
      };
    } catch (error) {
      handleSentryError(error, "generatePresignedUrl");
      console.log("generatePresignedUrl error===", error);
      return { success: false, message: error.message };
    }
  };

  static removeAttachmentsFromS3 = async (attachments: string) => {
    try {
      const keys = await getSecretKeys();
      console.log("Inside ==remove s3");

      const myData = JSON.parse(attachments);

      const linkArray: string[] = [];
      for (const link of myData) {
        linkArray.push(
          link.replace(
            keys.s3_bucket_cloudfront_distribution_url_for_s9_innerview,
            ""
          )
        );
      }
      await deleteFileFromS3(
        linkArray?.length === 1 ? linkArray[0] : linkArray
      );

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      handleSentryError(error, "removeAttachmentsFromS3");
      console.log("removeAttachmentsFromS3 error===>>>", error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };
}

export default CommonServices;
