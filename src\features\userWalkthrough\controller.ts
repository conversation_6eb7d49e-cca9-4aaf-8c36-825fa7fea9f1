import { Request, Response } from "express";
import UserWalkthroughServices from "./services";
import { IUpdateWalkthroughStatus } from "./interface";

export const getWalkthroughStatus = async (req: Request, res: Response) => {
  try {
    const { userId } = req;

    const result = await UserWalkthroughServices.getWalkthroughStatus(userId);

    return res.status(200).json(result);
  } catch (error) {
    console.error("getWalkthroughStatus controller error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to get walkthrough status",
    });
  }
};

export const updateWalkthroughStatus = async (req: Request, res: Response) => {
  try {
    const { userId } = req;
    const data: IUpdateWalkthroughStatus = req.body;

    const result = await UserWalkthroughServices.updateWalkthroughStatus(
      userId,
      data
    );

    return res.status(200).json(result);
  } catch (error) {
    console.error("updateWalkthroughStatus controller error:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to update walkthrough status",
    });
  }
};
