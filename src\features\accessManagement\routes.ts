import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  addRoleDepartmentAndPermissionForAdmin,
  addUserRole,
  deleteUserRole,
  getCommonUserRoles,
  getRolePermissions,
  getRolePermissionsByRoleId,
  getUserPermissions,
  getUserRolesPagination,
  updateRolePermissions,
  updateUserRole,
} from "./controller";
import {
  schemaValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  addUserRoleValidation,
  updateRolePermissionsValidation,
  updateUserRoleValidation,
  addRoleDepartmentAndPermissionForAdminValidation,
  roleIdParamValidation,
  rolePermissionsParamsValidation,
  getUserRolesPaginationValidation,
} from "./validation";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import {
  authorizedForCreateNewRole,
  authorizedForManageUserPermissions,
} from "../../middleware/isAuthorized";
import adminAuth from "../../middleware/adminAuth";

const accessManagementRoutes = express.Router();

/**
 * @swagger
 * /access-management/user-roles:
 *   get:
 *     summary: Get User Roles List
 *     tags:
 *       - Access Management Routes
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: user_roles_fetch
 *                  data:
 *                    type: array
 *                    items:
 *                      "oneOf": [
 *                        { $ref: '#/components/schemas/userRole' },
 *                      ]
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.get(
  ROUTES.GET_USER_ROLES,
  auth,
  HandleErrors(getCommonUserRoles)
);
/**
 * @swagger
 * /access-management/user-roles-pagination:
 *   get:
 *     summary: Get User Roles with Pagination
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           default: 0
 *         description: Starting index for pagination
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: user_roles_fetch
 *                  data:
 *                    type: object
 *                    properties:
 *                      roles:
 *                        type: array
 *                        items:
 *                          $ref: '#/components/schemas/userRole'
 *                      pagination:
 *                        type: object
 *                        properties:
 *                          total:
 *                            type: integer
 *                            example: 25
 *                          offset:
 *                            type: integer
 *                            example: 0
 *                          limit:
 *                            type: integer
 *                            example: 10
 *                          pages:
 *                            type: integer
 *                            example: 3
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.get(
  ROUTES.GET_USER_ROLES_PAGINATION,
  auth,
  paramsValidation(getUserRolesPaginationValidation),
  HandleErrors(getUserRolesPagination)
);

/**
 * @swagger
 * /access-management/add-user-role:
 *   post:
 *     summary: Add new user role
 *     description: Creates a new user role with specified permissions
 *     tags: [Access Management Routes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *             properties:
 *               name:
 *                 type: string
 *                 description: Name of the new role
 *                 example: Admin
 *     responses:
 *       200:
 *         description: Role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: user_role_added
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       format: uuid
 *                     name:
 *                       type: string
 *                       example: Admin
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: token_req
 *       403:
 *         description: Forbidden - User doesn't have permission
 */
accessManagementRoutes.post(
  ROUTES.ADD_USER_ROLE,
  auth,
  authorizedForCreateNewRole,
  schemaValidation(addUserRoleValidation),
  HandleErrors(addUserRole)
);

/**
 * @swagger
 * /access-management/user-role/{roleId}:
 *   put:
 *     summary: Update User Role
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         description: ID of the role to update
 *         schema:
 *           type: integer
 *           format: int64
 *           example: 2
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: user_role_updated
 *                  data:
 *                    type: object
 *                    properties:
 *                      id:
 *                        type: string
 *                      name:
 *                        type: string
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.put(
  ROUTES.USER_ROLE_WITH_ID,
  auth,
  authorizedForCreateNewRole,
  paramsValidation(roleIdParamValidation),
  schemaValidation(updateUserRoleValidation),
  HandleErrors(updateUserRole)
);

/**
 * @swagger
 * /access-management/user-role/{roleId}:
 *   delete:
 *     summary: Delete User Role
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         description: ID of the role to delete
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: user_role_deleted
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.delete(
  ROUTES.USER_ROLE_WITH_ID,
  auth,
  authorizedForCreateNewRole,
  paramsValidation(roleIdParamValidation),
  HandleErrors(deleteUserRole)
);

/**
 * @swagger
 * /access-management/role-permissions:
 *   get:
 *     summary: Get Role Permissions
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: query
 *         name: offset
 *         required: false
 *         description: Starting index for pagination
 *         schema:
 *           type: integer
 *           default: 0
 *       - in: query
 *         name: limit
 *         required: false
 *         description: Number of items per page
 *         schema:
 *           type: integer
 *           default: 10
 *       - in: query
 *         name: search
 *         required: false
 *         description: General search term
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: role_permissions_fetch
 *                  data:
 *                    type: array
 *                    items:
 *                      "oneOf": [
 *                        { $ref: '#/components/schemas/rolePermissions' },
 *                      ]
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 * components:
 *   schemas:
 *     userRole:
 *       type: object
 *       properties:
 *         id:
 *           type: string
 *           format: uuid
 *         name:
 *           type: string
 *         permission_count:
 *           type: number
 *         updated_ts:
 *           type: string
 *           format: date-time
 *     rolePermissions:
 *       type: object
 *       properties:
 *         role_id:
 *           type: string
 *           format: uuid
 *         role_name:
 *           type: string
 *         permissions:
 *           type: array
 *           items:
 *             type: object
 *             properties:
 *               id:
 *                 type: string
 *               name:
 *                 type: string
 *               selected:
 *                 type: boolean
 */
accessManagementRoutes.get(
  ROUTES.ROLE_PERMISSIONS,
  auth,
  authorizedForManageUserPermissions,
  paramsValidation(rolePermissionsParamsValidation),
  HandleErrors(getRolePermissions)
);

/**
 * @swagger
 * /access-management/role-permissions/{roleId}:
 *   put:
 *     summary: Update Role Permissions
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         description: ID of the role to update permissions
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               permissionIds:
 *                 type: array
 *                 items:
 *                   type: integer
 *                 description: Array of permission IDs
 *                 example: [2576, 2564]
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: role_permissions_updated
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.put(
  ROUTES.ROLE_PERMISSIONS_WITH_ID,
  auth,
  authorizedForManageUserPermissions,
  schemaValidation(updateRolePermissionsValidation),
  paramsValidation(roleIdParamValidation),
  HandleErrors(updateRolePermissions)
);

/**
 * @swagger
 * /access-management/role-permissions/{roleId}:
 *   get:
 *     summary: Get Permissions for a Specific Role
 *     tags:
 *       - Access Management Routes
 *     parameters:
 *       - in: path
 *         name: roleId
 *         required: true
 *         description: ID of the role to get permissions for
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: role_permissions_fetch
 *                  data:
 *                    type: object
 *                    properties:
 *                      role_id:
 *                        type: string
 *                      role_name:
 *                        type: string
 *                      permissions:
 *                        type: array
 *                        items:
 *                          type: object
 *                          properties:
 *                            id:
 *                              type: string
 *                              format: uuid
 *                            name:
 *                              type: string
 *                            selected:
 *                              type: boolean
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.get(
  ROUTES.ROLE_PERMISSIONS_WITH_ID,
  auth,
  authorizedForManageUserPermissions,
  paramsValidation(roleIdParamValidation),
  HandleErrors(getRolePermissionsByRoleId)
);

/**
 * Add role department and permission for admin
 *
 * @param {Request} req - The HTTP request object
 * @param {Response} res - The HTTP response object
 */
/**
 * @swagger
 * /access-management/add-role-department-and-permission-for-admin:
 *   post:
 *     summary: Add role, department and permission for admin
 *     tags:
 *       - Access Management Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               organizationId:
 *                 type: integer
 *                 description: ID of the organization
 *                 example: 1
 *               userId:
 *                 type: integer
 *                 description: ID of the user
 *                 example: 101
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: role_department_permission_added
 *                  data:
 *                    type: object
 *                    properties:
 *                      roleId:
 *                        type: string
 *                      departmentId:
 *                        type: string
 *                      permissionId:
 *                        type: string
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */

// This route does not require any permissions
accessManagementRoutes.post(
  ROUTES.ADD_ROLE_DEPARTMENT_AND_PERMISSION_FOR_ADMIN,
  adminAuth,
  schemaValidation(addRoleDepartmentAndPermissionForAdminValidation),
  HandleErrors(addRoleDepartmentAndPermissionForAdmin)
);

/**
 * @swagger
 * /access-management/user-permissions:
 *   get:
 *     summary: Get permissions for the authenticated user
 *     tags:
 *       - Access Management Routes
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: user_permissions_fetch
 *                  data:
 *                    type: array
 *                    items:
 *                      type: object
 *                      properties:
 *                        id:
 *                          type: string
 *                          format: uuid
 *                        name:
 *                          type: string
 *       401:
 *          description: Unauthorized
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 */
accessManagementRoutes.get(
  ROUTES.USER_PERMISSIONS,
  auth,
  HandleErrors(getUserPermissions)
);

export default accessManagementRoutes;
