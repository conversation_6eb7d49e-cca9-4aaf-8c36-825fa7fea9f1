import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  UpdateDateColumn,
  CreateDateColumn,
} from "typeorm";

@Entity("skills")
export default class SkillsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "title", length: 50 })
  title: string;

  @Column({ name: "short_description", length: 500 })
  shortDescription: string;

  @Column({ name: "is_core_skill", default: false })
  isCoreSkill: boolean;

  @Column({ name: "type", length: 50 })
  type: string;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}
