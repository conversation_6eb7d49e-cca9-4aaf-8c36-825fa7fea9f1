/* eslint-disable dot-notation */

import dbConnection from "../../db/dbConnection";
import { API_RESPONSE_MSG } from "../../utils/constants";
import OrganizationModel from "../../schema/s9/organization";

import { handleSentryError } from "../../utils/helper";

class Logo {
  /**
   * Update the logo for an organization
   * @param orgId Organization ID
   * @param logoUrl New logo URL
   * @returns Result of the update operation
   */
  static updateLogo = async (orgId: number, logoUrl: string) => {
    try {
      console.log("Updating logo for orgId:", orgId, "with logoUrl:", logoUrl);
      if (!orgId || !logoUrl) {
        return {
          success: false,
          message: API_RESPONSE_MSG.invalid_data,
          code: 400,
        };
      }

      // Get the data source
      const orgSource = await dbConnection.getS9DataSource();
      // Find the organization
      const organizationRepo = orgSource.getRepository(OrganizationModel);
      const organization = await organizationRepo.findOne({
        where: { id: orgId },
      });

      if (!organization) {
        return {
          success: false,
          message: API_RESPONSE_MSG.organization_not_found,
          code: 404,
        };
      }

      console.log("Found organization:", organization);

      // Update the logo
      organization.logo = logoUrl;
      await organizationRepo.save(organization);

      return {
        success: true,
        message: API_RESPONSE_MSG.logo_updated_successfully,
        code: 200,
      };
    } catch (error) {
      console.error("Error updating organization logo:", error);
      handleSentryError(error, "updateLogo");
      return null;
    }
  };
}

export default Logo;
