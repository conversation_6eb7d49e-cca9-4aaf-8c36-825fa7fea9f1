import Joi from "joi";
import {
  DEFAULT_SORT,
  EMAIL_REGEX,
  GENDER_TYPES,
  LABELS,
} from "../../utils/constants";

// Validation schema for fetching a paginated and filtered list of jobs for an organization
export const organizationJobListValidationSchema = Joi.object({
  organizationId: Joi.string().label(LABELS.orgId).required(),
  offset: Joi.number().min(0).default(0),
  limit: Joi.number().min(1).max(100).default(10),
  sort: Joi.string().valid(DEFAULT_SORT).default(DEFAULT_SORT),
  searchTerm: Joi.string().allow("", null),
});

// Validation schema for applying to a job
export const applyJobValidationSchema = Joi.object({
  candidateName: Joi.string().label(LABELS.candidateName).required(),
  email: Joi.string().email().label(LABELS.email).required(),
  gender: Joi.string()
    .valid(...GENDER_TYPES)
    .label(LABELS.gender)
    .required(),
  resume: Joi.string().label(LABELS.resume).required(),
  jobId: Joi.number().label(LABELS.jobId).required(),
  otp: Joi.number().label(LABELS.otp).required().min(1000).max(9999),
});

// Validation schema for sending OTP to a candidate
export const otpValidationSchema = Joi.object().keys({
  email: Joi.string().regex(EMAIL_REGEX).label(LABELS.email).required(),
  jobId: Joi.number().label(LABELS.jobId).required(),
  orgId: Joi.string().label(LABELS.orgId).required(),
});

// Validation schema for verifying the OTP sent to a candidate
export const verifyOtpValidationSchema = otpValidationSchema.concat(
  Joi.object({
    otp: Joi.string().length(4).label(LABELS.otp).required(),
  })
);
