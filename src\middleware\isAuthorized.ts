import { NextFunction, Response, Request } from "express";
import AccessManagementServices from "../features/accessManagement/services";
import { AUTH_MSG, PERMISSION } from "../utils/constants";
import { handleSentryError } from "../utils/helper";

// /**
//  * Middleware to check if the user has the required permission
//  * @param permission - Permission slug to check
//  * @returns Express middleware function
//  */
// export const isAuthorized =
//   (permission: string) =>
//   async (req: Request, res: Response, next: NextFunction) => {
//     try {
//       const roleId = req?.roleId;

//       if (!roleId) {
//         return res.status(401).json({
//           success: false,
//           message: AUTH_MSG.unauthorized_role,
//           code: 401,
//         });
//       }

//       // Find permission mappings for this role with related permission data
//       const rolePermissions = await AccessManagementServices.getUserPermissions(
//         roleId,
//         true
//       );

//       if (rolePermissions.success) {
//         // Check if the required permission exists in the user's permissions

//         if (!rolePermissions.data.rolePermissions.includes(permission)) {
//           return res.status(403).json({
//             success: false,
//             message: AUTH_MSG.permission_not_available,
//             code: 403,
//           });
//         }

//         next();
//       } else {
//         return res.status(403).json({
//           success: false,
//           message: AUTH_MSG.permission_not_available,
//           code: 403,
//         });
//       }
//     } catch (error) {
//       Sentry.captureException(error);
//       return res.status(500).json({
//         success: false,
//         message: AUTH_MSG.authorization_error,
//         code: 500,
//       });
//     }
//     return null;
//   };

/**
 * Custom middleware to check if user has ANY ONE of multiple permissions (OR logic)
 * @param permissions - Array of permission slugs to check
 * @returns Express middleware function
 */
export const isAuthorizedAny =
  (permissions: string[]) =>
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const roleId = req?.roleId;

      if (!roleId) {
        return res.status(401).json({
          success: false,
          message: AUTH_MSG.unauthorized_role,
          code: 401,
        });
      }

      // Find permission mappings for this role with related permission data
      const rolePermissions = await AccessManagementServices.getUserPermissions(
        roleId,
        true
      );

      if (!rolePermissions.success) {
        return res.status(403).json({
          success: false,
          message: AUTH_MSG.permission_not_available,
          code: 403,
        });
      }

      // Check if ANY of the required permissions exists in the user's permissions
      const hasAnyPermission = permissions.some((permission) =>
        rolePermissions.data.rolePermissions.includes(permission)
      );

      if (!hasAnyPermission) {
        return res.status(403).json({
          success: false,
          message: AUTH_MSG.permission_not_available,
          code: 403,
        });
      }

      return next(); // Ensure this is returned here
    } catch (error) {
      handleSentryError(error, "isAuthorizedAny");
      return res.status(500).json({
        success: false,
        message: AUTH_MSG.authorization_error,
        code: 500,
      });
    }
  };

// Pre-defined middleware functions for common permissions
export const authorizedForCreateNewRole = isAuthorizedAny([
  PERMISSION.CREATE_NEW_ROLE,
]);
export const authorizedForManageUserPermissions = isAuthorizedAny([
  PERMISSION.MANAGE_USER_PERMISSIONS,
]);
export const authorizedForCreateNewDepartment = isAuthorizedAny([
  PERMISSION.CREATE_NEW_DEPARTMENT,
]);

export const authorizedForAddEmployee = isAuthorizedAny([
  PERMISSION.ADD_EMPLOYEE,
]);

export const authorizedForManualResumeScreening = isAuthorizedAny([
  PERMISSION.MANUAL_RESUME_SCREENING,
]);
export const authorizedForArchiveRestoreCandidates = isAuthorizedAny([
  PERMISSION.ARCHIVE_RESTORE_CANDIDATES,
]);
export const authorizedForArchiveRestoreJobPosts = isAuthorizedAny([
  PERMISSION.ARCHIVE_RESTORE_JOB_POSTS,
]);
export const authorizedForCreateOrEditJobPost = isAuthorizedAny([
  PERMISSION.CREATE_OR_EDIT_JOB_POST,
]);
export const authorizedForManageTopCandidates = isAuthorizedAny([
  PERMISSION.MANAGE_TOP_CANDIDATES,
]);

export const authorizedForManageCandidateProfile = isAuthorizedAny([
  PERMISSION.MANAGE_CANDIDATE_PROFILE,
]);

export const authorizedForAddAdditionalCandidateInfo = isAuthorizedAny([
  PERMISSION.ADD_ADDITIONAL_CANDIDATE_INFO,
]);

export const authorizedForScheduleConductInterviews = isAuthorizedAny([
  PERMISSION.SCHEDULE_CONDUCT_INTERVIEWS,
]);

export const authorizedForManagePreInterviewQuestions = isAuthorizedAny([
  PERMISSION.MANAGE_PRE_INTERVIEW_QUESTIONS,
]);
export const authorizedForViewAuditLogs = isAuthorizedAny([
  PERMISSION.VIEW_AUDIT_LOGS_UPCOMING,
]);

export const authorizedForManageSubscriptions = isAuthorizedAny([
  PERMISSION.MANAGE_SUBSCRIPTIONS,
]);

export const authorizedForViewHiredCandidates = isAuthorizedAny([
  PERMISSION.VIEW_HIRED_CANDIDATES,
]);

export const authorizedForHireCandidate = isAuthorizedAny([
  PERMISSION.HIRE_CANDIDATE,
]);

export const authorizedForCandidateDetailsAccess = isAuthorizedAny([
  PERMISSION.MANAGE_CANDIDATE_PROFILE,
  PERMISSION.VIEW_HIRED_CANDIDATES,
  PERMISSION.HIRE_CANDIDATE,
]);

export const authorizedForHireCandidateAccess = isAuthorizedAny([
  PERMISSION.HIRE_CANDIDATE,
  PERMISSION.VIEW_HIRED_CANDIDATES,
]);

export const authorizedForManageCandidateProfileAccess = isAuthorizedAny([
  PERMISSION.MANAGE_CANDIDATE_PROFILE,
  PERMISSION.HIRE_CANDIDATE,
]);

export default isAuthorizedAny;
