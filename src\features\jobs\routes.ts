import express from "express";

import HandleErrors from "../../middleware/handleError";
import { sanitizeBody } from "../../middleware/sanitize";
import { schemaValidation } from "../../middleware/validateSchema";
import {
  authorizedForCreateOrEditJobPost,
  authorizedForArchiveRestoreJobPosts,
} from "../../middleware/isAuthorized";

import generateJobSkillsSchema from "./validation";
import {
  generateJobSkills,
  getJobDescUploadUrl,
  getSkills,
  generateJobRequirement,
  saveJobDetails,
  getAllJobsMeta,
  updateJob,
  getDashboardCounts,
  getJobHtmlDescription,
  updateJobDescription,
  generatePdf,
} from "./controller";

import {
  parseAdvancedPdf,
  extractPdfImages,
  getPdfMetadata,
} from "./advancedPdfController";

import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import upload from "../../middleware/upload";

// Configure multer for memory storage (buffer)

/**
 * @swagger
 * tags:
 *   name: Jobs
 *   description: Job management and related operations
 */

/**
 * @swagger
 * components:
 *   schemas:
 *     JobSkillsRequest:
 *       type: object
 *       properties:
 *         title:
 *           type: string
 *           description: Job title with key skills
 *           example: "Technical Lead - React, Node.js, Next.js, Python, Vite"
 *         employment_type:
 *           type: string
 *           description: Type of employment (full_time, part_time, etc.)
 *           example: "full_time"
 *         department_id:
 *           type: string
 *           description: Department ID
 *           example: "79"
 *         salary_range:
 *           type: string
 *           description: Salary range for the position
 *           example: "$90,000 - $120,000"
 *         salary_cycle:
 *           type: string
 *           description: Salary cycle period
 *           example: "per annum"
 *         location_type:
 *           type: string
 *           description: Type of work location
 *           example: "onsite"
 *         state:
 *           type: string
 *           description: State location
 *           example: "Florida"
 *         city:
 *           type: string
 *           description: City location
 *           example: "Innovation Drive"
 *         role_overview:
 *           type: string
 *           description: Brief overview of the role
 *           example: "This position leads the development of cutting-edge web applications using React, Node.js, Next.js, Python, and Vite."
 *         experience_level:
 *           type: string
 *           description: Required experience level
 *           example: "No experience necessary"
 *         responsibilities:
 *           type: string
 *           description: Detailed job responsibilities
 *           example: "Lead the design and implementation of scalable web applications using React, Node.js, and Next.js."
 *         educations_requirement:
 *           type: string
 *           description: Educational requirements
 *           example: "Bachelor's or Master's degree in Computer Science, Engineering, or a related field"
 *         certifications:
 *           type: string
 *           description: Required or preferred certifications
 *           example: "AWS Certified Solutions Architect, Certified ScrumMaster (CSM), or similar certifications are a plus."
 *         skills_and_software_expertise:
 *           type: string
 *           description: Required skills and software expertise
 *           example: "React, Node.js, Next.js, Python, Vite, RESTful APIs, GraphQL"
 *         experience_required:
 *           type: string
 *           description: Years of experience required
 *           example: "7"
 *         ideal_candidate_traits:
 *           type: string
 *           description: Ideal candidate traits
 *           example: "Detail-oriented, proactive, excellent communicator, team player"
 *         about_company:
 *           type: string
 *           description: Information about the company
 *           example: "We are a dynamic and innovative technology company"
 *         perks_benefits:
 *           type: string
 *           description: Perks and benefits offered
 *           example: "Competitive salary and performance-based bonuses"
 *         tone_style:
 *           type: string
 *           description: Tone style for the job description
 *           example: "Professional_Formal"
 *         additional_info:
 *           type: string
 *           description: Additional information
 *           example: "Candidates with contributions to open-source projects or a strong GitHub portfolio are highly encouraged to apply"
 *         compliance_statement:
 *           type: array
 *           items:
 *             type: string
 *           description: Compliance statements
 *           example: ["Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"]
 *         show_compliance:
 *           type: boolean
 *           description: Whether to show compliance statements
 *           example: true
 *         hiring_type:
 *           type: string
 *           description: Type of hiring
 *           example: "internal"
 *         jd_link:
 *           type: string
 *           description: Link to job description document
 *           example: "https://s9-interview-assets.s3.us-east-1.amazonaws.com/251/job-description/TL_JD_NEW.pdf_1758015312613.pdf"
 *     JobSkillsResponse:
 *       type: object
 *       properties:
 *         success:
 *           type: boolean
 *           example: true
 *           description: Indicates if the operation was successful
 *         message:
 *           type: string
 *           example: "skills_generated"
 *           description: Success or error message
 *         code:
 *           type: number
 *           example: 200
 *           description: HTTP status code
 *         data:
 *           type: object
 *           properties:
 *             careerSkills:
 *               type: array
 *               description: Career skills relevant to the job
 *               items:
 *                 type: object
 *                 properties:
 *                   name:
 *                     type: string
 *                     example: "Technical Leadership"
 *                     description: Skill name
 *                   description:
 *                     type: string
 *                     example: "Leads the design, architecture, and implementation of scalable web applications, mentoring junior developers and conducting code reviews."
 *                     description: Detailed description of the skill
 *             roleSpecificSkills:
 *               type: array
 *               description: Role-specific skills
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     example: "14"
 *                     description: Skill ID
 *                   name:
 *                     type: string
 *                     example: "Problem Solving"
 *                     description: Skill name
 *                   description:
 *                     type: string
 *                     example: "The skill of focusing on a specific challenge, then resolving it effectively."
 *                     description: Detailed description of the skill
 *             cultureSpecificSkills:
 *               type: array
 *               description: Culture-specific skills
 *               items:
 *                 type: object
 *                 properties:
 *                   id:
 *                     type: string
 *                     example: "28"
 *                     description: Skill ID
 *                   name:
 *                     type: string
 *                     example: "Communication"
 *                     description: Skill name
 *                   description:
 *                     type: string
 *                     example: "Communication is the act of transferring information clearly and effectively."
 *                     description: Detailed description of the skill
 */

const jobRoutes = express.Router();

/**
 * @swagger
 * /jobs/generate-skills:
 *   post:
 *     summary: Generate relevant skills based on job description
 *     description: Uses AI to analyze a job description and return relevant skills
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/JobSkillsRequest'
 *     responses:
 *       200:
 *         description: Skills successfully generated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/JobSkillsResponse'
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.post(
  ROUTES.JOBS.GENERATE_SKILLS,
  auth,
  authorizedForCreateOrEditJobPost,
  sanitizeBody(),
  schemaValidation(generateJobSkillsSchema),
  HandleErrors(generateJobSkills)
);

/**
 * @swagger
 * /jobs/upload-url:
 *   post:
 *     summary: Upload job description PDF, extract data and pre-fill form fields
 *     description: Uploads a PDF job description, parses its content, extracts structured job information using GPT, and returns form field values
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: Job description PDF file to upload and process
 *     responses:
 *       200:
 *         description: PDF successfully processed and form fields extracted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the operation was successful
 *                 message:
 *                   type: string
 *                   example: "job_description_processed_successfully"
 *                   description: Success message
 *                 code:
 *                   type: number
 *                   example: 200
 *                   description: HTTP status code
 *                 data:
 *                   type: object
 *                   properties:
 *                     formFields:
 *                       type: object
 *                       description: Extracted form field values from the PDF content
 *                       properties:
 *                         job_title:
 *                           type: string
 *                           example: "Technical Lead"
 *                           description: Job title extracted from the PDF
 *                         department:
 *                           type: string
 *                           example: "Engineering"
 *                           description: Department name extracted from the PDF
 *                         job_location:
 *                           type: string
 *                           example: "Florida"
 *                           description: Job location extracted from the PDF
 *                         state:
 *                           type: string
 *                           example: "Florida"
 *                           description: State location
 *                         city:
 *                           type: string
 *                           example: "Innovation Dr"
 *                           description: City location
 *                         job_type:
 *                           type: string
 *                           example: "full_time"
 *                           description: Job type extracted from the PDF
 *                         location_type:
 *                           type: string
 *                           example: "onsite"
 *                           description: Location type (remote, onsite, hybrid)
 *                         job_description:
 *                           type: string
 *                           example: "This position..."
 *                           description: Job description extracted from the PDF
 *                         responsibilities:
 *                           type: string
 *                           example: "Lead the design..."
 *                           description: Job responsibilities extracted from the PDF
 *                         requirements:
 *                           type: string
 *                           example: "Bachelor's or..."
 *                           description: Job requirements extracted from the PDF
 *                         certifications:
 *                           type: string
 *                           example: "AWS Certified..."
 *                           description: Certifications extracted from the PDF
 *                         salary_range:
 *                           type: string
 *                           example: "$90k-$120k"
 *                           description: Salary range extracted from the PDF
 *                         salary_cycle:
 *                           type: string
 *                           example: "per annum"
 *                           description: Salary cycle period
 *                         experience_required:
 *                           type: string
 *                           example: "7"
 *                           description: Years of experience required
 *                         education_required:
 *                           type: string
 *                           example: "Bachelor's or..."
 *                           description: Education requirements extracted from the PDF
 *                         skills_required:
 *                           type: string
 *                           example: "React, Node.js..."
 *                           description: Required skills extracted from the PDF
 *                         benefits:
 *                           type: string
 *                           example: "Competitive..."
 *                           description: Benefits extracted from the PDF
 *                         tone_style:
 *                           type: string
 *                           example: "Professional"
 *                           description: Tone style for the job description
 *                         compliance_statement:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: []
 *                           description: Compliance statements
 *                         candidate_traits:
 *                           type: string
 *                           example: "Detail-oriented..."
 *                           description: Candidate traits extracted from the PDF
 *                         about_company:
 *                           type: string
 *                           example: "We are a dynamic..."
 *                           description: Company information extracted from the PDF
 *                         additional_info:
 *                           type: string
 *                           example: "Candidates with..."
 *                           description: Additional information extracted from the PDF
 *                         source_document_quality:
 *                           type: number
 *                           example: 9
 *                           description: Quality score of the source document
 *                     jd_link:
 *                       type: string
 *                       example: "https://s9-interview-assets.s3.us-east-1.amazonaws.com/251/job-description/TL_JD_NEW.pdf_175801591..."
 *                       description: URL to the uploaded job description PDF file (with AWS S3 presigned URL parameters)
 *       400:
 *         description: No PDF file uploaded or invalid request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "no_pdf_file_uploaded"
 *                 code:
 *                   type: number
 *                   example: 400
 *       422:
 *         description: Could not process the PDF file
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "job_description_processed_failed"
 *                 error:
 *                   type: string
 *                   example: "pdf parsing failed"
 *                 code:
 *                   type: number
 *                   example: 422
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.post(
  ROUTES.JOBS.UPLOAD_URL,
  upload.single("file"),
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(getJobDescUploadUrl)
);

/**
 * @swagger
 * /jobs/get-all-skills:
 *   get:
 *     summary: Get all available skills from the database
 *     description: Retrieves all skills data categorized by type from the database
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Skills data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                   description: Indicates if the operation was successful
 *                 message:
 *                   type: string
 *                   example: "skills_generated"
 *                   description: Success message
 *                 code:
 *                   type: number
 *                   example: 200
 *                   description: HTTP status code
 *                 data:
 *                   type: array
 *                   description: Array of skill types with their items
 *                   items:
 *                     type: object
 *                     properties:
 *                       type:
 *                         type: string
 *                         example: "Mentality"
 *                         description: Skill category type
 *                       items:
 *                         type: array
 *                         description: Skills in this category
 *                         items:
 *                           type: object
 *                           properties:
 *                             id:
 *                               type: integer
 *                               example: 10
 *                               description: Skill ID
 *                             type:
 *                               type: string
 *                               example: "Mentality"
 *                               description: Skill type
 *                             title:
 *                               type: string
 *                               example: "Self Discipline"
 *                               description: Skill title
 *                             short_description:
 *                               type: string
 *                               example: "Unwavering..."
 *                               description: Brief description of the skill
 *                             description:
 *                               type: string
 *                               example: "Detailed desc..."
 *                               description: Full description of the skill
 *                             value_points:
 *                               type: string
 *                               example: "Value points..."
 *                               description: Value points associated with the skill
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       403:
 *         description: Forbidden - Insufficient permissions to access this resource
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden"
 *                 code:
 *                   type: number
 *                   example: 403
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(
  ROUTES.JOBS.GET_ALL_SKILLS,
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(getSkills)
);

/**
 * @swagger
 * /jobs/generate-job-requirement:
 *   post:
 *     summary: Generate job requirement
 *     description: Processes job details and skills to generate a full job requirement
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               department_id:
 *                 type: string
 *                 example: "79"
 *               hiring_type:
 *                 type: string
 *                 example: "internal"
 *               title:
 *                 type: string
 *                 example: "Technical Lead - React, Node.js, Next.js, Python, Vite"
 *               employment_type:
 *                 type: string
 *                 example: "full_time"
 *               salary_range:
 *                 type: string
 *                 example: "$90,000 - $120,000"
 *               salary_cycle:
 *                 type: string
 *                 example: "per annum"
 *               location_type:
 *                 type: string
 *                 example: "onsite"
 *               state:
 *                 type: string
 *                 example: "Florida"
 *               city:
 *                 type: string
 *                 example: "Innovation Drive"
 *               role_overview:
 *                 type: string
 *                 example: "This position leads the development of cutting-edge web applications..."
 *               responsibilities:
 *                 type: string
 *                 example: "Lead the design and implementation of scalable web applications..."
 *               educations_requirement:
 *                 type: string
 *                 example: "Bachelor's or Master's degree in Computer Science..."
 *               certifications:
 *                 type: string
 *                 example: "AWS Certified Solutions Architect..."
 *               skills_and_software_expertise:
 *                 type: string
 *                 example: "React, Node.js, Next.js, Python, Vite..."
 *               experience_required:
 *                 type: string
 *                 example: "7"
 *               ideal_candidate_traits:
 *                 type: string
 *                 example: "Detail-oriented, proactive, excellent communicator..."
 *               about_company:
 *                 type: string
 *                 example: "We are a dynamic and innovative technology company..."
 *               perks_benefits:
 *                 type: string
 *                 example: "Competitive salary and performance-based bonuses..."
 *               tone_style:
 *                 type: string
 *                 example: "Professional_Formal"
 *               additional_info:
 *                 type: string
 *                 example: "Candidates with contributions to open-source projects..."
 *               compliance_statement:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"]
 *               show_compliance:
 *                 type: boolean
 *                 example: true
 *               experience_level:
 *                 type: string
 *                 example: "No experience necessary"
 *               all_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     type:
 *                       type: string
 *                       example: "Mentality"
 *                     items:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           id:
 *                             type: integer
 *                             example: 10
 *                           type:
 *                             type: string
 *                             example: "Mentality"
 *                           title:
 *                             type: string
 *                             example: "Self Discipline"
 *                           short_description:
 *                             type: string
 *                             example: "Unwavering performance within a system of rules or boundaries."
 *               career_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Technical Leadership"
 *                     description:
 *                       type: string
 *                       example: "Leading the design, architecture, and implementation of scalable web applications..."
 *               role_specific_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "14"
 *                     name:
 *                       type: string
 *                       example: "Problem Solving"
 *                     description:
 *                       type: string
 *                       example: "The skill of focusing on a specific challenge, then resolving it effectively."
 *               culture_specific_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "28"
 *                     name:
 *                       type: string
 *                       example: "Communication"
 *                     description:
 *                       type: string
 *                       example: "Communication is the act of transferring information clearly and effectively."
 *     responses:
 *       200:
 *         description: Job requirement generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "job_requirement_generated"
 *                 data:
 *                   type: string
 *                   example: "<section class=\"job-header\">\n  <h2>Technical Lead - React, Node.js, Next.js, Python, Vite</h2>\n  <ul class=\"job-meta\">\n    <li><strong>Location:</strong> Innovation Drive, Florida (Onsite)</li>\n    <li><strong>Employment Type:</strong> Full Time</li>\n    <li><strong>Salary:</strong> $90,000 - $120,000 per annum</li>\n  </ul>\n</section>\n\n<section class=\"job-section\">\n  <h3>Company Overview</h3>\n  <p>We are a dynamic and innovative technology company...</p>\n</section>"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.post(
  ROUTES.JOBS.GENERATE_JOB_REQUIREMENT,
  auth,
  sanitizeBody(),
  HandleErrors(generateJobRequirement)
);

/**
 * @swagger
 * /jobs/save-job-details:
 *   post:
 *     summary: Save job details
 *     description: Saves the job details to the database
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               title:
 *                 type: string
 *                 example: "Technical Lead - React, Node.js, Next.js, Python, Vite"
 *               employment_type:
 *                 type: string
 *                 example: "full_time"
 *               salary_range:
 *                 type: string
 *                 example: "$90,000 - $120,000"
 *               salary_cycle:
 *                 type: string
 *                 example: "per annum"
 *               location_type:
 *                 type: string
 *                 example: "onsite"
 *               state:
 *                 type: string
 *                 example: "Florida"
 *               city:
 *                 type: string
 *                 example: "Innovation Drive"
 *               role_overview:
 *                 type: string
 *                 example: "This position leads the development of cutting-edge web applications..."
 *               experience_level:
 *                 type: string
 *                 example: "No experience necessary"
 *               responsibilities:
 *                 type: string
 *                 example: "Lead the design and implementation of scalable web applications..."
 *               educations_requirement:
 *                 type: string
 *                 example: "Bachelor's or Master's degree in Computer Science, Engineering, or a related field"
 *               certifications:
 *                 type: string
 *                 example: "AWS Certified Solutions Architect, Certified ScrumMaster (CSM), or similar certifications are a plus."
 *               skills_and_software_expertise:
 *                 type: string
 *                 example: "React, Node.js, Next.js, Python, Vite, RESTful APIs..."
 *               experience_required:
 *                 type: string
 *                 example: "7"
 *               ideal_candidate_traits:
 *                 type: string
 *                 example: "Detail-oriented, proactive, excellent communicator..."
 *               about_company:
 *                 type: string
 *                 example: "We are a dynamic and innovative technology company..."
 *               perks_benefits:
 *                 type: string
 *                 example: "Competitive salary and performance-based bonuses..."
 *               tone_style:
 *                 type: string
 *                 example: "Professional_Formal"
 *               additional_info:
 *                 type: string
 *                 example: "Candidates with contributions to open-source projects..."
 *               compliance_statement:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)"]
 *               show_compliance:
 *                 type: boolean
 *                 example: true
 *               final_job_description_html:
 *                 type: string
 *                 example: "<section class=\"job-header\">\n  <h2>Technical Lead - React, Node.js, Next.js, Python, Vite</h2>...</section>"
 *               hiring_type:
 *                 type: string
 *                 example: "internal"
 *               department_id:
 *                 type: string
 *                 example: "79"
 *               jd_link:
 *                 type: string
 *                 example: "https://s9-interview-assets.s3.us-east-1.amazonaws.com/251/job-description/TL_JD_NEW.pdf_1758015312613.pdf"
 *               career_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       example: "Technical Leadership"
 *                     description:
 *                       type: string
 *                       example: "Leading the design, architecture, and implementation of scalable web applications..."
 *               role_specific_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "14"
 *                     name:
 *                       type: string
 *                       example: "Problem Solving"
 *                     description:
 *                       type: string
 *                       example: "The skill of focusing on a specific challenge, then resolving it effectively."
 *               culture_specific_skills:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: string
 *                       example: "28"
 *                     name:
 *                       type: string
 *                       example: "Communication"
 *                     description:
 *                       type: string
 *                       example: "Communication is the act of transferring information clearly and effectively."
 *     responses:
 *       200:
 *         description: Job details saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "job_details_saved"
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: integer
 *                       example: 844
 *                     orgId:
 *                       type: integer
 *                       example: 251
 *                     title:
 *                       type: string
 *                       example: "Technical Lead - React, Node.js, Next.js, Python, Vite"
 *                     employmentType:
 *                       type: string
 *                       example: "full_time"
 *                     salaryRange:
 *                       type: string
 *                       example: "$90,000 - $120,000"
 *                     salaryCycle:
 *                       type: string
 *                       example: "per annum"
 *                     locationType:
 *                       type: string
 *                       example: "onsite"
 *                     location:
 *                       type: string
 *                       example: "onsite"
 *                     state:
 *                       type: string
 *                       example: "Florida"
 *                     city:
 *                       type: string
 *                       example: "Innovation Drive"
 *                     roleOverview:
 *                       type: string
 *                       example: "This position leads the development of cutting-edge web applications..."
 *                     experienceLevel:
 *                       type: string
 *                       example: "No experience necessary"
 *                     responsibilities:
 *                       type: string
 *                       example: "Lead the design and implementation of scalable web applications..."
 *                     educationsRequirement:
 *                       type: string
 *                       example: "Bachelor&#039;s or Master&#039;s degree in Computer Science, Engineering, or a related field"
 *                     skillsAndSoftwareExpertise:
 *                       type: string
 *                       example: "React, Node.js, Next.js, Python, Vite, RESTful APIs..."
 *                     certifications:
 *                       type: string
 *                       example: "AWS Certified Solutions Architect, Certified ScrumMaster (CSM), or similar certifications are a plus."
 *                     experienceRequired:
 *                       type: string
 *                       example: "7"
 *                     idealCandidateTraits:
 *                       type: string
 *                       example: "Detail-oriented, proactive, excellent communicator..."
 *                     aboutCompany:
 *                       type: string
 *                       example: "We are a dynamic and innovative technology company..."
 *                     perksBenefits:
 *                       type: string
 *                       example: "Competitive salary and performance-based bonuses..."
 *                     toneStyle:
 *                       type: string
 *                       example: "Professional_Formal"
 *                     additionalInfo:
 *                       type: string
 *                       example: "Candidates with contributions to open-source projects..."
 *                     departmentId:
 *                       type: string
 *                       example: "79"
 *                     hiringType:
 *                       type: string
 *                       example: "internal"
 *                     showCompliance:
 *                       type: boolean
 *                       example: true
 *                     complianceStatement:
 *                       type: string
 *                       example: "[\"Affirmative Action Statement (For Federal Contractors or Affirmative Action Employers)\"]"
 *                     finalJobDescriptionHtml:
 *                       type: string
 *                       example: "&lt;section class=&quot;job-header&quot;&gt;\n  &lt;h2&gt;Technical Lead - React, Node.js, Next.js, Python, Vite&lt;/h2&gt;...&lt;/section&gt;"
 *                     jdLink:
 *                       type: string
 *                       example: "https://s9-interview-assets.s3.us-east-1.amazonaws.com/251/job-description/TL_JD_NEW.pdf_1758015312613.pdf"
 *                     jobId:
 *                       type: string
 *                       example: "ORG-TE-0008"
 *                     jobDetailFile:
 *                       type: ["null", "string"]
 *                       example: null
 *                     finalJobDescription:
 *                       type: ["null", "string"]
 *                       example: null
 *                     id:
 *                       type: integer
 *                       example: 205
 *                     isActive:
 *                       type: boolean
 *                       example: true
 *                     createdTs:
 *                       type: string
 *                       format: "date-time"
 *                       example: "2025-09-16T12:53:12.612Z"
 *                     updatedTs:
 *                       type: string
 *                       format: "date-time"
 *                       example: "2025-09-16T12:53:12.612Z"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Bad request - Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       403:
 *         description: Forbidden - User does not have permission to create job posts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden"
 *                 code:
 *                   type: number
 *                   example: 403
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.post(
  ROUTES.JOBS.SAVE_JOB_DETAILS,
  auth,
  authorizedForCreateOrEditJobPost,
  sanitizeBody(),
  HandleErrors(saveJobDetails)
);

/**
 * @swagger
 * /jobs/get-jobs-meta:
 *   get:
 *     summary: Get job metadata
 *     description: Returns job id, title, and created timestamp with pagination
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         required: false
 *         description: Filter jobs by active status (true/false)
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 0
 *         required: false
 *         description: Page number for pagination (0-based indexing)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *         required: false
 *         description: Number of items to return per page
 *     responses:
 *       200:
 *         description: Job metadata retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Job metadata fetched successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 202
 *                       jobId:
 *                         type: string
 *                         example: "ORG-TE-0005"
 *                       title:
 *                         type: string
 *                         example: "Technical Lead"
 *                       postedDate:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-03T12:52:24.819Z"
 *                       updatedDate:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-11T13:42:30.625Z"
 *                       isActive:
 *                         type: integer
 *                         example: 0
 *                       finalJobDescriptionHtml:
 *                         type: string
 *                         example: "&lt;p&gt; &lt;/p&gt;&lt;h2&gt;Technical Lead&lt;/h2&gt;&lt;p&gt;...&lt;/p&gt;"
 *                       applicationCount:
 *                         type: string
 *                         example: "2"
 *                 code:
 *                   type: number
 *                   example: 200
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(ROUTES.JOBS.GET_JOBS_META, auth, HandleErrors(getAllJobsMeta)); // ✅ NEW ROUTE

/**
 * @swagger
 * /jobs/updateJob/{id}:
 *   put:
 *     summary: Archive or unarchive a job by ID
 *     description: Sets the isActive field of the specified job based on the status parameter (true for active, false for archive)
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the job to update
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               status:
 *                 type: boolean
 *                 description: Set to false to archive or true to unarchive/activate the job
 *                 example: false
 *             required:
 *               - status
 *     responses:
 *       200:
 *         description: Job successfully updated
 *         content:
 *           application/json:
 *             schema:
 *               oneOf:
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: "job_archive_successfully"
 *                     code:
 *                       type: number
 *                       example: 200
 *                   description: Response when archiving a job (status=false)
 *                 - type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                     message:
 *                       type: string
 *                       example: "job_restored_successfully"
 *                     code:
 *                       type: number
 *                       example: 200
 *                   description: Response when restoring/unarchiving a job (status=true)
 *       400:
 *         description: Invalid job ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_job_id"
 *                 code:
 *                   type: number
 *                   example: 400
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "job_not_found"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.put(
  ROUTES.JOBS.UPDATE_JOB,
  auth,
  authorizedForArchiveRestoreJobPosts,
  HandleErrors(updateJob)
);
// ✅ Added deactivate job API

/**
 * @swagger
 * /jobs/dashboard-counts:
 *   get:
 *     summary: Get dashboard counts for jobs
 *     description: Returns counts of total and active jobs for the current user
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved dashboard counts
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     success:
 *                       type: boolean
 *                       example: true
 *                       description: Indicates if the operation was successful
 *                     message:
 *                       type: string
 *                       example: "dashboard_counts_fetch_success"
 *                       description: Success message
 *                     totalJobs:
 *                       type: number
 *                       example: 81
 *                       description: Total number of jobs for the user in the organization
 *                     activeJobs:
 *                       type: number
 *                       example: 44
 *                       description: Number of active jobs for the user in the organization
 *                     onHoldApplications:
 *                       type: number
 *                       example: 9
 *                       description: Number of applications on hold
 *                     upcomingInterviews:
 *                       type: number
 *                       example: 12
 *                       description: Number of upcoming interviews
 *                     scheduledInterviews:
 *                       type: number
 *                       example: 163
 *                       description: Total number of scheduled interviews
 *                     hiredCandidates:
 *                       type: number
 *                       example: 7
 *                       description: Number of hired candidates
 *                 code:
 *                   type: number
 *                   example: 200
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Failed to fetch dashboard counts
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(
  ROUTES.JOBS.DASHBOARD_COUNTS,
  auth,
  HandleErrors(getDashboardCounts)
);

/**
 * @swagger
 * /jobs/get-job-html-description:
 *   get:
 *     summary: Get job HTML description by job ID
 *     description: Returns the HTML description of the specified job
 *     tags: [Jobs]
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: integer
 *         required: true
 *         description: Numeric ID of the job to fetch HTML description for
 *     responses:
 *       200:
 *         description: Successfully retrieved job HTML description
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "job_fetch_success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: number
 *                       example: 202
 *                     title:
 *                       type: string
 *                       example: "MBA in People Management"
 *                     htmlDescription:
 *                       type: string
 *                       example: "&lt;p&gt; &lt;/p&gt;&lt;h2&gt;MBA in People Management&lt;/h2&gt;&lt;p&gt;...&lt;/p&gt;"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Invalid job ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invalid job ID
 *                 code:
 *                   type: number
 *                   example: 400
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Job not found"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.get(
  ROUTES.JOBS.GET_JOB_HTML_DESCRIPTION,
  auth,
  HandleErrors(getJobHtmlDescription)
);
export default jobRoutes;

/**
 * @swagger
 * /jobs/update-job-description:
 *   put:
 *     summary: Update job description
 *     description: Updates the HTML description of a job
 *     tags: [Jobs]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               jobId:
 *                 type: integer
 *                 description: jobId of the job to update
 *               finalJobDescriptionHtml:
 *                 type: string
 *                 description: HTML description of the job
 *             required:
 *               - jobId
 *               - finalJobDescriptionHtml
 *     responses:
 *       200:
 *         description: Job description updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "job_update_success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: number
 *                       example: 202
 *                     title:
 *                       type: string
 *                       example: "MBA in People Management"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Invalid job ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_job_id"
 *                 code:
 *                   type: number
 *                   example: 400
 *       404:
 *         description: Job not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "job_not_found"
 *                 code:
 *                   type: number
 *                   example: 404
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: number
 *                   example: 500
 */
jobRoutes.put(
  ROUTES.JOBS.UPDATE_JOB_DESCRIPTION,
  auth,
  HandleErrors(updateJobDescription)
);
// Swagger documentation not required for this route — it's not used in the frontend.
jobRoutes.post(ROUTES.JOBS.GENERATE_PDF, auth, HandleErrors(generatePdf));

/**
 * @swagger
 * /jobs/parse-advanced-pdf:
 *   post:
 *     summary: Advanced PDF parsing with OCR and image extraction support
 *     description: Parse complex PDFs including template resumes using OCR technology. Supports image extraction and handles PDFs that standard parsers cannot read.
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: PDF file to parse
 *               extractImages:
 *                 type: boolean
 *                 default: false
 *                 description: Whether to extract images from the PDF
 *               useOcr:
 *                 type: boolean
 *                 default: true
 *                 description: Whether to use OCR for text extraction
 *               ocrLanguage:
 *                 type: string
 *                 default: 'eng'
 *                 description: OCR language code (eng, spa, fra, etc.)
 *     responses:
 *       200:
 *         description: PDF successfully parsed
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "PDF processed successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     text:
 *                       type: string
 *                       description: Extracted text content
 *                     extractionMethod:
 *                       type: string
 *                       enum: ['text', 'ocr', 'hybrid']
 *                       description: Method used for extraction
 *                     confidence:
 *                       type: number
 *                       description: OCR confidence score (0-100)
 *                     metadata:
 *                       type: object
 *                       description: PDF metadata
 *                     images:
 *                       type: array
 *                       description: Extracted images (if requested)
 *                       items:
 *                         type: object
 *                         properties:
 *                           index:
 *                             type: number
 *                           size:
 *                             type: number
 *                           base64:
 *                             type: string
 *                           mimeType:
 *                             type: string
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: No PDF file uploaded
 *       422:
 *         description: Failed to process PDF
 *       500:
 *         description: Server error
 */
jobRoutes.post(
  ROUTES.JOBS.PARSE_ADVANCED_PDF,
  upload.single("file"),
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(parseAdvancedPdf)
);

/**
 * @swagger
 * /jobs/extract-pdf-images:
 *   post:
 *     summary: Extract images from PDF
 *     description: Extract all images from a PDF file and return them as base64 encoded data
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: PDF file to extract images from
 *     responses:
 *       200:
 *         description: Images successfully extracted
 *       404:
 *         description: No images found in PDF
 *       422:
 *         description: Failed to extract images
 */
jobRoutes.post(
  ROUTES.JOBS.EXTRACT_PDF_IMAGES,
  upload.single("file"),
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(extractPdfImages)
);

/**
 * @swagger
 * /jobs/get-pdf-metadata:
 *   post:
 *     summary: Get PDF metadata
 *     description: Extract metadata from PDF without full parsing
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - file
 *             properties:
 *               file:
 *                 type: string
 *                 format: binary
 *                 description: PDF file to get metadata from
 *     responses:
 *       200:
 *         description: Metadata successfully extracted
 *       422:
 *         description: Failed to extract metadata
 */
jobRoutes.post(
  ROUTES.JOBS.GET_PDF_METADATA,
  upload.single("file"),
  auth,
  authorizedForCreateOrEditJobPost,
  HandleErrors(getPdfMetadata)
);
