/**
 * Test file for Advanced PDF Parser
 * 
 * To run these tests, you would need to install Jest:
 * npm install --save-dev jest @types/jest
 * 
 * Then add to package.json:
 * "scripts": {
 *   "test": "jest"
 * }
 */

const { advancedPdfParser, parsePdfWithAdvancedRetries } = require('../dist/utils/advancedPdfParser');

describe('Advanced PDF Parser', () => {
  let testPdfBuffer;
  
  beforeEach(() => {
    // Create a simple test PDF buffer
    testPdfBuffer = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000058 00000 n \n0000000115 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n174\n%%EOF');
  });

  describe('parsePdf', () => {
    it('should parse PDF with text extraction', async () => {
      try {
        const result = await advancedPdfParser.parsePdf(testPdfBuffer, {
          extractImages: false,
          useOcr: false
        });

        expect(result).toBeDefined();
        expect(result.extractionMethod).toBe('text');
        expect(typeof result.text).toBe('string');
      } catch (error) {
        // Expected for minimal test PDF - just ensure it doesn't crash
        expect(error).toBeDefined();
      }
    });

    it('should handle OCR extraction when text extraction fails', async () => {
      try {
        const result = await advancedPdfParser.parsePdf(testPdfBuffer, {
          extractImages: false,
          useOcr: true,
          ocrLanguage: 'eng'
        });

        expect(result).toBeDefined();
        expect(['text', 'ocr', 'hybrid']).toContain(result.extractionMethod);
      } catch (error) {
        // Expected for minimal test PDF - just ensure it doesn't crash
        expect(error).toBeDefined();
      }
    });
  });

  describe('parsePdfWithAdvancedRetries', () => {
    it('should provide backward compatibility', async () => {
      try {
        const result = await parsePdfWithAdvancedRetries(testPdfBuffer, 3);
        
        if (result) {
          expect(result).toHaveProperty('text');
          expect(typeof result.text).toBe('string');
        } else {
          // Null result is acceptable for test PDF
          expect(result).toBeNull();
        }
      } catch (error) {
        // Expected for minimal test PDF
        expect(error).toBeDefined();
      }
    });

    it('should handle invalid input gracefully', async () => {
      const result = await parsePdfWithAdvancedRetries(Buffer.from(''), 1);
      expect(result).toBeNull();
    });
  });
});

describe('Error Handling', () => {
  it('should handle corrupted PDF gracefully', async () => {
    const corruptedBuffer = Buffer.from('This is not a PDF file at all!');
    
    const result = await parsePdfWithAdvancedRetries(corruptedBuffer, 1);
    expect(result).toBeNull();
  });

  it('should handle empty buffer', async () => {
    const emptyBuffer = Buffer.alloc(0);
    
    const result = await parsePdfWithAdvancedRetries(emptyBuffer, 1);
    expect(result).toBeNull();
  });
});
