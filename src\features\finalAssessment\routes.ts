import express from "express";
import {
  addManualFinalAssessmentQuestion,
  createFinalAssessment,
  getFinalAssessmentByCandidate,
  getFinalAssessmentQuestion,
  getAssessmentStatus,
  shareAssessment,
  submitAssessment,
  verifyCandidateEmail,
  generateAssessmentToken,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import {
  // paramsValidation,
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  addManualQuestionValidation,
  candidateAssessmentIdValidation,
  createFinalAssessmentByAiValidation,
  getAssessmentStatusValidation,
  getQuestionQueryValidation,
  shareAssessmentValidation,
  verifyCandidateEmailValidation,
  generateAssessmentTokenValidation,
} from "./validations";
import HandleErrors from "../../middleware/handleError";
import { authorizedForManageCandidateProfileAccess } from "../../middleware/isAuthorized";

const router = express.Router();

/**
 * @swagger
 * /final-assessment/create-final-assessment:
 *   post:
 *     summary: Create a new final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobId
 *               - jobApplicationId
 *             properties:
 *               jobId:
 *                 type: number
 *                 description: ID of the job
 *               jobApplicationId:
 *                 type: number
 *                 description: ID of the job application
 *     responses:
 *       200:
 *         description: Final assessment processing started
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "final_assessment_processing_started"
 *                 code:
 *                   type: number
 *                   example: 200
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.CREATE_FINAL_ASSESSMENT,
  auth,
  authorizedForManageCandidateProfileAccess,
  schemaValidation(createFinalAssessmentByAiValidation),
  HandleErrors(createFinalAssessment)
);

/**
 * @swagger
 * /final-assessment/assessment/questions:
 *   get:
 *     summary: Get final assessment questions grouped by skill type
 *     description: Retrieves questions for a final assessment with the specified finalAssessmentId, jobId, and jobApplicationId
 *     tags: [Final Assessment]
 *     parameters:
 *       - in: query
 *         name: finalAssessmentId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the final assessment
 *         example: 216
 *       - in: query
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the job
 *         example: 40
 *       - in: query
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: The ID of the job application
 *         example: 180
 *     responses:
 *       200:
 *         description: Questions fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "questions_fetched_successfully"
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     assessmentId:
 *                       type: integer
 *                       example: 216
 *                     jobApplicationId:
 *                       type: integer
 *                       example: 180
 *                     isAssessmentSubmitted:
 *                       type: boolean
 *                       example: false
 *                     isAssessmentShared:
 *                       type: boolean
 *                       example: false
 *                     questionGroups:
 *                       type: array
 *                       items:
 *                         properties:
 *                           type:
 *                             type: string
 *                             example: "technical"
 *                           questions:
 *                             type: array
 *                             items:
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 421
 *                                 question:
 *                                   type: string
 *                                   example: "What is the primary purpose of React's virtual DOM?"
 *                                 questionType:
 *                                   type: string
 *                                   example: "mcq"
 *                                 skillId:
 *                                   type: integer
 *                                   example: 5
 *                                 skillTitle:
 *                                   type: string
 *                                   example: "React"
 *                                 options:
 *                                   type: object
 *                                   example: { "options": [{"id": "a", "text": "To improve security"}, {"id": "b", "text": "To optimize performance"}] }
 *                                 correctAnswer:
 *                                   type: string
 *                                   example: "b"
 *                               type: object
 *                         type: object
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 */
router.get(
  ROUTES.FINAL_ASSESSMENT.GET_FINAL_ASSESSMENT_QUESTION,
  auth,
  queryValidation(getQuestionQueryValidation),
  HandleErrors(getFinalAssessmentQuestion)
);

/**
 * @swagger
 * /final-assessment/assessment/create-question:
 *   post:
 *     summary: Add a manual question to a final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *               - question
 *               - questionType
 *               - skillId
 *               - options
 *               - correctAnswer
 *             properties:
 *               finalAssessmentId:
 *                 type: number
 *                 example: 216
 *               question:
 *                 type: string
 *                 example: "What is the primary advantage of using React hooks?"
 *               questionType:
 *                 type: string
 *                 enum: [mcq, true_false]
 *                 example: "mcq"
 *               skillId:
 *                 type: number
 *                 example: 35
 *               options:
 *                 type: object
 *                 properties:
 *                   options:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: string
 *                           example: "a"
 *                         text:
 *                           type: string
 *                           example: "Improved performance"
 *                 example: {
 *                   "options": [
 *                     {"id": "A", "text": "fwef"},
 *                     {"id": "B", "text": "fwef"},
 *                     {"id": "C", "text": "fwefwe"},
 *                     {"id": "D", "text": "fw"}]
 *                 }
 *               correctAnswer:
 *                 type: string
 *                 example: "A"
 *     responses:
 *       200:
 *         description: Question added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "final_assessment_questions_created"
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 535
 *                     finalAssessmentId:
 *                       type: integer
 *                       example: 216
 *                     skillId:
 *                       type: integer
 *                       example: 35
 *                     question:
 *                       type: string
 *                       example: "fwefwefwefwefwef"
 *                     questionType:
 *                       type: string
 *                       example: "mcq"
 *                     options:
 *                       type: object
 *                       example: { "options": [{"id": "A", "text": "fwef"}, {"id": "B", "text": "fwef"}, {"id": "C", "text": "fwefwe"}, {"id": "D", "text": "fw"}] }
 *                     correctAnswer:
 *                       type: string
 *                       example: "A"
 *                     skillTitle:
 *                       type: string
 *                       example: "JavaScript"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.ADD_MANUAL_QUESTION,
  auth,
  schemaValidation(addManualQuestionValidation),
  HandleErrors(addManualFinalAssessmentQuestion)
);

/**
 * @swagger
 * /final-assessment/assessment/share:
 *   post:
 *     summary: Share assessment with candidate via email
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *               - assessmentLink
 *               - jobApplicationId
 *             properties:
 *               finalAssessmentId:
 *                 type: number
 *                 description: ID of the final assessment
 *                 example: 216
 *               assessmentLink:
 *                 type: string
 *                 description: Link to the assessment that will be sent to the candidate
 *                 example: "https://example.com/assessment/216"
 *               jobApplicationId:
 *                 type: number
 *                 description: ID of the job application
 *                 example: 180
 *     responses:
 *       200:
 *         description: Assessment shared successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "assessment_shared_successfully"
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     candidateEmail:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     candidateName:
 *                       type: string
 *                       example: "John Doe"
 *                     jobTitle:
 *                       type: string
 *                       example: "Senior React Developer"
 *                     assessmentLink:
 *                       type: string
 *                       example: "https://example.com/assessment/216"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.SHARE_ASSESSMENT,
  auth,
  schemaValidation(shareAssessmentValidation),
  HandleErrors(shareAssessment)
);

/**
 * @swagger
 * /final-assessment/candidate/assessment:
 *   get:
 *     summary: Get final assessment by finalAssessmentId for candidate view
 *     tags: [Final Assessment]
 *     parameters:
 *       - in: query
 *         name: finalAssessmentId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the final assessment (encrypted)
 *         example: "123"
 *     responses:
 *       200:
 *         description: Assessment fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "questions_fetched"
 *                 data:
 *                   type: object
 *                   properties:
 *                     assessmentId:
 *                       type: integer
 *                       example: 216
 *                     jobApplicationId:
 *                       type: integer
 *                       example: 180
 *                     questionGroups:
 *                       type: array
 *                       items:
 *                         properties:
 *                           type:
 *                             type: string
 *                             example: "technical"
 *                           questions:
 *                             type: array
 *                             items:
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 421
 *                                 question:
 *                                   type: string
 *                                   example: "What is the primary purpose of React's virtual DOM?"
 *                                 questionType:
 *                                   type: string
 *                                   example: "mcq"
 *                                 skillId:
 *                                   type: integer
 *                                   example: 5
 *                                 skillTitle:
 *                                   type: string
 *                                   example: "React"
 *                                 options:
 *                                   type: object
 *                                   example: { "options": [{"id": "a", "text": "To improve security"}, {"id": "b", "text": "To optimize performance"}] }
 *                               type: object
 *                         type: object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 */
// this is public api where candidate can get all question to submit assessment
router.get(
  ROUTES.FINAL_ASSESSMENT.GET_FINAL_ASSESSMENT_BY_CANDIDATE,
  auth,
  queryValidation(candidateAssessmentIdValidation),
  HandleErrors(getFinalAssessmentByCandidate)
);

/**
 * @swagger
 * /final-assessment/candidate/assessment/submit:
 *   post:
 *     summary: Submit candidate answers for an assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *               - candidateEmail
 *               - answers
 *             properties:
 *               finalAssessmentId:
 *                 type: string
 *                 description: Encrypted ID of the assessment
 *                 example: "123"
 *               candidateEmail:
 *                 type: string
 *                 description: Email of the candidate
 *                 example: "<EMAIL>"
 *               candidateName:
 *                 type: string
 *                 description: Name of the candidate (optional)
 *                 example: "John Doe"
 *               answers:
 *                 type: array
 *                 items:
 *                   type: object
 *                   required:
 *                     - questionId
 *                     - answer
 *                   properties:
 *                     questionId:
 *                       type: number
 *                       description: ID of the question
 *                       example: 421
 *                     answer:
 *                       type: string
 *                       description: Candidate's answer to the question
 *                       example: "b"
 *                 example: [
 *                   {"questionId": 421, "answer": "b"},
 *                   {"questionId": 422, "answer": "a"}
 *                 ]
 *     responses:
 *       200:
 *         description: Assessment submitted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "assessment_submitted_successfully"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.SUBMIT_ASSESSMENT,
  HandleErrors(submitAssessment)
);

/**
 * @swagger
 * /final-assessment/assessment-status:
 *   post:
 *     summary: Get assessment status based on isShare and isSubmit flags
 *     tags: [Final Assessment]
 *     description: This API is no longer in use
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobApplicationId
 *             properties:
 *               jobApplicationId:
 *                 type: number
 *                 description: ID of the job application
 *                 example: 180
 *     responses:
 *       200:
 *         description: Assessment status fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "assessment_status_fetched"
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     assessmentId:
 *                       type: integer
 *                       example: 40
 *                     jobApplicationId:
 *                       type: integer
 *                       example: 180
 *                     isAssessmentSubmitted:
 *                       type: boolean
 *                       example: false
 *                     isAssessmentShared:
 *                       type: boolean
 *                       example: false
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 */

// this api is no longer in use
router.post(
  ROUTES.FINAL_ASSESSMENT.GET_ASSESSMENT_STATUS,
  auth,
  schemaValidation(getAssessmentStatusValidation),
  HandleErrors(getAssessmentStatus)
);

/**
 * @swagger
 * /final-assessment/candidate/verify-email:
 *   post:
 *     summary: Verify if candidate email exists in the system
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - token
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email of the candidate
 *                 example: "<EMAIL>"
 *               token:
 *                 type: string
 *                 description: Token for the final assessment
 *                 example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *     responses:
 *       200:
 *         description: Email verification successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "email_verification_successful"
 *                 code:
 *                   type: number
 *                   example: 200
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "email_not_found"
 *                 code:
 *                   type: number
 *                   example: 400
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.VERIFY_CANDIDATE_EMAIL,
  schemaValidation(verifyCandidateEmailValidation),
  HandleErrors(verifyCandidateEmail)
);

/**
 * @swagger
 * /final-assessment/assessment/generate-token:
 *   post:
 *     summary: Generate a JWT token for a final assessment
 *     tags: [Final Assessment]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - finalAssessmentId
 *             properties:
 *               finalAssessmentId:
 *                 type: number
 *                 description: ID of the final assessment
 *                 example: 216
 *     responses:
 *       200:
 *         description: Token generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Assessment token generated successfully"
 *                 code:
 *                   type: number
 *                   example: 200
 *                 data:
 *                   type: object
 *                   properties:
 *                     token:
 *                       type: string
 *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *                     finalAssessmentId:
 *                       type: number
 *                       example: 216
 *                     expiresIn:
 *                       type: string
 *                       example: "24 hours"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_data"
 *                 code:
 *                   type: number
 *                   example: 400
 *       401:
 *         description: Unauthorized - Invalid or missing authentication token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: number
 *                   example: 401
 *       404:
 *         description: Assessment not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "assessment_not_found"
 *                 code:
 *                   type: number
 *                   example: 404
 */
router.post(
  ROUTES.FINAL_ASSESSMENT.GENERATE_ASSESSMENT_TOKEN,
  auth,
  schemaValidation(generateAssessmentTokenValidation),
  HandleErrors(generateAssessmentToken)
);

export default router;
