/* eslint-disable camelcase */
import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from "typeorm";
import AddressModel from "./address";
import { USER_TYPE } from "../../utils/constants";
import MyCollectionModel from "./my_collection";

export interface IUserSchema {
  id: number;
  email: string;
  type?: string;
  phone: string;
  password: string;
  image: string;
  assessment_completed: boolean;
  allow_notification: boolean;
  stratum_point: number;
  is_deleted: boolean;
  country_code: string;
  country: string;
  dob: Date;
  verifyOtp: {
    otp: string;
    otp_created_ts: string;
  } | null;
  isVerified: boolean;
  created_ts: Date;
  fcm_tokens: string[] | null;
  time_zone: string;
  updated_ts: Date;
  daily_streak_last_processed_ts: Date | null;
  streak_end_last_processed_ts: Date | null;
}

@Entity("users")
class UserModel implements IUserSchema {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: "enum",
    enum: USER_TYPE,
    default: USER_TYPE.user,
  })
  account_type: string;

  @Column({
    length: 50,
  })
  first_name: string;

  @Column({
    length: 50,
  })
  last_name: string;

  @Column({
    length: 25,
  })
  gender: string;

  @Column({
    length: 50,
    unique: true,
  })
  email: string;

  @Column({
    length: 15,
  })
  phone: string;

  @Column()
  stratum_point: number;

  @Column({
    length: 255,
  })
  password: string;

  @Column()
  age: Number;

  @Column()
  country_code: string;

  @Column()
  country: string;

  @Column({ default: false })
  isVerified: boolean;

  @Column({ default: false })
  sms_notification: boolean;

  @Column({ type: "json", nullable: true })
  fcm_tokens: string[] | null;

  @Column({ nullable: true })
  time_zone: string;

  @Column({ default: true })
  allow_notification: boolean;

  @Column({ default: false })
  is_deleted: boolean;

  @Column({ type: "json", nullable: true })
  verifyOtp: { otp: string; otp_created_ts: string } | null;

  @Column({ default: false })
  assessment_completed: boolean;

  @Column({ type: "text" })
  image: string;

  @Column({ type: "timestamp", nullable: true })
  dob: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_ts: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  updated_ts: Date;

  @Column({ type: "timestamp", nullable: true })
  daily_streak_last_processed_ts: Date | null;

  @Column({ type: "timestamp", nullable: true })
  streak_end_last_processed_ts: Date | null;

  @OneToMany(() => AddressModel, (address) => address.user)
  address: AddressModel;

  @OneToMany(() => MyCollectionModel, (my_collection) => my_collection.user)
  my_collections: MyCollectionModel[];
}

export default UserModel;
