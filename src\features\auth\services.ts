/* eslint-disable camelcase */
/* eslint-disable no-async-promise-executor */
/* eslint-disable no-promise-executor-return */

import { HmacSHA256, enc } from "crypto-js";
import bcrypt from "bcryptjs";

import {
  MESSAGE_TYPE,
  API_RESPONSE_MSG,
  PASSWORD_HASH_LENGTH,
  REDIS_EXPIRY,
  REDIS_KEYS,
  ACTIVITY_LOG_TYPE,
  ENTITY_TYPE,
  USER_TYPE,
  DEFAULT_COUNTRY_CODE,
  DEFAULT_COUNTRY,
  DEFAULT_ORG_USERS_COUNT,
  OTP_TYPE,
  DEFAULT_USER_COLLECTIONS,
} from "../../utils/constants";
import { addActivityLog, handleSentryError } from "../../utils/helper";
import DbConnection from "../../db/dbConnection";
import {
  IForgotPassword,
  ILogin,
  IR<PERSON>tPassword,
  ISignup,
  IVerify,
} from "./interface";

import createToken from "../../middleware/generate";
import { getSecretKeys } from "../../config/awsConfig";
import UserModel from "../../schema/s9/user";
import sendVerificationMail from "../../utils/verificationEmail";
import Employee from "../../schema/s9-innerview/employees";
import RolePermission from "../../schema/s9-innerview/role_permissions_mapping";
import Cache from "../../db/cache";
import OrganizationModel from "../../schema/s9/organization";
import SubscriptionServices from "../subscriptions/services";
import AddressModel from "../../schema/s9/address";
import OrganizationBranchesModel from "../../schema/s9/organization_branches";
import AccessManagementServices from "../accessManagement/services";
import MyCollectionModel from "../../schema/s9/my_collection";
import signupEmailTemplate from "../../utils/hiringEmailTemplate";

export interface IPresignedData {
  filePath: string;
  fileFormat: string;
}

export class AuthServices {
  /**
   * find email.
   *
   * @param AuthParams
   */
  static getUserByEmail = (email: string) =>
    new Promise<UserModel>(async (resolve, reject) => {
      try {
        return DbConnection.getS9DataSource()
          .then(async (dataConnection) => {
            const userRepository = dataConnection.getRepository(UserModel);
            const user = await userRepository.findOne({ where: { email } });
            resolve(user);
            return user;
          })
          .catch((error) => {
            handleSentryError(error, "getUserByEmail 1");
            // Handle any other errors that occur during the database operation

            reject(error);
            return error;
          });
      } catch (e) {
        handleSentryError(e, "getUserByEmail 2");
        return e;
      }
    });

  /**
   * find user by id
   *
   * @param AuthParams
   */
  static getUserByUserId = async (userId: number) => {
    const dataConnection = await DbConnection.getS9DataSource();
    const repository = dataConnection.getRepository(UserModel);
    const user = await repository.findOne({
      where: { id: Number(userId) },
    });
    return user;
  };

  /**
   * get otp from cache
   *
   * @param email
   */
  static getOtpFromCache = async (email: string) => {
    const cache = new Cache();
    const cachedOtp = await cache.get(
      REDIS_KEYS.USER_OTP_KEY.replace("{email}", String(email))
    );
    console.log("cachedOtp", cachedOtp);
    const verifyOtp = cachedOtp ? JSON.parse(cachedOtp) : null;
    return verifyOtp;
  };

  /**
   * Verify Otp
   * @param data
   * @returns
   */
  static verifyOtp = async (data: IVerify & ISignup) => {
    try {
      const { email, otp, type } = data;
      const cache = new Cache();

      console.log("data============>>>>>>>>>>>>", data);

      if (!otp && !email) {
        return {
          success: false,
          message: API_RESPONSE_MSG.invalid_data,
        };
      }

      let userDetail;
      if (type === OTP_TYPE.FORGOT_PASSWORD) {
        userDetail = await AuthServices.getUserByEmail(email);

        if (!userDetail) {
          return {
            success: false,
            message: API_RESPONSE_MSG.user_not_found,
          };
        }
      }

      const verifyOtp = await AuthServices.getOtpFromCache(email);
      console.log("verifyOtp service", verifyOtp);
      const keys = await getSecretKeys();
      const newOtpEncryption = enc.Base64.stringify(
        HmacSHA256(otp.toString(), keys.otp_enc_key)
      );

      if (!verifyOtp) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_expired,
        };
      }

      if (newOtpEncryption !== verifyOtp?.otp) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_otp,
        };
      }

      const dataConnection = await DbConnection.getS9DataSource();
      const repository = dataConnection.getRepository(UserModel);

      // only update user isVerified and updated_ts if type is forgot_password
      if (userDetail && userDetail.id && type === OTP_TYPE.FORGOT_PASSWORD) {
        await repository.update(userDetail.id, {
          isVerified: true,
          updated_ts: new Date(),
        });
      }

      // if type is signup then call signup function
      if (type === OTP_TYPE.SIGNUP) {
        const signupResponse = await AuthServices.signup(data);
        console.log("signupResponse", signupResponse);

        // Clear OTP from Redis after successful verification

        await cache.del(
          REDIS_KEYS.USER_OTP_KEY.replace("{email}", String(email))
        );

        if (signupResponse.success) {
          return {
            success: true,
            message: API_RESPONSE_MSG.signup_successful,
          };
        }

        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
        };
      }

      return {
        data: newOtpEncryption,
        success: true,
        message: API_RESPONSE_MSG.otp_verified,
      };
    } catch (error) {
      handleSentryError(error, "verifyOtp");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Resend Otp
   * @param data
   * @returns
   */
  static resendOtp = async (data: IVerify) => {
    try {
      const { email, type, name } = data;
      console.log("resendOtp data", data);

      const { success } = await this.sendVerificationEmail({
        email,
        type,
        name,
      });

      if (!success) {
        return {
          success: false,
          message: API_RESPONSE_MSG.otp_sending_failed,
        };
      }

      return {
        success: true,
        message: API_RESPONSE_MSG.verification_code_sent,
      };
    } catch (error) {
      handleSentryError(error, "resendOtp");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * User login
   * @param data login object
   * @returns
   */

  static login = async (data: ILogin) => {
    try {
      const { email, password } = data;
      // Query user details along with their address
      const userRepository =
        await DbConnection.getS9DatabaseRepository(UserModel);

      const userDetails = await userRepository.findOne({
        where: {
          email,
        },
        select: [
          "id",
          "email",
          "account_type",
          "password",
          "isVerified",
          "sms_notification",
          "allow_notification",
          "is_deleted",
          "image",
          "first_name",
          "last_name",
          "created_ts",
        ],
      });
      if (!userDetails || !userDetails.isVerified) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password, // to show message like "Email or password is incorrect"
        };
      }

      const employeeRepository =
        await DbConnection.getS9InnerViewDatabaseRepository(Employee);

      const employeeDetails = await employeeRepository
        .createQueryBuilder("employee")
        .leftJoinAndSelect("employee.role", "role")
        .leftJoinAndSelect("employee.department", "department")
        .where("employee.userId = :userId", { userId: userDetails.id })
        .select([
          "employee.id as employeeId",
          "employee.roleId as roleId",
          "employee.organizationId as organizationId",
          "employee.isActive as isActive",
          "employee.departmentId as departmentId",
          "role.id as roleId",
          "role.name as roleName",
          "role.isActive as roleIsActive",
          "department.id as departmentId",
          "department.name as departmentName",
          "department.isActive as departmentIsActive",
        ])
        .getRawOne();

      // Get organization details
      const organizationRepository =
        await DbConnection.getS9DatabaseRepository(OrganizationModel);
      const organizationDetails = await organizationRepository.findOne({
        where: {
          id: employeeDetails.organizationId,
        },
        select: ["id", "name", "organization_code"],
      });

      // get the current subscription plan for the organization with subscription and pricing details
      const orgSubscriptionInfo =
        await SubscriptionServices.getCurrentSubscription(
          employeeDetails.organizationId
        );

      if (!employeeDetails || !employeeDetails.isActive) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password, // to show message like "Email or password is incorrect"
        };
      }

      const checkPassword = await bcrypt.compare(
        password.trim(),
        userDetails.password
      );

      if (!checkPassword) {
        return {
          success: false,
          message: API_RESPONSE_MSG.wrong_password,
        };
      }

      const token = await createToken({
        email: userDetails.email,
        id: userDetails.id,
        orgId: employeeDetails.organizationId,
        roleId: employeeDetails.roleId,
        departmentId: employeeDetails.departmentId,
        type: userDetails.account_type,
      });

      delete userDetails.password;
      delete userDetails.fcm_tokens;

      const rolePermissionRepository =
        await DbConnection.getS9InnerViewDatabaseRepository(RolePermission);

      const permissions = await rolePermissionRepository
        .createQueryBuilder("rolePermission")
        .leftJoinAndSelect("rolePermission.permission", "permission")
        .where("rolePermission.roleId = :roleId", {
          roleId: employeeDetails.roleId,
        })
        .select(["permission.slug as permissionSlug"])
        .getRawMany();
      const permissionSlugs = permissions.map((perm) => perm.permissionSlug);

      const userInfo = {
        token,
        authData: {
          userData: {
            ...userDetails,
            orgId: employeeDetails.organizationId,
            organizationName: organizationDetails.name,
            logo: organizationDetails.logo || null,
            organizationCode: organizationDetails.organization_code,
            createdTs: userDetails.created_ts.toISOString(),
          },
          department: {
            departmentId: employeeDetails.departmentId,
            departmentName: employeeDetails.departmentName,
            departmentIsActive: employeeDetails.departmentIsActive,
          },
          role: {
            roleId: employeeDetails.roleId,
            roleName: employeeDetails.roleName,
            roleIsActive: employeeDetails.roleIsActive,
          },
          permissions: permissionSlugs, // Now just an array of strings
          currentPlan: orgSubscriptionInfo.data,
        },
      };
      const cache = new Cache();
      const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
        "{userId}",
        String(userDetails?.id)
      );
      const permissionsKey = REDIS_KEYS.ROLE_PERMISSIONS.replace(
        "{roleId}",
        String(employeeDetails?.roleId)
      );

      // Store the session token
      await cache.lPush(sessionKey, token);

      // Store permission slugs in Redis
      // Delete any existing permissions first
      await cache.del(permissionsKey);

      // We've already extracted permissionSlugs above for the user response

      // Store permission slugs in Redis with DEFAULT_REDIS_EXPIRY from constants
      await cache.set(
        permissionsKey,
        JSON.stringify(permissionSlugs),
        REDIS_EXPIRY.DEFAULT
      );

      // Insert activity log after successful login using helper
      try {
        await addActivityLog({
          orgId: employeeDetails.organizationId,
          logType: ACTIVITY_LOG_TYPE.LOGIN,
          userId: userDetails.id,
          entityId: userDetails.id,
          entityType: ENTITY_TYPE.USER,
          oldValue: null,
          newValue: null,
          comments: `User successfully logged.`,
        });
      } catch (activityLogError) {
        console.log("Error adding activity log:", activityLogError);

        handleSentryError(activityLogError, "login 1");
      }
      return {
        success: true,
        message: API_RESPONSE_MSG.login_successful,
        data: userInfo,
      };
    } catch (error) {
      console.log("Login Error", error);

      handleSentryError(error, "login 2");
      return {
        success: false,
        message: API_RESPONSE_MSG.login_failed,
      };
    }
  };

  /**
   * Forgot Password
   * @param data - Object containing phone number
   * @returns An object with success status, message, and data (if applicable)
   */
  static forgotPassword = async (data: IForgotPassword) => {
    try {
      const { email } = data;

      return await this.sendVerificationEmail({
        email,
        type: OTP_TYPE.FORGOT_PASSWORD,
      });
    } catch (error) {
      console.log("forgotPassword error", error);
      handleSentryError(error, "forgotPassword");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Check org exist with given code and name also whether the user is accociated with any org.
   *
   * @param data - Object containing organization code and name
   * @returns An object with success status, message, and data (if applicable)
   */
  static checkUserOrgExist = async (data: ISignup) => {
    try {
      const { email, organizationCode, organizationName, firstName } = data;

      const addressRepo =
        await DbConnection.getS9DatabaseRepository(AddressModel);
      const organizationRepo =
        await DbConnection.getS9DatabaseRepository(OrganizationModel);

      const existingOrganizationCode = await organizationRepo.findOne({
        where: { organization_code: organizationCode },
      });

      console.log("existingOrganizationCode====", existingOrganizationCode);

      if (existingOrganizationCode) {
        return {
          success: false,
          message: API_RESPONSE_MSG.organization_code_exist,
        };
      }
      const existingOrganizationName = await organizationRepo.findOne({
        where: { name: organizationName },
      });
      console.log("existingOrganizationName====", existingOrganizationName);
      if (existingOrganizationName) {
        return {
          success: false,
          message: API_RESPONSE_MSG.org_name_exist,
        };
      }

      const userDetail = await AuthServices.getUserByEmail(email);

      if (userDetail) {
        const address = await addressRepo.findOne({
          where: { user_id: userDetail.id },
        });

        console.log("address====>>>>>", address);

        // if user is already registered with any organization
        if (address && address.organization_id) {
          return {
            success: false,
            message:
              API_RESPONSE_MSG.user_already_associated_with_other_organization,
          };
        }

        return {
          success: false,
          message: API_RESPONSE_MSG.success,
          data: {
            userAlreadyExistWithoutOrg: true,
          },
        };
      }

      // send otp to user
      return await this.sendVerificationEmail({
        email,
        type: OTP_TYPE.SIGNUP,
        name: firstName,
      });
    } catch (error) {
      console.log("checkUserOrgExist error", error);
      handleSentryError(error, "checkUserOrgExist");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static signup = async (data: ISignup) => {
    try {
      const {
        email,
        password,
        organizationCode,
        organizationName,
        firstName,
        lastName,
        location,
        websiteURL,
        branchName,
        branchCode,
        tinNumber,
        passwordType,
      } = data;

      console.log("signup data======>>>>>", data);
      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);
      const addressRepo =
        await DbConnection.getS9DatabaseRepository(AddressModel);
      const organizationRepo =
        await DbConnection.getS9DatabaseRepository(OrganizationModel);
      const orgBranchesRepo = await DbConnection.getS9DatabaseRepository(
        OrganizationBranchesModel
      );
      const userDetail = await AuthServices.getUserByEmail(email);

      const contactPerson = `${userDetail?.first_name ? userDetail?.first_name : (firstName ?? "User")} ${userDetail?.last_name ? userDetail?.last_name : (lastName ?? "")}`;

      console.log("contactPerson", contactPerson);

      const organization = new OrganizationModel();
      organization.name = organizationName;
      organization.contact_person = contactPerson;
      organization.email = email.toLowerCase();
      organization.organization_code = organizationCode;
      organization.domain = websiteURL;
      organization.tinNumber = tinNumber;
      organization.location = location;
      organization.users = DEFAULT_ORG_USERS_COUNT;

      const result = await organizationRepo.save(organization);

      console.log("organization result", result);

      const orgBranches = new OrganizationBranchesModel();
      orgBranches.branchName = branchName;
      orgBranches.branchCode = branchCode;
      orgBranches.orgId = result.id;
      await orgBranchesRepo.save(orgBranches);

      console.log("orgBranches result", orgBranches);

      let userId: number;

      if (!userDetail) {
        const encPassword = await bcrypt.hash(password, PASSWORD_HASH_LENGTH);
        const newUser = new UserModel();
        newUser.email = email.toLowerCase();
        newUser.first_name = firstName ?? "";
        newUser.last_name = lastName ?? "";
        newUser.password = encPassword;
        newUser.account_type = USER_TYPE.user;
        newUser.created_ts = new Date();
        newUser.updated_ts = new Date();
        newUser.country_code = DEFAULT_COUNTRY_CODE;
        newUser.country = DEFAULT_COUNTRY;
        newUser.isVerified = true;
        const userResult = await userRepo.save(newUser);

        userId = userResult.id;

        console.log("newUser====", newUser, "userResult====", userResult);

        if (userResult) {
          const addressData = new AddressModel();
          addressData.organization_code = organizationCode;
          addressData.organization_id = result?.id;
          addressData.user_id = userId;
          addressData.created_ts = new Date();
          addressData.updated_ts = new Date();
          const addressRes = await addressRepo.save(addressData);
          console.log("addressRes====", addressRes);
        }

        const myCollectionRepo =
          await DbConnection.getS9DatabaseRepository(MyCollectionModel);

        const defaultCollectionArray = [];

        DEFAULT_USER_COLLECTIONS.forEach((collection) => {
          const myCollection = new MyCollectionModel();

          myCollection.name = collection;
          myCollection.user_id = userId;
          myCollection.created_ts = new Date();
          myCollection.updated_ts = new Date();
          myCollection.is_protected = true;

          defaultCollectionArray.push(myCollection);
        });

        // Bulk insertion
        myCollectionRepo.save(defaultCollectionArray);

        await signupEmailTemplate({
          email: userResult.email.toLowerCase(),
          name: contactPerson,
          type: USER_TYPE.new,
          password,
        });
      } else {
        // eslint-disable-next-line no-unused-expressions
        userId = userDetail?.id;

        // Update password if new
        if (passwordType === USER_TYPE.new) {
          console.log("inside new", passwordType);
          const encPassword = await bcrypt.hash(password, PASSWORD_HASH_LENGTH);
          await userRepo
            .createQueryBuilder()
            .update()
            .set({
              password: encPassword,
              updated_ts: new Date(),
            })
            .where("id = :userId", { userId })
            .execute();
        }

        // Update address table
        await addressRepo
          .createQueryBuilder()
          .update()
          .set({
            organization_code: organizationCode,
            organization_id: result?.id,
            updated_ts: new Date(),
          })
          .where("user_id = :user_id", { user_id: userId })
          .execute();

        await signupEmailTemplate({
          email: userDetail.email.toLowerCase(),
          name: contactPerson,
        });
      }

      await AccessManagementServices.addRoleDepartmentAndPermissionForAdmin({
        organizationId: result.id,
        userId,
      });

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      console.log("signup error", error);
      handleSentryError(error, "signup");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  /**
   * Reset Password
   * @param data - Object containing new password and phone number
   * @returns An object with success status and message
   */
  static resetPassword = async (data: IResetPassword) => {
    try {
      const { password, email, otp } = data;
      console.log("reset data", data);

      const dataConnection = await DbConnection.getS9DataSource();
      const repository = dataConnection.getRepository(UserModel);
      const user = await AuthServices.getUserByEmail(email);

      if (!user) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }

      const verifyOtp = await AuthServices.getOtpFromCache(email);
      console.log("verifyOtp reset password", verifyOtp);
      const keys = await getSecretKeys();
      const newOtpEncryption = enc.Base64.stringify(
        HmacSHA256(otp.toString(), keys.otp_enc_key)
      );

      if (!verifyOtp || newOtpEncryption !== verifyOtp?.otp) {
        return {
          success: false,
          message: API_RESPONSE_MSG.failed,
        };
      }

      if (password) {
        const encPassword = await bcrypt.hash(password, PASSWORD_HASH_LENGTH);
        user.password = encPassword;
      }
      user.updated_ts = new Date();
      await repository.save(user);

      return {
        success: true,
        message: API_RESPONSE_MSG.password_updated,
      };
    } catch (error) {
      handleSentryError(error, "resetPassword");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static deleteSession = async (token: string, userId: number) => {
    try {
      const cache = new Cache();
      const sessionKey = REDIS_KEYS.USER_SESSIONS.replace(
        "{userId}",
        String(userId)
      );

      await cache.lRem(sessionKey, token);

      return {
        success: true,
        message: API_RESPONSE_MSG.user_session_deleted,
      };
    } catch (error) {
      handleSentryError(error, "deleteSession");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateTimeZone = async (data: {
    timezone: string;
    userId: number;
  }) => {
    try {
      const { userId, timezone } = data;
      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);
      await userRepo
        .createQueryBuilder()
        .update(UserModel)
        .set({
          time_zone: timezone,
        })
        .where("id = :userId", { userId })
        .execute();

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
      };
    } catch (error) {
      handleSentryError(error, "updateTimeZone");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static sendVerificationEmail = async (data: {
    email: string;
    type: string;
    name?: string;
  }) => {
    try {
      const keys = await getSecretKeys();
      const { email, type, name } = data;

      const currentTimeStamp = new Date().getTime();

      let firstName: string;
      if (type === OTP_TYPE.FORGOT_PASSWORD) {
        const userDetails = await this.getUserByEmail(email);

        if (!userDetails) {
          return {
            success: false,
            message: API_RESPONSE_MSG.user_not_found,
          };
        }

        firstName = userDetails?.first_name;
      } else {
        firstName = name;
      }

      const { otp, response } = await sendVerificationMail({
        email,
        firstname: firstName,
        type,
      });
      // Encrypt the OTP using a secret key
      if ((await response).message === MESSAGE_TYPE.SENT) {
        const encryptedOtp = enc.Base64.stringify(
          HmacSHA256(otp?.toString(), keys.otp_enc_key)
        );

        const verifyOtp = {
          otp: encryptedOtp,
          otp_created_ts: currentTimeStamp.toString(),
        };

        const cache = new Cache();
        cache.set(
          REDIS_KEYS.USER_OTP_KEY.replace("{email}", String(email)),
          JSON.stringify(verifyOtp),
          60 * 5
        );

        return {
          success: true,
          message: API_RESPONSE_MSG.verification_code_sent,
        };
      }

      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    } catch (error) {
      console.log("sendVerificationEmail error", error);

      handleSentryError(error, "sendVerificationEmail");
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  // static getPredictions = async (searchText: string) => {
  //   const config: GoogleAPIParams = {
  //     url: "https://maps.googleapis.com/maps/api/place/autocomplete/json",
  //     method: "GET",
  //     params: {
  //       input: searchText,
  //     },
  //   };
  //   const response = await googleAPI(config as GoogleAPIParams);
  //   if (!response?.data) {
  //     return {
  //       success: false,
  //       message: "error_while_fetching_records",
  //     };
  //   }
  //   return {
  //     success: true,
  //     message: "records_found_successfully",
  //     data: response.data,
  //   };
  // };
}

export default AuthServices;
