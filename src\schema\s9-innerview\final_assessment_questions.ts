import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON><PERSON>umn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import { FinalAssessmentsModel } from "./final_assessments";
import { QuestionType } from "../../utils/constants";
import SkillsModel from "./skills";

@Entity("final_assessment_questions")
export class FinalAssessmentQuestionsModel {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => FinalAssessmentsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "final_assessment_id" })
  @Column({ name: "final_assessment_id", nullable: false })
  finalAssessmentId: number;

  @ManyToOne(() => SkillsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "skill_id" })
  @Column({ name: "skill_id", nullable: true })
  skillId: number;

  @Column({ name: "question", length: 255 })
  question: string;

  @Column({ name: "question_type", type: "enum", enum: QuestionType })
  questionType: QuestionType;

  @Column({ name: "options", type: "json", nullable: true })
  options: object;

  @Column({ name: "correct_answer", type: "text", nullable: true })
  correctAnswer: string;

  @Column({ name: "applicant_answer", type: "text", nullable: true })
  applicantAnswer: string;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default FinalAssessmentQuestionsModel;
