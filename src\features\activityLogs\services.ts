import { In } from "typeorm";
import dbConnection from "../../db/dbConnection";
import { ResponseObject } from "../../interface/commonInterface";

import { ACTIVITY_LOG_TYPE, DEFAULT_LIMIT } from "../../utils/constants";
import ActivityLogModel from "../../schema/s9-innerview/activity_logs";
import UserModel from "../../schema/s9/user";
import { handleSentryError } from "../../utils/helper";

export class CandidateApplicationService {
  static async getActivityLogsByOrgId(
    orgId: number,
    logType?: string,
    limit: number = DEFAULT_LIMIT,
    offset: number = 0
  ): Promise<ResponseObject> {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const activityLogsRepo = dataSource.getRepository(ActivityLogModel);

      let query = activityLogsRepo
        .createQueryBuilder("activity_logs")
        .where("activity_logs.org_id = :orgId", { orgId });

      if (logType && Object.values(ACTIVITY_LOG_TYPE).includes(logType)) {
        query = query.andWhere("activity_logs.log_type = :logType", {
          logType,
        });
      }

      const data = await query
        .orderBy("activity_logs.timestamp", "DESC")
        .offset(offset)
        .limit(limit)
        .getMany();

      // Fetch unique userIds from logs
      const userIds = Array.from(
        new Set(data.map((log) => log.userId).filter((id) => !!id))
      );
      const dataConnection = await dbConnection.getS9DataSource();

      const repository = dataConnection.getRepository(UserModel);

      // get all users with the fetched userIds
      if (userIds.length === 0) {
        return {
          success: true,
          message: "activity_logs_fetched",
          data: [],
        };
      }

      // get all users with the fetched userIds using in query
      const allUsers = await repository.find({
        where: {
          id: In(userIds),
        },
        select: {
          id: true,
          first_name: true,
          last_name: true,
        },
      });

      // Attach actionByName to each log
      const logsWithActionByName = data.map((log) => {
        const currentUser = allUsers.find((user) => user.id === log.userId);
        return {
          ...log,
          actionByName:
            `${currentUser?.first_name || ""} ${currentUser?.last_name || ""}`.trim(),
        };
      });

      return {
        success: true,
        message: "activity_logs_fetched",
        data: logsWithActionByName,
      };
    } catch (error) {
      handleSentryError(error, "getActivityLogsByOrgId");
      return {
        success: false,
        message: "activity_logs_fetch_failed",
        error: error.message,
      };
    }
  }
}

export default CandidateApplicationService;
