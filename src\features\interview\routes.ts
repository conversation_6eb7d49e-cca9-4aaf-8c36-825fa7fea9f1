import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  updateOrScheduleInterview,
  getInterviewers,
  getMyInterviews,
  getUpcomingOrPastInterviews,
  addInterviewSkillQuestion,
  getInterviewSkillQuestions,
  updateInterviewSkillQuestion,
  getJobList,
  getCandidateList,
  updateInterviewAnswers,
  endInterview,
  conductInterviewStaticInformation,
  getInterviewFeedback,
  updateInterviewFeedback,
  getPendingInterviews,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";
import {
  getInterviewersValidation,
  updateOrScheduleInterviewValidation,
  getMyInterviewsValidation,
  getInterviewSkillQuestionsValidation,
  updateInterviewSkillQuestionValidation,
  addInterviewSkillQuestionValidation,
  getJobListValidation,
  getCandidateListValidation,
  updateInterviewAnswersValidation,
  endInterviewValidation,
  updateInterviewFeedbackValidation,
  getInterviewFeedbackValidation,
} from "./vaildation";
import {
  schemaValidation,
  queryValidation,
  paramsValidation,
} from "../../middleware/validateSchema";
import {
  authorizedForManagePreInterviewQuestions,
  authorizedForScheduleConductInterviews,
} from "../../middleware/isAuthorized";

const interviewRoutes = express.Router();

/**
 * @swagger
 * /interview/update-or-schedule-interview:
 *   post:
 *     summary: Schedule or update an interview
 *     description: Creates a new interview or updates an existing one
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - title
 *               - interviewerId
 *               - jobApplicationId
 *               - scheduleAt
 *               - startTime
 *               - endTime
 *               - roundType
 *             properties:
 *               title:
 *                 type: string
 *                 description: Title of the interview
 *                 example: "Technical Interview Round 1"
 *               interviewerId:
 *                 type: integer
 *                 description: ID of the interviewer
 *                 example: 123
 *               jobApplicationId:
 *                 type: integer
 *                 description: ID of the job application
 *                 example: 456
 *               scheduleAt:
 *                 type: number
 *                 description: Timestamp in milliseconds when the interview was scheduled
 *                 example: 1695867600000
 *               startTime:
 *                 type: number
 *                 description: Interview start time timestamp in milliseconds
 *                 example: 1695870000000
 *               endTime:
 *                 type: number
 *                 description: Interview end time timestamp in milliseconds
 *                 example: 1695873600000
 *               roundType:
 *                 type: string
 *                 description: Type of interview round
 *                 example: "Video Call"
 *               jobId:
 *                 type: integer
 *                 description: ID of the job (optional)
 *                 example: 789
 *               description:
 *                 type: string
 *                 description: Additional details about the interview (optional)
 *                 example: "Please prepare to discuss your experience with React"
 *               fileUrlArray:
 *                 type: string
 *                 description: JSON stringified array of file URLs (optional)
 *                 example: "[\"https://example.com/file1.pdf\", \"https://example.com/file2.pdf\"]"
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview (only for updating existing interviews)
 *                 example: 101
 *     responses:
 *       200:
 *         description: Interview scheduled or updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interview_updated_successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 350
 *                     title:
 *                       type: string
 *                       example: "Round1"
 *                     description:
 *                       type: string
 *                       example: ""
 *                     attachments:
 *                       type: object
 *                       properties:
 *                         fileUrls:
 *                           type: array
 *                           items:
 *                             type: string
 *                           example: []
 *                     isCanceled:
 *                       type: boolean
 *                       example: false
 *                     jobId:
 *                       type: integer
 *                       example: 176
 *                     interviewerId:
 *                       type: integer
 *                       example: 1234
 *                     scheduledBy:
 *                       type: integer
 *                       example: 844
 *                     jobApplicationId:
 *                       type: integer
 *                       example: 650
 *                     scheduleAt:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-17T09:12:45.601Z"
 *                     startTime:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-20T18:30:00.000Z"
 *                     endTime:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-20T19:30:00.000Z"
 *                     recordingUrl:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     roundNumber:
 *                       type: integer
 *                       example: 1
 *                     roundType:
 *                       type: string
 *                       example: "Video Call"
 *                     hardSkillMarks:
 *                       type: integer
 *                       example: 0
 *                     transcriptText:
 *                       type: string
 *                       example: ""
 *                     applicantAiBehavioralAnalysis:
 *                       type: string
 *                       example: ""
 *                     applicantBehavioralNotes:
 *                       type: string
 *                       example: ""
 *                     interviewerPerformanceNotes:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     interviewSummary:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     channelName:
 *                       type: string
 *                       example: "interview_Round1_928869"
 *                     isEnded:
 *                       type: boolean
 *                       example: false
 *                     isAllowedForNextRound:
 *                       type: boolean
 *                       example: false
 *                     isFeedbackFilled:
 *                       type: boolean
 *                       example: false
 *                     aiDecisionForNextRound:
 *                       type: string
 *                       nullable: true
 *                       example: null
 *                     interviewEndTime:
 *                       type: string
 *                       format: date-time
 *                       nullable: true
 *                       example: null
 *                     agoraAdditionalFilesDeleted:
 *                       type: boolean
 *                       example: false
 *                     createdTs:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-03T09:39:56.653Z"
 *                     updatedTs:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-17T09:19:29.457Z"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       403:
 *         description: Forbidden - user doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden_access"
 *                 code:
 *                   type: integer
 *                   example: 403
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_OR_SCHEDULE_INTERVIEW,
  auth,
  authorizedForScheduleConductInterviews,
  schemaValidation(updateOrScheduleInterviewValidation),
  HandleErrors(updateOrScheduleInterview)
);

/**
 * @swagger
 * /interview/get-my-interviews:
 *   get:
 *     summary: Get user's interviews
 *     description: Retrieves all interviews associated with the current user
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: monthYear
 *         schema:
 *           type: string
 *         description: Month and year in format MM-YYYY (e.g., 09-2025)
 *     responses:
 *       200:
 *         description: Interviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interviews_fetched"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 61
 *                       title:
 *                         type: string
 *                         example: "checking"
 *                       start:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-02T19:30:00.000Z"
 *                       end:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-02T20:30:00.000Z"
 *                       roundNumber:
 *                         type: integer
 *                         example: 1
 *                       roundType:
 *                         type: string
 *                         example: "Video Call"
 *                       isEnded:
 *                         type: integer
 *                         example: 1
 *                       scheduleAt:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-08-27T10:42:21.000Z"
 *                       jobApplicationId:
 *                         type: integer
 *                         example: 306
 *                       attachments:
 *                         type: string
 *                         example: "{\"fileUrls\": []}"
 *                       interviewerId:
 *                         type: integer
 *                         example: 1018
 *                       description:
 *                         type: string
 *                         example: ""
 *                       resumeLink:
 *                         type: string
 *                         example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/ruchika.pdf-resume-1753348805444.pdf"
 *                       candidateName:
 *                         type: string
 *                         example: "ruchika rajawat"
 *                       candidateId:
 *                         type: integer
 *                         example: 214
 *                       candidateEmail:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       channelName:
 *                         type: string
 *                         example: "interview_checking_487039"
 *                       jobTitle:
 *                         type: string
 *                         example: "Senior Python Developer"
 *                       jobUniqueId:
 *                         type: string
 *                         example: "ORG-SE-0009"
 *                       jobId:
 *                         type: integer
 *                         example: 83
 *                       roleName:
 *                         type: string
 *                         example: "APM TEST02"
 *                       interviewerName:
 *                         type: string
 *                         example: "John Doe1018"
 *                       skillQuestionsGenerated:
 *                         type: boolean
 *                         example: true
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *       401:
 *         description: Unauthorized access
 *       500:
 *         description: Server error
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_MY_INTERVIEWS,
  auth,
  queryValidation(getMyInterviewsValidation),
  HandleErrors(getMyInterviews)
);

/**
 * @swagger
 * /interview/get-interviewers:
 *   get:
 *     summary: Get available interviewers
 *     description: Retrieves a list of available interviewers for a job
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: Job ID to get interviewers for
 *       - in: query
 *         name: searchString
 *         schema:
 *           type: string
 *         description: Optional search string to filter interviewers
 *     responses:
 *       200:
 *         description: Interviewers retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interviewers_fetched"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       label:
 *                         type: string
 *                         example: "John Doe907 (Associate Project Manager)"
 *                       value:
 *                         type: integer
 *                         example: 907
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_parameters"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEWERS,
  auth,
  queryValidation(getInterviewersValidation),
  HandleErrors(getInterviewers)
);

/**
 * @swagger
 * /interview/get-interview-skill-questions:
 *   get:
 *     summary: Get interview skill questions
 *     description: Retrieves skill questions for a specific interview
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobApplicationId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the job application
 *       - in: query
 *         name: interviewId
 *         schema:
 *           type: integer
 *         description: ID of the interview
 *     responses:
 *       200:
 *         description: Interview skill questions retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     roleSpecificQuestions:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           questions:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 10506
 *                                 jobSkillId:
 *                                   type: integer
 *                                   example: 2774
 *                                 interviewId:
 *                                   type: integer
 *                                   nullable: true
 *                                   example: null
 *                                 jobApplicationId:
 *                                   type: integer
 *                                   example: 663
 *                                 question:
 *                                   type: string
 *                                   example: "Describe a time you had to simplify complex information for a customer."
 *                                 answer:
 *                                   type: string
 *                                   example: ""
 *                                 questionType:
 *                                   type: string
 *                                   example: "role_specific"
 *                                 skillId:
 *                                   type: integer
 *                                   example: 28
 *                                 skillTitle:
 *                                   type: string
 *                                   example: "Communication"
 *                           interviewerName:
 *                             type: string
 *                             example: ""
 *                     cultureSpecificQuestions:
 *                       type: object
 *                       additionalProperties:
 *                         type: object
 *                         properties:
 *                           questions:
 *                             type: array
 *                             items:
 *                               type: object
 *                               properties:
 *                                 id:
 *                                   type: integer
 *                                   example: 10536
 *                                 question:
 *                                   type: string
 *                                   example: "Can you describe a time when you went above and beyond?"
 *                                 questionType:
 *                                   type: string
 *                                   example: "culture_specific"
 *                           interviewerName:
 *                             type: string
 *                             example: ""
 *                     careerBasedQuestions:
 *                       type: object
 *                       properties:
 *                         questions:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               id:
 *                                 type: integer
 *                                 example: 10501
 *                               question:
 *                                 type: string
 *                                 example: "Can you describe a time when you provided support to a customer?"
 *                               questionType:
 *                                 type: string
 *                                 example: "career_based"
 *                         score:
 *                           type: integer
 *                           example: 10
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_parameters"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEW_SKILL_QUESTIONS,
  auth,
  queryValidation(getInterviewSkillQuestionsValidation),
  HandleErrors(getInterviewSkillQuestions)
);

/**
 * @swagger
 * /interview/update-interview-skill-question:
 *   post:
 *     summary: Update interview skill question
 *     description: Updates an existing interview skill question
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *               - interviewQuestionId
 *               - question
 *             properties:
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview
 *               interviewQuestionId:
 *                 type: integer
 *                 description: ID of the question to update
 *               question:
 *                 type: string
 *                 description: Updated question text
 *     responses:
 *       200:
 *         description: Interview skill question updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interview_question_updated"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       403:
 *         description: Forbidden - user doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden_access"
 *                 code:
 *                   type: integer
 *                   example: 403
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_SKILL_QUESTION,
  auth,
  authorizedForManagePreInterviewQuestions,
  schemaValidation(updateInterviewSkillQuestionValidation),
  HandleErrors(updateInterviewSkillQuestion)
);

/**
 * @swagger
 * /interview/add-interview-skill-question:
 *   post:
 *     summary: Add interview skill question
 *     description: Adds a new skill question to an interview
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobApplicationId
 *               - question
 *               - skillType
 *             properties:
 *               jobApplicationId:
 *                 type: integer
 *                 description: ID of the job application
 *                 example: 815
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview
 *                 example: 428
 *               question:
 *                 type: string
 *                 description: Question text
 *                 example: "Tell us about a situation where you guided a team through a challenge. What did you do to support your team members?"
 *               skillType:
 *                 type: string
 *                 description: Type of skill being assessed
 *                 example: "career_based"
 *               jobSkillId:
 *                 type: integer
 *                 description: ID of the job skill
 *                 example: 3269
 *               questionType:
 *                 type: string
 *                 description: Type of question (optional)
 *               isFollowUpSkill:
 *                 type: boolean
 *                 description: Whether this is a follow-up question (optional)
 *                 example: false
 *     responses:
 *       200:
 *         description: Interview skill question added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interview_question_added"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       example: 15798
 *                     jobSkillId:
 *                       type: integer
 *                       nullable: true
 *                       example: null
 *                     interviewId:
 *                       type: integer
 *                       example: 428
 *                     jobApplicationId:
 *                       type: integer
 *                       example: 815
 *                     question:
 *                       type: string
 *                       example: "Tell us about a situation where you guided a team through a challenge."
 *                     answer:
 *                       type: string
 *                       example: ""
 *                     questionType:
 *                       type: string
 *                       example: "career_based"
 *                     interviewerId:
 *                       type: integer
 *                       example: 844
 *                     createdTs:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-17T10:46:42.653Z"
 *                     updatedTs:
 *                       type: string
 *                       format: date-time
 *                       example: "2025-09-17T10:46:42.653Z"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       403:
 *         description: Forbidden - user doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "forbidden_access"
 *                 code:
 *                   type: integer
 *                   example: 403
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.ADD_INTERVIEW_SKILL_QUESTION,
  auth,
  authorizedForManagePreInterviewQuestions,
  schemaValidation(addInterviewSkillQuestionValidation),
  HandleErrors(addInterviewSkillQuestion)
);

/**
 * @swagger
 * /interview/get-upcoming-or-past-interviews:
 *   get:
 *     summary: Get upcoming or past interviews
 *     description: Retrieves a list of upcoming or past interviews based on query parameters
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: isPast
 *         schema:
 *           type: boolean
 *         description: Set to true to get past interviews, false for upcoming interviews
 *       - in: query
 *         name: searchStr
 *         schema:
 *           type: string
 *         description: Search string to filter results
 *     responses:
 *       200:
 *         description: Interviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Fetched upcoming interviews successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       interviewId:
 *                         type: integer
 *                         example: 295
 *                       jobApplicationId:
 *                         type: integer
 *                         example: 663
 *                       startTime:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-17T20:30:00.000Z"
 *                       endTime:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-17T22:00:00.000Z"
 *                       isEnded:
 *                         type: integer
 *                         example: 0
 *                       jobId:
 *                         type: integer
 *                         example: 149
 *                       roundType:
 *                         type: string
 *                         example: "Video Call"
 *                       interviewerId:
 *                         type: integer
 *                         example: 844
 *                       channelName:
 *                         type: string
 *                         example: "interview_test45_738151"
 *                       resumeFile:
 *                         type: string
 *                         example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/149/resumes/Test-resume-1756463417838.pdf"
 *                       candidateName:
 *                         type: string
 *                         example: "Test"
 *                       candidateId:
 *                         type: integer
 *                         example: 536
 *                       candidateImageUrl:
 *                         type: string
 *                         example: "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-5.png"
 *                       jobTitle:
 *                         type: string
 *                         example: "Customer Support / Service Executive"
 *                       feedbackCount:
 *                         type: string
 *                         example: "0"
 *                       interviewerName:
 *                         type: string
 *                         example: "John1g Doe844"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_UPCOMING_OR_PAST_INTERVIEWS,
  auth,
  HandleErrors(getUpcomingOrPastInterviews)
);

/**
 * @swagger
 * /interview/get-job-list:
 *   get:
 *     summary: Get job list
 *     description: Retrieves a list of jobs for interview scheduling
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: searchString
 *         schema:
 *           type: string
 *         description: Optional search string to filter jobs
 *     responses:
 *       200:
 *         description: Job list retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: integer
 *                         example: 12
 *                       label:
 *                         type: string
 *                         example: "Senior Frontend Developer(ORG-SE-0001)"
 *                       jobId:
 *                         type: string
 *                         example: "ORG-SE-0001"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_parameters"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_JOB_LIST,
  auth,
  queryValidation(getJobListValidation),
  HandleErrors(getJobList)
);

/**
 * @swagger
 * /interview/get-candidate-list:
 *   get:
 *     summary: Get candidate list
 *     description: Retrieves a list of candidates for a specific job
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: jobId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the job to get candidates for
 *       - in: query
 *         name: searchString
 *         schema:
 *           type: string
 *         description: Optional search string to filter candidates
 *     responses:
 *       200:
 *         description: Candidate list retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       value:
 *                         type: integer
 *                         example: 23
 *                       label:
 *                         type: string
 *                         example: "John Doe"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_parameters"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_CANDIDATE_LIST,
  auth,
  queryValidation(getCandidateListValidation),
  HandleErrors(getCandidateList)
);

/**
 * @swagger
 * /interview/update-interview-answers:
 *   post:
 *     summary: Update interview answers
 *     description: Updates answers to interview questions
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *               - skillMarked
 *               - skillType
 *               - answers
 *             properties:
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview
 *                 example: 336
 *               skillMarked:
 *                 type: integer
 *                 description: Skill rating marked by interviewer
 *                 example: 10
 *               skillType:
 *                 type: string
 *                 description: Type of skill being assessed
 *                 example: "career_based"
 *               jobSkillId:
 *                 type: integer
 *                 description: ID of the job skill (optional)
 *               skillId:
 *                 type: integer
 *                 description: ID of the skill (optional)
 *               answers:
 *                 type: array
 *                 description: Array of question answers
 *                 items:
 *                   type: object
 *                   properties:
 *                     questionId:
 *                       type: integer
 *                       description: ID of the question
 *                       example: 11951
 *                     answer:
 *                       type: string
 *                       description: Answer to the question
 *                       example: "yes"
 *                 example: [
 *                   {
 *                     "questionId": 11951,
 *                     "answer": "yes"
 *                   },
 *                   {
 *                     "questionId": 14485,
 *                     "answer": "hhyrtywrty"
 *                   }
 *                 ]
 *     responses:
 *       200:
 *         description: Interview answers updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_ANSWERS,
  auth,
  schemaValidation(updateInterviewAnswersValidation),
  HandleErrors(updateInterviewAnswers)
);

/**
 * @swagger
 * /interview/end-interview:
 *   post:
 *     summary: End interview
 *     description: Ends an interview and locks all evaluations
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *             properties:
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview to end
 *               behaviouralNotes:
 *                 type: string
 *                 description: Notes about candidate's behavioral aspects
 *     responses:
 *       200:
 *         description: Interview ended successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "interview_ended"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.END_INTERVIEW,
  auth,
  schemaValidation(endInterviewValidation),
  HandleErrors(endInterview)
);

/**
 * @swagger
 * /interview/conduct-interview-static-information:
 *   get:
 *     summary: Get static information for conducting interviews
 *     description: Retrieves static information needed for conducting interviews
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Static information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     oneToOneInterviewInstructions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           title:
 *                             type: string
 *                             example: "Arrive on Time with ID"
 *                           content:
 *                             type: string
 *                             example: "Arrive at the interview location on time with a government-issued ID."
 *                     videoCallInterviewInstructions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           title:
 *                             type: string
 *                             example: "Join the Interview on Time"
 *                           content:
 *                             type: string
 *                             example: "Ensure you join the interview on time using the link provided."
 *                     stratumDescription:
 *                       type: object
 *                       additionalProperties:
 *                         type: string
 *                       example: {
 *                         "1": "I am not aware of this skill or how it affects my performance.",
 *                         "2": "I am aware of the skill but unconcerned about its impact on my performance.",
 *                         "10": "Excessive use of a skill that disrupts performance, relationships, or clarity, becoming counterproductive over time."
 *                       }
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.CONDUCT_INTERVIEW_STATIC_INFORMATION,
  auth,
  HandleErrors(conductInterviewStaticInformation)
);

/**
 * @swagger
 * /interview/get-interview-feedback/{interviewId}:
 *   get:
 *     summary: Get interview feedback
 *     description: Retrieves feedback for a specific interview
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: interviewId
 *         required: true
 *         schema:
 *           type: integer
 *         description: ID of the interview
 *     responses:
 *       200:
 *         description: Interview feedback retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     cultureSpecificSkills:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *                     roleSpecificSkills:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *                     careerBasedSkills:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 1714
 *                         score:
 *                           type: integer
 *                           example: 6
 *                         highlights:
 *                           type: object
 *                           properties:
 *                             aiFeedback:
 *                               type: array
 *                               items:
 *                                 type: string
 *                               example: [
 *                                 "Good technical foundation",
 *                                 "Needs practical experience",
 *                                 "Shows curiosity"
 *                               ]
 *                             overAllFeedback:
 *                               type: array
 *                               items:
 *                                 type: string
 *                               example: [
 *                                 "Good technical foundation",
 *                                 "Needs practical experience",
 *                                 "Shows curiosity"
 *                               ]
 *                     applicantAiBehavioralAnalysis:
 *                       type: string
 *                       example: "Build React apps with APIs, shadow senior devs, get regular feedback"
 *                     applicantBehavioralNotes:
 *                       type: string
 *                       example: ""
 *                     roundNumber:
 *                       type: integer
 *                       example: 1
 *                     candidateName:
 *                       type: string
 *                       example: "Harshit"
 *                     jobTitle:
 *                       type: string
 *                       example: "React"
 *                     candidateEmail:
 *                       type: string
 *                       example: "<EMAIL>"
 *                     isFeedbackFilled:
 *                       type: integer
 *                       example: 0
 *                     aiDecisionForNextRound:
 *                       type: string
 *                       example: "Rejected"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       400:
 *         description: Invalid interview ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_interview_id"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Interview not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "interview_not_found"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_INTERVIEW_FEEDBACK,
  auth,
  paramsValidation(getInterviewFeedbackValidation),
  HandleErrors(getInterviewFeedback)
);

/**
 * @swagger
 * /interview/update-interview-feedback:
 *   post:
 *     summary: Update interview feedback
 *     description: Updates feedback for a specific interview
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - interviewId
 *             properties:
 *               interviewId:
 *                 type: integer
 *                 description: ID of the interview
 *               feedback:
 *                 type: string
 *                 description: Feedback text for the interview
 *               isAdvanced:
 *                 type: boolean
 *                 description: Flag indicating if this is an advanced feedback
 *               behavioralNotes:
 *                 type: string
 *                 description: Notes about candidate's behavioral aspects
 *     responses:
 *       200:
 *         description: Interview feedback updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 success:
 *                   type: boolean
 *                   example: true
 *       400:
 *         description: Invalid request body
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_request_body"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 400
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       404:
 *         description: Interview not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "interview_not_found"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 404
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.post(
  ROUTES.INTERVIEW.UPDATE_INTERVIEW_FEEDBACK,
  auth,
  schemaValidation(updateInterviewFeedbackValidation),
  HandleErrors(updateInterviewFeedback)
);

/**
 * @swagger
 * /interview/get-pending-interviews:
 *   get:
 *     summary: Get pending interviews
 *     description: Retrieves a list of pending interviews that need feedback
 *     tags: [Interviews]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Pending interviews retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       interviewId:
 *                         type: integer
 *                         example: 414
 *                       candidateName:
 *                         type: string
 *                         example: "Harshit"
 *                       jobTitle:
 *                         type: string
 *                         example: "React"
 *                       roundNumber:
 *                         type: integer
 *                         example: 1
 *                       date:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-12T04:47:56.000Z"
 *                       startTime:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-10-08T08:30:00.000Z"
 *                       endTime:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-10-08T09:30:00.000Z"
 *                       interviewerId:
 *                         type: integer
 *                         example: 844
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized_access"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "server_error"
 *                 data:
 *                   type: null
 *                   example: null
 *                 code:
 *                   type: integer
 *                   example: 500
 */
interviewRoutes.get(
  ROUTES.INTERVIEW.GET_PENDING_INTERVIEWS,
  auth,
  HandleErrors(getPendingInterviews)
);

export default interviewRoutes;
