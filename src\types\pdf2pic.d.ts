declare module 'pdf2pic' {
  interface ConvertOptions {
    density?: number;
    saveFilename?: string;
    savePath?: string;
    format?: 'png' | 'jpg' | 'jpeg';
    width?: number;
    height?: number;
    quality?: number;
  }

  interface ConvertResult {
    name: string;
    size: number;
    path: string;
    page: number;
  }

  interface Converter {
    convert(pageNumber: number): Promise<ConvertResult>;
    bulk(pageNumbers: number | number[]): Promise<ConvertResult[]>;
  }

  function fromPath(pdfPath: string, options?: ConvertOptions): Converter;
  function fromBuffer(buffer: Buffer, options?: ConvertOptions): Converter;

  export { fromPath, fromBuffer, ConvertOptions, ConvertResult, Converter };
}
