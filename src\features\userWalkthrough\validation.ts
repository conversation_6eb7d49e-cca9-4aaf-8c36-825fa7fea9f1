import <PERSON><PERSON> from "joi";
import { WalkthroughName } from "../../schema/s9-innerview/walkthrough_status";

const joiObject = Joi.object().options({ abortEarly: false });

/**
 * Validation schema for updating walkthrough status
 * Validates that the request body contains a valid walkthrough name
 */
const updateWalkthroughStatusValidation = joiObject.keys({
  name: Joi.string()
    .valid(...Object.values(WalkthroughName))
    .required(),
});

/**
 * No validation schema needed for getWalkthroughStatus as it only uses
 * the userId from the request which is added via middleware
 */

export default {
  updateWalkthroughStatusValidation,
};
