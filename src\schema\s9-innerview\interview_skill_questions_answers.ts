/* eslint-disable no-unused-vars */
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
} from "typeorm";
import JobSkillsModel from "./job_skills";

export enum SourceType {
  AI = "AI",
  MANUAL = "MANUAL",
}

@Entity("interview_skill_questions_answers")
class InterviewSkillQuestionsAnswersModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "job_skill_id", nullable: true })
  jobSkillId: number;

  @Column({ name: "interview_id", nullable: true })
  interviewId: number;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @Column({ name: "question", type: "varchar", length: 500 })
  question: string;

  @Column({ name: "answer", type: "varchar", length: 2000 })
  answer: string;

  @Column({
    name: "source",
    type: "enum",
    enum: SourceType,
  })
  source: SourceType;

  @ManyToOne(() => JobSkillsModel)
  @JoinColumn({ name: "job_skill_id" })
  jobSkill: JobSkillsModel;

  @CreateDateColumn({
    type: "timestamp",
    name: "created_ts",
    nullable: false,
  })
  createdTs: Date;

  @UpdateDateColumn({
    type: "timestamp",
    name: "updated_ts",
    nullable: false,
  })
  updatedTs: Date;
}

export default InterviewSkillQuestionsAnswersModel;
