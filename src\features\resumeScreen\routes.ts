import express from "express";
import HandleErrors from "../../middleware/handleError";
import { sanitizeBody } from "../../middleware/sanitize";
import {
  queryValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import { ROUTES } from "../../utils/constants";
import {
  uploadManualCandidate,
  getAllPendingJobApplications,
  changeApplicationStatus,
} from "./controller";
import getPresignedUrl from "./presignedUrl.controller";
import {
  manualCandidateUploadSchema,
  getAllPendingJobApplicationsSchema,
  changeApplicationStatusParamsSchema,
} from "./validation";
import upload from "../../middleware/upload";
import auth from "../../middleware/auth";
import { authorizedForManualResumeScreening } from "../../middleware/isAuthorized";

const resumeScreenRoutes = express.Router();

/**
 * @swagger
 * tags:
 *   name: ResumeScreen
 *   description: Resume screening and candidate management
 */

/**
 * @swagger
 * /resume-screen/manual-candidate-upload:
 *   post:
 *     summary: Upload a candidate manually
 *     description: Creates a new candidate and job application
 *     tags: [ResumeScreen]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - job_id
 *               - candidates
 *             properties:
 *               job_id:
 *                 type: number
 *                 description: Job ID associated with the applications
 *                 example: 201
 *               candidates:
 *                 type: array
 *                 description: Array of candidate data
 *                 items:
 *                   type: object
 *                   required:
 *                     - name
 *                     - email
 *                     - gender
 *                     - resume_file
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: Name of the candidate
 *                       example: "Amet velit voluptat"
 *                     email:
 *                       type: string
 *                       description: Email of the candidate
 *                       example: "<EMAIL>"
 *                     gender:
 *                       type: string
 *                       enum: [Male, Female]
 *                       description: Gender of the candidate
 *                       example: "Male"
 *                     additional_details:
 *                       type: string
 *                       description: Additional details about the candidate
 *                       example: "Et aut quia eu in re"
 *                     resume_file:
 *                       type: string
 *                       description: S3 URL link to the resume file (required)
 *                       example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/resumes/Amet-resume-1757573269542.pdf"
 *                     assessment_file:
 *                       type: string
 *                       description: S3 URL link to the assessment file (optional)
 *                       example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/assessments/Amet-assessment-1757573269542.pdf"
 *     responses:
 *       200:
 *         description: Candidate uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Candidates uploaded successfully"
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       success:
 *                         type: boolean
 *                         example: true
 *                       candidateData:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "Amet velit voluptat"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           gender:
 *                             type: string
 *                             example: "Male"
 *                           additional_details:
 *                             type: string
 *                             example: "Et aut quia eu in re"
 *                           resume_file:
 *                             type: string
 *                             example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/resumes/Amet-resume-1757573269542.pdf"
 *                           assessment_file:
 *                             type: string
 *                             example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/assessments/Amet-assessment-1757573269542.pdf"
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to process any candidates"
 *                 results:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       success:
 *                         type: boolean
 *                         example: false
 *                       candidateData:
 *                         type: object
 *                         properties:
 *                           name:
 *                             type: string
 *                             example: "Amet velit voluptat"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           gender:
 *                             type: string
 *                             example: "Male"
 *                           additional_details:
 *                             type: string
 *                             example: "Et aut quia eu in re"
 *                           resume_file:
 *                             type: string
 *                             example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/resumes/Amet-resume-1757573269542.pdf"
 *                           assessment_file:
 *                             type: string
 *                             example: "https://dxxd0n8h8rh9s.cloudfront.net/manual-uploads/201/assessments/Amet-assessment-1757573269542.pdf"
 *                       error:
 *                         type: string
 *                         example: "Candidate already exists for this job"
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error occurred"
 *                 code:
 *                   type: number
 *                   example: 500
 */
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.MANUAL_CANDIDATE_UPLOAD,
  auth,
  authorizedForManualResumeScreening,
  sanitizeBody(),
  schemaValidation(manualCandidateUploadSchema),
  HandleErrors(uploadManualCandidate)
);

/**
 * @swagger
 * /resume-screen/get-all-pending-job-applications:
 *   get:
 *     summary: Get all pending job applications
 *     description: Retrieves all pending job applications with optional filtering by job_id, hiring_manager_id, status, and pagination support
 *     tags: [ResumeScreen]
 *     parameters:
 *       - in: query
 *         name: job_id
 *         schema:
 *           type: integer
 *         description: Optional job ID to filter applications
 *         example: 197
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [Pending, Approved, Rejected, On-Hold]
 *         description: Optional status to filter applications (default is Pending)
 *         example: Pending
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: Optional number of records to return per page (default is 10)
 *         example: 15
 *       - in: query
 *         name: offset
 *         schema:
 *           type: integer
 *           minimum: 0
 *         description: Optional number of records to skip for pagination (default is 0)
 *         example: 0
 *     responses:
 *       200:
 *         description: List of pending job applications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "pending_applications_retrieved"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 352
 *                       job_id:
 *                         type: integer
 *                         example: 197
 *                       candidate_id:
 *                         type: integer
 *                         example: 468
 *                       status:
 *                         type: string
 *                         example: "Pending"
 *                       candidate_name:
 *                         type: string
 *                         example: "John Doe"
 *                       candidate_email:
 *                         type: string
 *                         example: "<EMAIL>"
 *                       resume_url:
 *                         type: string
 *                         example: "https://example.com/resumes/john-doe.pdf"
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-15T10:30:00.000Z"
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "job_not_found"
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error occurred"
 *                 code:
 *                   type: number
 *                   example: 500
 */
resumeScreenRoutes.get(
  ROUTES.RESUME_SCREEN.GET_ALL_PENDING_JOB_APPLICATIONS,
  auth,
  authorizedForManualResumeScreening,
  queryValidation(getAllPendingJobApplicationsSchema),
  HandleErrors(getAllPendingJobApplications)
);

/**
 * @swagger
 * /resume-screen/change-application-status:
 *   post:
 *     summary: Change the status of a job application
 *     description: Updates the status of a job application and creates a status history record
 *     tags: [ResumeScreen]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - job_id
 *               - candidate_id
 *               - hiring_manager_id
 *               - status
 *               - hiring_manager_reason
 *             properties:
 *               job_id:
 *                 type: integer
 *                 description: Job ID associated with the application
 *                 example: 197
 *               candidate_id:
 *                 type: integer
 *                 description: Candidate ID associated with the application
 *                 example: 658
 *               hiring_manager_id:
 *                 type: integer
 *                 description: ID of the hiring manager changing the status
 *                 example: 909
 *               status:
 *                 type: string
 *                 enum: [Approved, Rejected, On-Hold]
 *                 description: New status for the application
 *                 example: "Approved"
 *               hiring_manager_reason:
 *                 type: string
 *                 description: Reason for the status change
 *                 example: "fwfwfw"
 *     responses:
 *       200:
 *         description: Application status updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     id:
 *                       type: integer
 *                       description: ID of the updated application
 *                       example: 818
 *                     status:
 *                       type: string
 *                       description: New status of the application
 *                       example: "Approved"
 *       400:
 *         description: Invalid input data or application not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid input data or application not found"
 *                 code:
 *                   type: number
 *                   example: 400
 *       500:
 *         description: Server error occurred
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Server error occurred"
 *                 code:
 *                   type: number
 *                   example: 500
 */
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.CHANGE_APPLICATION_STATUS,
  auth,
  authorizedForManualResumeScreening,
  sanitizeBody(),
  schemaValidation(changeApplicationStatusParamsSchema),
  HandleErrors(changeApplicationStatus)
);

// Swagger documentation not required for this route — it's not used in the frontend.
resumeScreenRoutes.post(
  ROUTES.RESUME_SCREEN.GET_PRESIGNED_URL,
  auth,
  authorizedForManualResumeScreening,
  upload.single("file"), // Process multipart/form-data with 'file' field
  HandleErrors(getPresignedUrl)
);

export default resumeScreenRoutes;
