import * as Sentry from "@sentry/node";
import dbConnection from "../../db/dbConnection";
import WalkthroughStatusModel from "../../schema/s9-innerview/walkthrough_status";
import { API_RESPONSE_MSG } from "../../utils/constants";
import { IUpdateWalkthroughStatus } from "./interface";

class UserWalkthroughServices {
  static getWalkthroughStatus = async (userId: number) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const walkthroughRepo = dataSource.getRepository(WalkthroughStatusModel);

      const walkthroughStatuses = await walkthroughRepo.find({
        where: {
          userId,
        },
        select: ["name"],
      });
      console.log("walkthroughStatuses ===>>> ", walkthroughStatuses);
      console.log("userId ===>>> ", userId);

      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        data: walkthroughStatuses.map(
          (walkthroughStatus) => walkthroughStatus.name
        ),
      };
    } catch (error) {
      console.log("error ===>>> ", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };

  static updateWalkthroughStatus = async (
    userId: number,
    data: IUpdateWalkthroughStatus
  ) => {
    try {
      const dataSource = await dbConnection.getS9InnerviewDataSource();
      const walkthroughRepo = dataSource.getRepository(WalkthroughStatusModel);

      // Check if a record already exists for this user and walkthrough name
      const existingStatus = await walkthroughRepo.findOne({
        where: {
          userId,
          name: data.name,
        },
      });

      console.log("existingStatus ===>>> ", existingStatus);
      if (existingStatus) {
        // Record already exists, no need to update anything substantial
        await walkthroughRepo
          .createQueryBuilder()
          .update(WalkthroughStatusModel)
          .set({ updatedTs: new Date() }) // Just update timestamp
          .where("userId = :userId", { userId })
          .andWhere("name = :name", { name: data.name })
          .execute();
        console.log("existingStatus ===>>> ", existingStatus);

        // Return success with indication that data was already present
        return {
          success: true,
          message: "Data already exists",
          isExisting: true,
        };
      }

      // Create new record
      const walkthroughStatus = new WalkthroughStatusModel();
      walkthroughStatus.userId = userId;
      walkthroughStatus.name = data.name;
      await walkthroughRepo.save(walkthroughStatus);
      console.log("walkthroughStatus ===>>> ", walkthroughStatus);

      // Return success with indication that new data was created
      return {
        success: true,
        message: API_RESPONSE_MSG.success,
        isExisting: false,
      };
    } catch (error) {
      console.error("updateWalkthroughStatus error ===>>>", error);
      Sentry.captureException(error);
      return {
        success: false,
        message: API_RESPONSE_MSG.failed,
      };
    }
  };
}

export default UserWalkthroughServices;
