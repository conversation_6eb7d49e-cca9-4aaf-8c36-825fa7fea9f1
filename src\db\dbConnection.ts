import { DataSource, EntityTarget } from "typeorm";
import {
  s9DbConnectionDetails,
  s9InnerviewDbConnectionDetails,
} from "../config/databaseConfig";
import { handleSentryError } from "../utils/helper";

/**
 * Database connection class to manage multiple database connections
 */
class DbConnection {
  s9ConnectionInfo: Promise<DataSource> | undefined;

  s9InnerviewConnectionInfo: Promise<DataSource> | undefined;

  /**
   * Get Stratum9 original database connection (stratum9_mobile_app).
   */
  async getS9DataSource(): Promise<DataSource> {
    return new Promise((resolve) => {
      if (!this.s9ConnectionInfo) {
        s9DbConnectionDetails()
          .then(
            (connection: Promise<DataSource>) => {
              this.s9ConnectionInfo = connection;
              resolve(this.s9ConnectionInfo);
            },
            (onrejected) => {
              // eslint-disable-next-line no-console
              console.log("Error in S9 db connection onrejected", onrejected);
            }
          )
          .catch((e) => {
            handleSentryError(e, "getS9DataSource");
            // eslint-disable-next-line no-console
            console.log(
              "s9DbConnectionDetails().then Error in S9 db connection",
              e
            );
          });
      } else {
        resolve(this.s9ConnectionInfo);
      }
    });
  }

  /**
   * Get Stratum9 Innerview database connection (stratum9_innerview_dev).
   */
  async getS9InnerviewDataSource(): Promise<DataSource> {
    return new Promise((resolve) => {
      if (!this.s9InnerviewConnectionInfo) {
        s9InnerviewDbConnectionDetails()
          .then(
            (connection: Promise<DataSource>) => {
              this.s9InnerviewConnectionInfo = connection;
              resolve(this.s9InnerviewConnectionInfo);
            },
            (onrejected) => {
              // eslint-disable-next-line no-console
              console.log(
                "Error in S9 Innerview db connection onrejected",
                onrejected
              );
            }
          )
          .catch((e) => {
            handleSentryError(e, "getS9InnerviewDataSource");
            // eslint-disable-next-line no-console
            console.log(
              "s9InnerviewDbConnectionDetails().then Error in S9 Innerview db connection",
              e
            );
          });
      } else {
        resolve(this.s9InnerviewConnectionInfo);
      }
    });
  }

  /**
   * Get repository from S9 original database (stratum9_mobile_app)
   * @param model Entity model
   * @returns Repository for the model
   */
  public async getS9DatabaseRepository<T>(model: EntityTarget<T>) {
    const dbConnection = await this.getS9DataSource();
    const repository = dbConnection.getRepository(model);
    return repository;
  }

  /**
   * Get repository from S9 Innerview database (stratum9_innerview_dev)
   * @param model Entity model
   * @returns Repository for the model
   */
  public async getS9InnerViewDatabaseRepository<T>(model: EntityTarget<T>) {
    const dbConnection = await this.getS9InnerviewDataSource();
    const repository = dbConnection.getRepository(model);
    return repository;
  }
}

export default new DbConnection();
export { DbConnection };
export { s9DbConnectionDetails, s9InnerviewDbConnectionDetails };
