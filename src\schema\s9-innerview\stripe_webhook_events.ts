import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
} from "typeorm";

@Entity("stripe_webhook_events")
class StripeWebhookModel {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "event_type", type: "varchar", length: 50, nullable: false })
  eventType: string;

  @Column({ name: "event_data", type: "json", nullable: false })
  eventData: object;

  @Column({
    name: "stripe_payment_id",
    type: "varchar",
    length: 50,
    nullable: false,
  })
  stripePaymentId: string;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @CreateDateColumn({
    name: "received_at",
    type: "timestamp",
  })
  receivedAt: Date;
}

export default StripeWebhookModel;
