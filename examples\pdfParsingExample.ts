/**
 * Example usage of the Advanced PDF Parser
 * 
 * This example demonstrates how to use the new PDF parsing capabilities
 * for handling template resumes and extracting images from PDFs.
 */

import * as fs from 'fs';
import * as path from 'path';
import { advancedPdfParser, parsePdfWithAdvancedRetries } from '../src/utils/advancedPdfParser';
import { parsePdfAdvanced } from '../src/utils/helper';

async function demonstrateBasicUsage() {
  console.log('=== Basic PDF Parsing (Backward Compatible) ===');
  
  try {
    // This is how you would use it in existing code - no changes needed!
    const pdfPath = path.join(__dirname, 'sample-resume.pdf');
    
    if (fs.existsSync(pdfPath)) {
      const buffer = fs.readFileSync(pdfPath);
      
      // Existing function now uses advanced parsing automatically
      const result = await parsePdfWithAdvancedRetries(buffer);
      
      if (result) {
        console.log('✅ PDF parsed successfully');
        console.log(`📄 Text length: ${result.text.length} characters`);
        console.log(`📝 First 200 characters: ${result.text.substring(0, 200)}...`);
      } else {
        console.log('❌ Failed to parse PDF');
      }
    } else {
      console.log('📁 Sample PDF not found, skipping basic demo');
    }
  } catch (error) {
    console.error('❌ Error in basic parsing:', error.message);
  }
}

async function demonstrateAdvancedUsage() {
  console.log('\n=== Advanced PDF Parsing with OCR ===');
  
  try {
    const pdfPath = path.join(__dirname, 'template-resume.pdf');
    
    if (fs.existsSync(pdfPath)) {
      const buffer = fs.readFileSync(pdfPath);
      
      // Advanced parsing with all features
      const result = await parsePdfAdvanced(buffer, {
        extractImages: true,
        useOcr: true,
        ocrLanguage: 'eng'
      });
      
      console.log('✅ Advanced PDF parsing completed');
      console.log(`📄 Extraction method: ${result.extractionMethod}`);
      console.log(`📝 Text length: ${result.text.length} characters`);
      console.log(`🖼️  Images found: ${result.images?.length || 0}`);
      console.log(`📊 OCR confidence: ${result.confidence || 'N/A'}%`);
      console.log(`📋 Pages: ${result.metadata?.pages || 'Unknown'}`);
      
      if (result.images && result.images.length > 0) {
        console.log('\n🖼️  Image Details:');
        result.images.forEach((image, index) => {
          console.log(`   Image ${index + 1}: ${(image.length / 1024).toFixed(2)} KB`);
        });
      }
      
    } else {
      console.log('📁 Template PDF not found, creating demo with mock data');
      await demonstrateWithMockData();
    }
  } catch (error) {
    console.error('❌ Error in advanced parsing:', error.message);
  }
}

async function demonstrateWithMockData() {
  console.log('\n=== Demo with Mock PDF Data ===');
  
  // Create a minimal PDF buffer for demonstration
  const mockPdfBuffer = Buffer.from(`%PDF-1.4
1 0 obj
<<
/Type /Catalog
/Pages 2 0 R
>>
endobj
2 0 obj
<<
/Type /Pages
/Kids [3 0 R]
/Count 1
>>
endobj
3 0 obj
<<
/Type /Page
/Parent 2 0 R
/MediaBox [0 0 612 792]
/Contents 4 0 R
>>
endobj
4 0 obj
<<
/Length 44
>>
stream
BT
/F1 12 Tf
100 700 Td
(John Doe Resume) Tj
ET
endstream
endobj
xref
0 5
0000000000 65535 f 
0000000009 00000 n 
0000000058 00000 n 
0000000115 00000 n 
0000000204 00000 n 
trailer
<<
/Size 5
/Root 1 0 R
>>
startxref
297
%%EOF`);

  try {
    const result = await advancedPdfParser.parsePdf(mockPdfBuffer, {
      extractImages: false,
      useOcr: true,
      maxRetries: 1
    });
    
    console.log('✅ Mock PDF processed');
    console.log(`📄 Extraction method: ${result.extractionMethod}`);
    console.log(`📝 Text extracted: "${result.text.trim()}"`);
    
  } catch (error) {
    console.log('ℹ️  Mock PDF parsing failed (expected for minimal PDF):', error.message);
  }
}

async function demonstrateImageExtraction() {
  console.log('\n=== Image Extraction Demo ===');
  
  try {
    const pdfPath = path.join(__dirname, 'resume-with-images.pdf');
    
    if (fs.existsSync(pdfPath)) {
      const buffer = fs.readFileSync(pdfPath);
      
      // Extract only images
      const images = await advancedPdfParser.extractImages(buffer);
      
      console.log(`🖼️  Extracted ${images.length} images`);
      
      // Save images to files
      images.forEach((imageBuffer, index) => {
        const imagePath = path.join(__dirname, `extracted-image-${index + 1}.png`);
        fs.writeFileSync(imagePath, imageBuffer);
        console.log(`💾 Saved image ${index + 1} to: ${imagePath}`);
      });
      
    } else {
      console.log('📁 PDF with images not found, skipping image extraction demo');
    }
  } catch (error) {
    console.error('❌ Error in image extraction:', error.message);
  }
}

async function demonstrateMetadataExtraction() {
  console.log('\n=== Metadata Extraction Demo ===');
  
  try {
    const mockBuffer = Buffer.from('%PDF-1.4\n% Mock PDF for metadata demo\n%%EOF');
    
    const metadata = await advancedPdfParser.getMetadata(mockBuffer);
    
    if (metadata) {
      console.log('📋 PDF Metadata:');
      console.log(`   📄 Pages: ${metadata.pages || 'Unknown'}`);
      console.log(`   📝 Title: ${metadata.title || 'Not specified'}`);
      console.log(`   👤 Author: ${metadata.author || 'Not specified'}`);
      console.log(`   🛠️  Creator: ${metadata.creator || 'Not specified'}`);
      console.log(`   📅 Creation Date: ${metadata.creationDate || 'Not specified'}`);
    } else {
      console.log('ℹ️  No metadata available');
    }
  } catch (error) {
    console.log('ℹ️  Metadata extraction failed (expected for mock PDF):', error.message);
  }
}

async function demonstrateErrorHandling() {
  console.log('\n=== Error Handling Demo ===');
  
  // Test with invalid PDF
  const invalidBuffer = Buffer.from('This is not a PDF file!');
  
  try {
    const result = await parsePdfWithAdvancedRetries(invalidBuffer, 1);
    console.log('📄 Invalid PDF result:', result);
  } catch (error) {
    console.log('✅ Error handled gracefully:', error.message);
  }
  
  // Test with empty buffer
  const emptyBuffer = Buffer.alloc(0);
  
  try {
    const result = await parsePdfWithAdvancedRetries(emptyBuffer, 1);
    console.log('📄 Empty PDF result:', result);
  } catch (error) {
    console.log('✅ Empty buffer handled gracefully:', error.message);
  }
}

async function runAllDemos() {
  console.log('🚀 Advanced PDF Parser Demo\n');
  console.log('This demo shows the capabilities of the new PDF parsing system.');
  console.log('It can handle template resumes, extract images, and use OCR for scanned documents.\n');
  
  await demonstrateBasicUsage();
  await demonstrateAdvancedUsage();
  await demonstrateImageExtraction();
  await demonstrateMetadataExtraction();
  await demonstrateErrorHandling();
  
  console.log('\n✨ Demo completed!');
  console.log('\n📚 For more information, see: docs/ADVANCED_PDF_PARSING.md');
}

// Run the demo if this file is executed directly
if (require.main === module) {
  runAllDemos().catch(console.error);
}

export {
  demonstrateBasicUsage,
  demonstrateAdvancedUsage,
  demonstrateImageExtraction,
  demonstrateMetadataExtraction,
  demonstrateErrorHandling,
  runAllDemos
};
