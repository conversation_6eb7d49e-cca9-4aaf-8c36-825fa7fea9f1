import express from "express";
import auth from "../../middleware/auth";
import {
  getNotificationsController,
  markAsWatchedController,
  deleteUsersAllNotificationsController,
  getUnreadNotificationsCountController,
} from "./controllers";
import HandleErrors from "../../middleware/handleError";
import { ROUTES } from "../../utils/constants";

const notificationRoutes = express.Router();

/**
 * @swagger
 * /notifications/get-notifications:
 *   get:
 *     tags: [Notifications]
 *     summary: Get notifications with optional filters
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: ["Interview Scheduled", "Interview Feedback Pending", "Job Post Archived", "Candidate Promoted", "Candidate Hired/Unhired", "Final Assessment Submitted", "Subscription Updated"]
 *       - in: query
 *         name: isWatched
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: offset
 *         schema:
 *           type: number
 *           default: 0
 *       - in: query
 *         name: limit
 *         schema:
 *           type: number
 *           default: 10
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: List of notifications retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       id:
 *                         type: integer
 *                         example: 4444
 *                       type:
 *                         type: string
 *                         example: "Job Post Archived"
 *                         enum: ["Interview Scheduled", "Interview Feedback Pending", "Job Post Archived", "Candidate Promoted", "Candidate Hired/Unhired", "Final Assessment Submitted", "Subscription Updated", "Interview Advanced"]
 *                       title:
 *                         type: string
 *                         example: "Job Post Archived"
 *                       description:
 *                         type: string
 *                         example: "Job post Senior Java Developer has been archived. Can be restored"
 *                       relatedId:
 *                         type: integer
 *                         example: 207
 *                       additionalInfo:
 *                         type: object
 *                         nullable: true
 *                         example: null
 *                       isWatched:
 *                         type: integer
 *                         example: 0
 *                         description: "0 for unwatched, 1 for watched"
 *                       createdTs:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-17T13:16:36.421Z"
 *                       updatedTs:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-17T13:16:36.421Z"
 *                 code:
 *                   type: integer
 *                   example: 200
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *                 code:
 *                   type: integer
 *                   example: 401
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 *                 code:
 *                   type: integer
 *                   example: 500
 */
notificationRoutes.get(
  ROUTES.NOTIFICATIONS.GET_NOTIFICATIONS,
  auth,
  HandleErrors(getNotificationsController)
);

/**
 * @swagger
 * /notifications/mark-as-watched:
 *   post:
 *     tags: [Notifications]
 *     summary: Mark a notification as watched
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *             properties: {}
 *     responses:
 *       200:
 *         description: Notification marked as watched
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "notification_marked_as_watched"
 *                 data:
 *                   type: object
 *                   properties:
 *                     generatedMaps:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *                     raw:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *                     affected:
 *                       type: integer
 *                       example: 6
 *                       description: Number of notifications marked as watched
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *       404:
 *         description: Notification not found or already marked as watched
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Notification not found or already marked as watched"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 */
notificationRoutes.post(
  ROUTES.NOTIFICATIONS.MARK_AS_WATCHED,
  auth,
  HandleErrors(markAsWatchedController)
);

/**
 * @swagger
 * /notifications/delete-users-all-notifications:
 *   delete:
 *     tags: [Notifications]
 *     summary: Delete all notifications for the authenticated user
 *     description: Removes all notifications associated with the current user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: All notifications successfully deleted
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     raw:
 *                       type: array
 *                       items:
 *                         type: object
 *                       example: []
 *                     affected:
 *                       type: integer
 *                       description: Number of notifications deleted
 *                       example: 6
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "failed_to_delete_notifications"
 */
notificationRoutes.delete(
  ROUTES.NOTIFICATIONS.DELETE_USERS_ALL_NOTIFICATIONS,
  auth,
  HandleErrors(deleteUsersAllNotificationsController)
);

/**
 * @swagger
 * /notifications/get-unread-notifications-count:
 *   get:
 *     tags: [Notifications]
 *     summary: Get count of unread notifications
 *     description: Returns the total number of unread notifications for the authenticated user
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Successfully retrieved unread notification count
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "unread_count_fetched"
 *                 data:
 *                   type: object
 *                   properties:
 *                     count:
 *                       type: integer
 *                       description: Number of unread notifications
 *                       example: 3
 *       401:
 *         description: Unauthorized - User not authenticated
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "failed_to_fetch_unread_count"
 */
notificationRoutes.get(
  ROUTES.NOTIFICATIONS.GET_UNREAD_NOTIFICATIONS_COUNT,
  auth,
  HandleErrors(getUnreadNotificationsCountController)
);

export default notificationRoutes;
