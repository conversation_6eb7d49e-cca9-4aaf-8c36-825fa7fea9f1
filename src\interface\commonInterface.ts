import { Request } from "express";

export interface RequestWithUser extends Request {
  userId: number;
  roleId: number;
  orgId: number;
  departmentId: number;
  token: string;
}

export interface WeightedCriteria {
  score: number;
  weight_percentage: number;
  out_of: number;
  details: string;
}

export interface ATSAnalysisResult {
  total_ats_score: number;
  ai_decision: string;
  ai_reason: string;
  weighted_criteria: {
    skills_match: WeightedCriteria;
    experience_relevance: WeightedCriteria;
    education_qualifications: WeightedCriteria;
    keywords_match: WeightedCriteria;
    role_responsibilities: WeightedCriteria;
  };
}

export interface WeightedCriterion {
  score: number;
  weight_percentage: number;
  out_of: number;
  details: string;
}

export interface IJobApplication {
  id: number;
  hiringManagerId: number;
  jobId: number;
  candidateId: number;
  resumeFile: string;
  resumeText: string;
  assessmentFile: string;
  assessmentText: string;
  additionalDetails: string;
  source: string;
  atsScore: ATSAnalysisResult;
  aiDecision: string;
  aiReason: string;
  status: string;
  isTopApplication: boolean;
  applicationRankStatus: string;
  hiringManagerReason: string | null;
  isActive: boolean;
  createdTs: string; // or Date if parsed
  updatedTs: string; // or Date if parsed
}

export interface envData {
  countryCode: string;
  env: string;
  port: number;
  siteUrl: string;
  logGroupName: string;
  bucket: string;
  region: string;
  secretManagerKey: string;
  profile: string;
}

export interface ISecretKeys {
  pubnub_publishKey: string;
  pubnub_subscribeKey: string;
  pubnub_userId: string;
  deepgram_secret_key: string;
  token_key: string;
  otp_enc_key: string;
  db_hostname: string;
  db_username: string;
  db_password: string;
  db_database_name: string;
  s9iv_db_database_name: string;
  s9iv_db_hostname: string;
  s9iv_db_username: string;
  s9iv_db_password: string;
  reset_password_url: string;
  jwt_secret: string;
  twillo_sid: string;
  twillo_token: string;
  twillo_number: string;
  database_host: string;
  database_port: string;
  database_username: string;
  database_password: string;
  database_name: string;
  paypal_WebhookID: string;
  paypal_clientId: string;
  paypal_clientSecret: string;
  admin_url: string;
  seller_url: string;
  paypal_mode: string;
  fedex_secret: string;
  fedex_apikey: string;
  shipper_postalcode: string;
  fedex_account: string;
  sendgrid_key: string;
  sendgrid_mail: string;
  fedex_url: string;
  paypal_url: string;
  accelerate_url: string;
  ec2_generate_pdf_url: string;
  googleAutocompleteApiKey: string;
  authKey: string;
  dailywins_attempt_limit: number;
  redis_db_endpoint: string;
  s3_bucket_cloudfront_distribution_url: string;
  s3_bucket_cloudfront_distribution_id: string;
  s3_bucket_cloudfront_distribution_url_for_s9_innerview: string;
  s3_bucket_cloudfront_distribution_id_for_s9_innerview: string;
  s3_bucket_url_s9_innerview: string;
  s3_bucket_url: string;
  s9_hiring_database_name: string;
  agora_appCertificate: string;
  agora_appId: string;
  agora_meeting_link_encryption_key: string;
  agora_customerSecret: string;
  agora_customerId: string;
  openai_api_key: string;
  stripe_secret_key: string;
  stripe_webhook_secret: string;
  google_api_key: string;
  orgId_encryption_key: string;
  finalAssessmentSecrete: string;
}
export declare interface ResponseObject {
  success: boolean;
  message?: string;
  data?: any;
  token?: Object;
  error?: unknown;
  totalPages?: number;
}

export interface IEnvData {
  env: string;
  port: number;
  awsRegion: string;
  profile: string;
  s3BucketName: string;
  secretManagerKey: string;
  sandGridKey: string;
  sandGridEmail: string;
  reCaptchaSecretKey: string;
  invisibleReCaptchaSecretKey: string;
}

export interface ISendMailContent {
  subject: string;
  text: string;
  html: string;
}

interface IAttachments {
  content: string;
  filename: string;
  disposition: string;
}

export interface ISendMail {
  email: string;
  subject: string;
  textContent: string;
  htmlContent: string;
  attachments?: IAttachments[];
}

export interface IEmailData {
  from: string;
  to: string;
  subject: string;
  text: string;
  html: string;
  attachments?: IAttachments[];
}

export interface SkillSummary {
  finalSummary: string[];
}
