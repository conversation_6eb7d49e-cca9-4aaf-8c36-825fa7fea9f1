import Joi from "joi";
import { RecordingMode } from "./interface";
import { AGORA_USER_TYPE } from "../../utils/constants";

export const startRecordingSchema = Joi.object({
  interviewId: Joi.string().required(),
  channelName: Joi.string().required(),
  agoraInterviewerId: Joi.string().required(),
});

export const stopRecordingSchema = Joi.object({
  channelName: Joi.string().required(),
  mode: Joi.string()
    .valid(RecordingMode.MIX, RecordingMode.INDIVIDUAL)
    .required(),
  resourceId: Joi.string().required(),
  sid: Joi.string().required(),
  uid: Joi.number().required(),
});

export const createTokenSchema = Joi.object({
  interviewId: Joi.string().required(),
  personType: Joi.string()
    .valid(AGORA_USER_TYPE.Interviewer, AGORA_USER_TYPE.Candidate)
    .required(),
  channelName: Joi.string().required(),
  interviewerId: Joi.string().optional(),
  candidateId: Joi.string().optional(),
  candidateEmail: Joi.string().optional(),
});
