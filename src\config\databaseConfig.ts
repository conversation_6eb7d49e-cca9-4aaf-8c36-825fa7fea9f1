import { DataSource } from "typeorm";
import { SnakeNamingStrategy } from "typeorm-naming-strategies";

import { getSecretKeys } from "./awsConfig";

import { DB_CONST } from "../utils/constants";
import EmployeeModel from "../schema/s9-innerview/employees";
import RoleModel from "../schema/s9-innerview/roles";
import RolePermissionModel from "../schema/s9-innerview/role_permissions_mapping";
import DepartmentModel from "../schema/s9-innerview/departments";
import PermissionModel from "../schema/s9-innerview/permissions";
import UserModel from "../schema/s9/user";
import AddressModel from "../schema/s9/address";
import OrganizationModel from "../schema/s9/organization";
import AssessmentModel from "../schema/s9/assessments";
import { JobsModel } from "../schema/s9-innerview/jobs";
import SkillsModel from "../schema/s9-innerview/skills";
import JobSkillsModel from "../schema/s9-innerview/job_skills";
import CandidatesModel from "../schema/s9-innerview/candidates";
import JobApplicationsModel from "../schema/s9-innerview/job_applications";
import JobApplicationStatusHistoryModel from "../schema/s9-innerview/job_application_status_history";
import FinalAssessmentsModel from "../schema/s9-innerview/final_assessments";
import FinalAssessmentQuestionsModel from "../schema/s9-innerview/final_assessment_questions";
import ApplicantFinalSummariesModel from "../schema/s9-innerview/applicant_final_summaries";
import InterviewModel from "../schema/s9-innerview/interview";
import InterviewSkillEvaluationModel from "../schema/s9-innerview/interview_skill_evaluations";
import InterviewSkillQuestionsAnswersModel from "../schema/s9-innerview/interview_skill_questions_answers";
import SubscriptionPlanModel from "../schema/s9-innerview/subscription_plans";
import SubscriptionPricingModel from "../schema/s9-innerview/subscription_pricing";
import SubscriptionTransactionModel from "../schema/s9-innerview/subscription_transactions";
import StripeWebhookModel from "../schema/s9-innerview/stripe_webhook_events";
import OrganizationSubscriptionModel from "../schema/s9-innerview/organization_subscriptions";
import OrganizationSubscriptionBenefitModel from "../schema/s9-innerview/organization_subscription_benefits";
import NotificationsModal from "../schema/s9-innerview/notifications";
import ApplicantAdditionalInfoModel from "../schema/s9-innerview/applicant_additional_info";
import InterviewFeedbackModel from "../schema/s9-innerview/interview_feedback";
import ActivityLogs from "../schema/s9-innerview/activity_logs";
import ApplicationsJobSkillsModel from "../schema/s9-innerview/applications_job_skills";
import { handleSentryError } from "../utils/helper";
import WalkthroughStatusModel from "../schema/s9-innerview/walkthrough_status";
import OrganizationBranchesModel from "../schema/s9/organization_branches";
import MyCollectionModel from "../schema/s9/my_collection";

// Environment variables are loaded via Node.js process.env

/**
 * Main database connection details for stratum9 main
 * @returns DataSource promise
 */
export const s9DbConnectionDetails = async () => {
  const keys = await getSecretKeys();
  return new Promise((resolve) => {
    try {
      const dataSource = new DataSource({
        type: "mysql",
        host: keys.db_hostname,
        port: 3306,
        username: keys.db_username,
        password: keys.db_password,
        database: keys.db_database_name,
        connectTimeout: 30000,
        extra: {
          charset: DB_CONST.CHARSET,
        },
        timezone: "Z",
        name: DB_CONST.S9_NAME,
        entities: [
          UserModel,
          AddressModel,
          OrganizationModel,
          AssessmentModel,
          OrganizationBranchesModel,
          MyCollectionModel,
        ],
        namingStrategy: new SnakeNamingStrategy(),
        // migrations: ["src/migrations/**/*{.ts,.js}"],
        // subscribers: ["../src/subscriber/**/*{.ts,.js}"],
      });
      dataSource
        .initialize()
        .then(() => {
          resolve(dataSource);
        })
        .catch((err) => {
          handleSentryError(err, "s9DbConnectionDetails");
          resolve(undefined);
        });
    } catch (e) {
      handleSentryError(e, "s9DbConnectionDetails");
      resolve(undefined);
    }
  });
};
/**
 * Original database connection details for stratum9_innerview_dev
 * @returns DataSource promise
 */
export const s9InnerviewDbConnectionDetails = async () => {
  const keys = await getSecretKeys();
  return new Promise((resolve) => {
    try {
      const dataSource = new DataSource({
        type: "mysql",
        host: keys.db_hostname,
        port: 3306,
        username: keys.db_username,
        password: keys.db_password,
        database: keys.s9_hiring_database_name,
        synchronize: false,
        connectTimeout: 30000,
        logging: false,
        extra: {
          charset: DB_CONST.CHARSET,
        },
        timezone: "Z",
        name: DB_CONST.S9_INNERVIEW_NAME,
        entities: [
          EmployeeModel,
          RoleModel,
          RolePermissionModel,
          DepartmentModel,
          PermissionModel,
          JobsModel,
          SkillsModel,
          JobSkillsModel,
          CandidatesModel,
          JobApplicationsModel,
          JobApplicationStatusHistoryModel,
          FinalAssessmentsModel,
          FinalAssessmentQuestionsModel,
          ApplicantFinalSummariesModel,
          InterviewModel,
          InterviewSkillEvaluationModel,
          InterviewSkillQuestionsAnswersModel,
          SubscriptionPlanModel,
          SubscriptionPricingModel,
          SubscriptionTransactionModel,
          StripeWebhookModel,
          OrganizationSubscriptionModel,
          OrganizationSubscriptionBenefitModel,
          NotificationsModal,
          ApplicantAdditionalInfoModel,
          OrganizationSubscriptionBenefitModel,
          InterviewFeedbackModel,
          ActivityLogs,
          ApplicationsJobSkillsModel,
          WalkthroughStatusModel,
        ],
        namingStrategy: new SnakeNamingStrategy(),
        migrations: ["src/migrations/**/*{.ts,.js}"],
        subscribers: ["../src/subscriber/**/*{.ts,.js}"],
      });
      dataSource
        .initialize()
        .then(() => {
          resolve(dataSource);
        })
        .catch((err) => {
          handleSentryError(err, "s9InnerviewDbConnectionDetails");
          resolve(undefined);
        });
    } catch (e) {
      handleSentryError(e, "s9InnerviewDbConnectionDetails");
      resolve(undefined);
    }
  });
};
