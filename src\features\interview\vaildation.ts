import Joi from "joi";

const joiObject = Joi.object().options({ abortEarly: false });

export const getMyInterviewsValidation = joiObject.keys({
  monthYear: Joi.string().required(),
});

export const updateOrScheduleInterviewValidation = joiObject.keys({
  title: Joi.string().required(),
  jobId: Joi.number().optional(),
  interviewerId: Joi.number().required(),
  jobApplicationId: Joi.number().required(),
  scheduleAt: Joi.number().required(),
  startTime: Joi.number().required(),
  endTime: Joi.number().required(),
  roundType: Joi.string().required(),
  description: Joi.string().optional().allow(""),
  fileUrlArray: Joi.string().optional().allow(""),
  interviewId: Joi.number().optional(),
});

export const getJobListValidation = joiObject.keys({
  searchString: Joi.string().optional().allow(""),
});

export const getInterviewersValidation = joiObject
  .concat(getJobListValidation)
  .keys({
    jobId: Joi.number().required().allow(""),
  });

export const getCandidateListValidation = joiObject.concat(
  getInterviewersValidation
);

export const getInterviewSkillQuestionsValidation = joiObject.keys({
  jobApplicationId: Joi.number().required(),
  interviewId: Joi.number().optional(),
});

export const updateInterviewSkillQuestionValidation = joiObject.keys({
  interviewId: Joi.number().required(),
  question: Joi.string().required(),
  interviewQuestionId: Joi.number().required(),
});

export const addInterviewSkillQuestionValidation = joiObject.keys({
  jobApplicationId: Joi.number().required(),
  interviewId: Joi.number().required(),
  question: Joi.string().required(),
  skillType: Joi.string().required(),
  questionType: Joi.string().optional(),
  jobSkillId: Joi.number().optional(),
});

export const updateInterviewAnswersValidation = joiObject.keys({
  interviewId: Joi.number().optional(),
  skillMarked: Joi.number().optional(),
  skillType: Joi.string().required(),
  jobSkillId: Joi.number().optional(),
  skillId: Joi.number().optional(),
  answers: Joi.array()
    .items(
      Joi.object({
        questionId: Joi.number().required(),
        answer: Joi.string().optional().allow(""),
      })
    )
    .optional(),
});

export const endInterviewValidation = joiObject.keys({
  interviewId: Joi.number().required(),
  behaviouralNotes: Joi.string().optional().allow(""),
});

export const updateInterviewFeedbackValidation = joiObject.keys({
  interviewId: Joi.number().required(),
  feedback: Joi.string().required(),
  isAdvanced: Joi.boolean().required(),
  behavioralNotes: Joi.string().optional().allow(""),
  highlights: Joi.array()
    .items(Joi.string().optional().allow(""))
    .optional()
    .allow(""),
});

export const getInterviewFeedbackValidation = joiObject.keys({
  interviewId: Joi.string().required(),
});
