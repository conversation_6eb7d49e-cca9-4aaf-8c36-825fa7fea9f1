{"local": {"env": "local", "port": 3001, "server_name": "Local", "job_apply_website_url": "http://localhost:5173/jobs?orgId=:encryptedOrgId", "job_apply_script_api_url": "http://localhost:3001/api/v1/job-apply/load-iframe?orgId=:encryptedOrgId", "job_apply_iframe_api_url": "http://localhost:3001/api/v1/job-apply/get-iframe-content?orgId=:encryptedOrgId", "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "region": "us-east-1", "s9_innerview_website_url": "https://develop.drrjxkqeoo26r.amplifyapp.com", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-development-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-development-generateIntFinalSummary", "s3_bucket": "s9-interview-assets", "s3BucketName": "s9-interview-assets", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}, "s9_innerview_backend_url": "http://localhost:3001/api/v1"}, "development": {"env": "development", "port": 3001, "server_name": "Development", "job_apply_website_url": "http://stratum9-hiring-org-jobs-apply-module-dev.s3-website-us-east-1.amazonaws.com/jobs?orgId=:encryptedOrgId", "job_apply_script_api_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1/job-apply/load-iframe?orgId=:encryptedOrgId", "job_apply_iframe_api_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1/job-apply/get-iframe-content?orgId=:encryptedOrgId", "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "stratum9_mobile_app-secret-keys-stratum9", "s9_innerview_website_url": "https://develop.drrjxkqeoo26r.amplifyapp.com", "s3_bucket": "s9-interview-assets", "s3BucketName": "s9-interview-assets", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-development-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-development-generateIntFinalSummary", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}, "s9_innerview_backend_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1"}, "staging": {"env": "staging", "port": 8000, "server_name": "Staging", "job_apply_website_url": "http://stratum9-hiring-org-jobs-apply-module-dev.s3-website-us-east-1.amazonaws.com/jobs?orgId=:encryptedOrgId", "job_apply_script_api_url": "https://db37cbkkjfl0x.cloudfront.net/api/v1/job-apply/load-iframe?orgId=:encryptedOrgId", "job_apply_iframe_api_url": "https://db37cbkkjfl0x.cloudfront.net/api/v1/job-apply/get-iframe-content?orgId=:encryptedOrgId", "profile": "stratum9", "secretManagerKey": "staging-secret-keys-stratum9-innerview", "region": "us-east-1", "s9_innerview_website_url": "https://staging.d203wj3sxolpdu.amplifyapp.com", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-staging-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-staging-generateIntFinalSummary", "s3_bucket": "s9-interview-assets-staging", "s3BucketName": "s9-interview-assets-staging", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "stripe": {"success_url": "https://staging.d203wj3sxolpdu.amplifyapp.com/subscriptions/success", "cancel_url": "https://staging.d203wj3sxolpdu.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}, "s9_innerview_backend_url": "https://db37cbkkjfl0x.cloudfront.net/api/v1"}, "production": {"env": "production", "port": 3001, "server_name": "Production", "job_apply_website_url": "http://stratum9-hiring-org-jobs-apply-module-dev.s3-website-us-east-1.amazonaws.com/jobs?orgId=:encryptedOrgId", "job_apply_script_api_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1/job-apply/load-iframe?orgId=:encryptedOrgId", "job_apply_iframe_api_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1/job-apply/get-iframe-content?orgId=:encryptedOrgId", "region": "us-east-1", "profile": "stratum9", "secretManagerKey": "prod-secret-keys-stratum9", "s9_innerview_website_url": "https://develop.drrjxkqeoo26r.amplifyapp.com", "s3_bucket": "s9-interview-assets-prod", "s3BucketName": "s9-interview-assets-prod", "s3AccessKey": "********************", "s3SecretKey": "4O+kA6wzYIMYE8zAoOcFqhleRx6PV/rHX8iJU1tQ", "s3Region": 0, "s3FilenamePrefix": "agoraInterviewRecordings", "generate_interview_questions_lambda": "stratum-hiring-cron-jobs-production-generateInterviewQuestions", "generate_int_final_summary_lambda": "stratum-hiring-cron-jobs-production-generateIntFinalSummary", "stripe": {"success_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/success", "cancel_url": "https://develop.drrjxkqeoo26r.amplifyapp.com/subscriptions/cancel", "api_version": "2025-06-30.basil"}, "s9_innerview_backend_url": "https://d2euqjswxwm2yy.cloudfront.net/api/v1"}}