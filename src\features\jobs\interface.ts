import {
  EmploymentType,
  HiringType,
  LocationType,
  SalaryCycle,
  ToneStyle,
} from "../../schema/s9-innerview/jobs";

/**
 * Interface for skills with name and description
 */
export interface ISkill {
  id?: string;
  name: string;
  description: string;
}

/**
 * Interface for skills categorized by type
 * - Career skills: top 5 career-based skills
 * - Role-specific skills: top 10 role-specific performance-based skills
 * - Culture-specific skills: top 5 culture-specific performance-based skills
 */
export interface IExtractedSkills {
  careerSkills: ISkill[];
  roleSpecificSkills: ISkill[];
  cultureSpecificSkills: ISkill[];
}

// Interface for error response from GPT
export interface IGptErrorResponse {
  error: string;
  message: string;
}

// Use union type for GPT responses
export type IGptResponse = IExtractedSkills | IGptErrorResponse;

/**
 * Job interface matching the database schema
 */
export interface IJobGenerationRequest {
  id?: number;
  user_id: number;
  org_id: number;
  department_id: number;
  title: string;
  employment_type: EmploymentType;
  salary_range: string;
  salary_cycle: SalaryCycle;
  location_type: LocationType;
  location: string;
  state: string;
  city: string;
  role_overview: string;
  experience_level: string;
  responsibilities: string;
  educations_requirement: string;
  skills_and_software_expertise: string;
  certifications: string;
  experience_required: string;
  ideal_candidate_traits: string;
  about_company: string;
  perks_benefits: string;
  tone_style: ToneStyle;
  additional_info: string;
  show_compliance: boolean;
  compliance_statement: string[];
  final_job_description?: string;
  final_job_description_html?: string;
  jd_link?: string;
  hiring_type: HiringType;
  created_ts?: Date;
  updated_ts?: Date;
}
