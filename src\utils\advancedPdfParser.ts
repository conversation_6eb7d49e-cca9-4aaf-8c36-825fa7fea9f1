import pdf from "pdf-parse";
import pdf2pic from "pdf2pic";
import Tesseract from "tesseract.js";
import { createWorker } from "tesseract.js";
import * as fs from "fs";
import * as path from "path";
import { promisify } from "util";
import { handleSentryError } from "./helper";

const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);

export interface ParsedPdfResult {
  text: string;
  images?: Buffer[];
  metadata?: {
    pages: number;
    title?: string;
    author?: string;
    creator?: string;
    producer?: string;
    creationDate?: Date;
    modificationDate?: Date;
  };
  extractionMethod: 'text' | 'ocr' | 'hybrid';
  confidence?: number;
}

export class AdvancedPdfParser {
  private tempDir: string;

  constructor() {
    this.tempDir = path.join(process.cwd(), 'temp', 'pdf-processing');
    this.ensureTempDir();
  }

  private async ensureTempDir(): Promise<void> {
    try {
      await mkdir(this.tempDir, { recursive: true });
    } catch (error) {
      console.warn('Failed to create temp directory:', error);
    }
  }

  /**
   * Main parsing method that tries multiple approaches
   */
  async parsePdf(buffer: Buffer, options: {
    extractImages?: boolean;
    useOcr?: boolean;
    ocrLanguage?: string;
    maxRetries?: number;
  } = {}): Promise<ParsedPdfResult> {
    const {
      extractImages = false,
      useOcr = true,
      ocrLanguage = 'eng',
      maxRetries = 3
    } = options;

    let attempt = 0;
    let lastError: Error | null = null;

    while (attempt < maxRetries) {
      try {
        // Step 1: Try standard text extraction first
        const textResult = await this.extractTextWithPdfParse(buffer);
        
        // Check if we got meaningful text (not just whitespace or minimal content)
        const hasGoodText = textResult.text.trim().length > 50 && 
                           this.hasReadableContent(textResult.text);

        if (hasGoodText && !extractImages) {
          return {
            ...textResult,
            extractionMethod: 'text'
          };
        }

        // Step 2: If text extraction failed or we need images, use OCR approach
        if (useOcr || extractImages || !hasGoodText) {
          const ocrResult = await this.extractWithOcr(buffer, {
            extractImages,
            language: ocrLanguage
          });

          // Use OCR text if it's better than direct text extraction
          const finalText = ocrResult.text.length > textResult.text.length ? 
                           ocrResult.text : textResult.text;

          return {
            text: finalText,
            images: ocrResult.images,
            metadata: textResult.metadata,
            extractionMethod: hasGoodText ? 'hybrid' : 'ocr',
            confidence: ocrResult.confidence
          };
        }

        return textResult;

      } catch (error) {
        lastError = error as Error;
        attempt++;
        console.warn(`PDF parsing attempt ${attempt} failed:`, error);
        
        if (attempt < maxRetries) {
          // Wait before retry with exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
        }
      }
    }

    // If all attempts failed, throw the last error
    throw lastError || new Error('PDF parsing failed after all retries');
  }

  /**
   * Extract text using pdf-parse library
   */
  private async extractTextWithPdfParse(buffer: Buffer): Promise<ParsedPdfResult> {
    try {
      const data = await pdf(buffer);
      
      return {
        text: data.text || '',
        metadata: {
          pages: data.numpages,
          title: data.info?.Title,
          author: data.info?.Author,
          creator: data.info?.Creator,
          producer: data.info?.Producer,
          creationDate: data.info?.CreationDate,
          modificationDate: data.info?.ModDate
        },
        extractionMethod: 'text'
      };
    } catch (error) {
      handleSentryError(error, 'extractTextWithPdfParse');
      throw new Error(`PDF text extraction failed: ${error.message}`);
    }
  }

  /**
   * Extract text and images using OCR
   */
  private async extractWithOcr(buffer: Buffer, options: {
    extractImages?: boolean;
    language?: string;
  } = {}): Promise<{ text: string; images?: Buffer[]; confidence?: number }> {
    const { extractImages = false, language = 'eng' } = options;
    
    let tempFiles: string[] = [];
    
    try {
      // Convert PDF to images
      const convert = pdf2pic.fromBuffer(buffer, {
        density: 300,           // Higher DPI for better OCR
        saveFilename: "page",
        savePath: this.tempDir,
        format: "png",
        width: 2000,           // High resolution for better text recognition
        height: 2000
      });

      const results = await convert.bulk(-1); // Convert all pages
      tempFiles = results.map(result => result.path);

      let allText = '';
      let totalConfidence = 0;
      const images: Buffer[] = [];

      // Process each page with OCR
      for (let i = 0; i < results.length; i++) {
        const imagePath = results[i].path;
        
        // Extract images if requested
        if (extractImages) {
          const imageBuffer = await fs.promises.readFile(imagePath);
          images.push(imageBuffer);
        }

        // Perform OCR on the image
        const { data: { text, confidence } } = await Tesseract.recognize(
          imagePath,
          language,
          {
            logger: m => console.log(m) // Optional: log OCR progress
          }
        );

        allText += text + '\n\n';
        totalConfidence += confidence;
      }

      const averageConfidence = totalConfidence / results.length;

      return {
        text: allText.trim(),
        images: extractImages ? images : undefined,
        confidence: averageConfidence
      };

    } catch (error) {
      handleSentryError(error, 'extractWithOcr');
      throw new Error(`OCR extraction failed: ${error.message}`);
    } finally {
      // Clean up temporary files
      await this.cleanupTempFiles(tempFiles);
    }
  }

  /**
   * Check if extracted text contains readable content
   */
  private hasReadableContent(text: string): boolean {
    const cleanText = text.trim();
    
    // Check for minimum length
    if (cleanText.length < 50) return false;
    
    // Check for reasonable word count
    const words = cleanText.split(/\s+/).filter(word => word.length > 2);
    if (words.length < 10) return false;
    
    // Check for readable characters ratio
    const readableChars = cleanText.match(/[a-zA-Z0-9\s]/g)?.length || 0;
    const readableRatio = readableChars / cleanText.length;
    
    return readableRatio > 0.7; // At least 70% readable characters
  }

  /**
   * Clean up temporary files
   */
  private async cleanupTempFiles(filePaths: string[]): Promise<void> {
    for (const filePath of filePaths) {
      try {
        await unlink(filePath);
      } catch (error) {
        console.warn(`Failed to delete temp file ${filePath}:`, error);
      }
    }
  }

  /**
   * Extract only images from PDF
   */
  async extractImages(buffer: Buffer): Promise<Buffer[]> {
    const result = await this.parsePdf(buffer, { 
      extractImages: true, 
      useOcr: true 
    });
    return result.images || [];
  }

  /**
   * Get PDF metadata without full parsing
   */
  async getMetadata(buffer: Buffer): Promise<ParsedPdfResult['metadata']> {
    try {
      const data = await pdf(buffer);
      return {
        pages: data.numpages,
        title: data.info?.Title,
        author: data.info?.Author,
        creator: data.info?.Creator,
        producer: data.info?.Producer,
        creationDate: data.info?.CreationDate,
        modificationDate: data.info?.ModDate
      };
    } catch (error) {
      handleSentryError(error, 'getMetadata');
      return undefined;
    }
  }
}

// Export singleton instance
export const advancedPdfParser = new AdvancedPdfParser();

// Backward compatibility function
export const parsePdfWithAdvancedRetries = async (
  buffer: Buffer,
  maxAttempts = 3
): Promise<{ text: string } | null> => {
  try {
    const result = await advancedPdfParser.parsePdf(buffer, {
      useOcr: true,
      extractImages: false,
      maxRetries: maxAttempts
    });
    
    return { text: result.text };
  } catch (error) {
    handleSentryError(error, 'parsePdfWithAdvancedRetries');
    return null;
  }
};
