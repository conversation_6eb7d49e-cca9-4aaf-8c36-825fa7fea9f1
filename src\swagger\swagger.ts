import envConfig from "../config/envConfig";

const ENV = envConfig();

const swaggerDefinition = {
  definition: {
    openapi: "3.0.0",

    info: {
      title: "Stratum9 Hiring Api Documentation",
      version: "1.0.0",
      description:
        "This is a REST API application made with Express. It retrieves data from Database.",
    },

    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
        },
      },
    },

    security: [
      {
        bearerAuth: [],
      },
    ],

    servers: [
      {
        url: ENV.s9_innerview_backend_url,
        description: `${ENV.server_name} environment`,
      },
    ],

    // Ensure scheme is explicitly set
    schemes: ["http", "https"],
  },

  // Configure Swagger options to handle CORS
  swaggerOptions: {
    withCredentials: true,
    requestInterceptor: (req) => {
      req.headers["Content-Type"] = "application/json";
      req.headers["Access-Control-Allow-Origin"] = "*";
      return req;
    },
    responseInterceptor: (res) => res,
  },

  apis: ENV.env === "local" ? ["src/**/*.ts"] : ["**/*.ts", "src/**/*.js"],
};
export default swaggerDefinition;
