/* eslint-disable no-unused-vars */
import { Request, Response, NextFunction } from "express";
import { handleSentryError } from "../utils/helper";

/**
 * Recursively sanitizes and trims all string values in an object
 * @param data Object to sanitize
 * @returns Sanitized object
 */
const sanitizeData = (data: any): any => {
  if (!data) return data;

  if (typeof data === "string") {
    // Trim the string and sanitize by removing potentially harmful characters
    // Here we're trimming and replacing HTML special chars
    return (
      data
        .trim()
        // .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#039;")
    );
  }

  if (Array.isArray(data)) {
    return data.map((item) => sanitizeData(item));
  }

  if (typeof data === "object") {
    const sanitizedData: any = {};

    Object.keys(data).forEach((key) => {
      sanitizedData[key] = sanitizeData(data[key]);
    });

    return sanitizedData;
  }

  return data;
};

/**
 * A middleware to sanitize and trim request body data
 * Should be applied before validation middleware
 * @returns {Middleware}
 */
export const sanitizeBody =
  () =>
  (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (req.body) {
        req.body = sanitizeData(req.body);
      }
      next();
    } catch (err) {
      handleSentryError(err, "sanitizeBody");
      next(err);
    }
  };

/**
 * A middleware to sanitize and trim request query parameters
 * @returns {Middleware}
 */
export const sanitizeQuery =
  () =>
  (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (req.query) {
        req.query = sanitizeData(req.query);
      }
      next();
    } catch (err) {
      handleSentryError(err, "sanitizeQuery");
      next(err);
    }
  };

/**
 * A middleware to sanitize and trim request parameters
 * @returns {Middleware}
 */
export const sanitizeParams =
  () =>
  (req: Request, res: Response, next: NextFunction): void => {
    try {
      if (req.params) {
        req.params = sanitizeData(req.params);
      }
      next();
    } catch (err) {
      handleSentryError(err, "sanitizeParams");
      next(err);
    }
  };
