import { Request, Response } from "express";
import { parsePdfAdvanced } from "../../utils/helper";
import { API_RESPONSE_MSG } from "../../utils/constants";
import { handleSentryError } from "../../utils/helper";

/**
 * Advanced PDF parsing controller with image extraction and OCR support
 * Handles complex PDF formats including template resumes
 * 
 * @param {Request} req - The HTTP request object with the uploaded PDF file
 * @param {Response} res - The HTTP response object
 */
export const parseAdvancedPdf = async (
  req: Request,
  res: Response
): Promise<Response> => {
  try {
    // Check if file was uploaded
    if (!req.file || !req.file.buffer) {
      return res.status(400).json({
        success: false,
        message: API_RESPONSE_MSG.no_pdf_file_uploaded,
        code: 400,
      });
    }

    // Parse options from request body or use defaults
    const {
      extractImages = false,
      useOcr = true,
      ocrLanguage = 'eng'
    } = req.body;

    try {
      const bufferCopy = Buffer.from(req.file.buffer);
      
      // Use advanced PDF parsing
      const result = await parsePdfAdvanced(bufferCopy, {
        extractImages: Boolean(extractImages),
        useOcr: Boolean(useOcr),
        ocrLanguage: String(ocrLanguage)
      });

      if (!result || !result.text.trim()) {
        return res.status(422).json({
          success: false,
          message: "Failed to extract text from PDF",
          error: "No readable content found in the PDF",
          code: 422,
        });
      }

      // Prepare response data
      const responseData: any = {
        text: result.text,
        extractionMethod: result.extractionMethod,
        metadata: result.metadata,
        confidence: result.confidence
      };

      // Include images if they were extracted
      if (result.images && result.images.length > 0) {
        responseData.images = result.images.map((imageBuffer, index) => ({
          index,
          size: imageBuffer.length,
          base64: imageBuffer.toString('base64'),
          mimeType: 'image/png'
        }));
        responseData.imageCount = result.images.length;
      }

      return res.status(200).json({
        success: true,
        message: "PDF processed successfully",
        data: responseData,
        code: 200,
      });

    } catch (pdfError) {
      handleSentryError(pdfError, "parseAdvancedPdf");
      return res.status(422).json({
        success: false,
        message: "Failed to process PDF",
        error: pdfError.message,
        code: 422,
      });
    }
  } catch (error) {
    handleSentryError(error, "parseAdvancedPdf");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Extract only images from PDF
 * 
 * @param {Request} req - The HTTP request object with the uploaded PDF file
 * @param {Response} res - The HTTP response object
 */
export const extractPdfImages = async (
  req: Request,
  res: Response
): Promise<Response> => {
  try {
    // Check if file was uploaded
    if (!req.file || !req.file.buffer) {
      return res.status(400).json({
        success: false,
        message: API_RESPONSE_MSG.no_pdf_file_uploaded,
        code: 400,
      });
    }

    try {
      const bufferCopy = Buffer.from(req.file.buffer);
      
      // Extract only images
      const result = await parsePdfAdvanced(bufferCopy, {
        extractImages: true,
        useOcr: false // We only want images, not text
      });

      if (!result.images || result.images.length === 0) {
        return res.status(404).json({
          success: false,
          message: "No images found in the PDF",
          code: 404,
        });
      }

      // Prepare image data
      const images = result.images.map((imageBuffer, index) => ({
        index,
        size: imageBuffer.length,
        base64: imageBuffer.toString('base64'),
        mimeType: 'image/png'
      }));

      return res.status(200).json({
        success: true,
        message: `Successfully extracted ${images.length} images from PDF`,
        data: {
          imageCount: images.length,
          images,
          metadata: result.metadata
        },
        code: 200,
      });

    } catch (pdfError) {
      handleSentryError(pdfError, "extractPdfImages");
      return res.status(422).json({
        success: false,
        message: "Failed to extract images from PDF",
        error: pdfError.message,
        code: 422,
      });
    }
  } catch (error) {
    handleSentryError(error, "extractPdfImages");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};

/**
 * Get PDF metadata without full parsing
 * 
 * @param {Request} req - The HTTP request object with the uploaded PDF file
 * @param {Response} res - The HTTP response object
 */
export const getPdfMetadata = async (
  req: Request,
  res: Response
): Promise<Response> => {
  try {
    // Check if file was uploaded
    if (!req.file || !req.file.buffer) {
      return res.status(400).json({
        success: false,
        message: API_RESPONSE_MSG.no_pdf_file_uploaded,
        code: 400,
      });
    }

    try {
      const bufferCopy = Buffer.from(req.file.buffer);
      
      // Get only metadata
      const result = await parsePdfAdvanced(bufferCopy, {
        extractImages: false,
        useOcr: false // Just get basic metadata
      });

      return res.status(200).json({
        success: true,
        message: "PDF metadata extracted successfully",
        data: {
          metadata: result.metadata,
          hasText: Boolean(result.text && result.text.trim().length > 0),
          textLength: result.text ? result.text.length : 0
        },
        code: 200,
      });

    } catch (pdfError) {
      handleSentryError(pdfError, "getPdfMetadata");
      return res.status(422).json({
        success: false,
        message: "Failed to extract PDF metadata",
        error: pdfError.message,
        code: 422,
      });
    }
  } catch (error) {
    handleSentryError(error, "getPdfMetadata");
    return res.status(500).json({
      success: false,
      message: error.message || API_RESPONSE_MSG.internal_server_error,
      code: 500,
    });
  }
};
