import sendMail from "./sendgrid";
import { JOB_APPLY_EMAIL_CONTENTS } from "./constants";
import { getTruncatedName, handleSentryError } from "./helper";

const sendJobApplyVerificationMail = async ({ email, orgName, logo }) => {
  try {
    const otp = Math.floor(1000 + Math.random() * 9000);
    const { SUBJECT, TEXT_CONTENT, MESSAGE } = JOB_APPLY_EMAIL_CONTENTS;

    const htmlContent = `
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <title>Verification</title>
      <style>
         body {
         margin: 0;
         background-color: #cccccc;
         }
         table {
         border-spacing: 0;
         }
         td {
         padding: 0;
         }
         img {
         border: 0;
         }
         .wrapper {
         width: 100%;
         table-layout: fixed;
         background-color: #436EB6;
         padding-bottom: 60px;
         }
         .main {
         background-color: #fff;
         margin: 0 auto;
         width: 100%;
         max-width: 600px;
         border-spacing: 0;
         font-family: sans-serif;
         color: #171a1b;
         padding: 20px;
         font-size: 14px;
         }
         td {
         padding: 5px 0;
         }
         ul{
            list-style: none;
         }
      </style>
   </head>
   <body style="margin: 0">
      <center class="wrapper">
      ${
        logo
          ? `
         
         <img
            src="${logo}"
            alt="logo"
            style="
            margin: 0 auto;
            width: 150px;
            margin-bottom: 20px;
            margin-top: 20px;
            background:#fff;
            padding: 5px ;
            border-color:#436eb6;
            "
            />
         `
          : `<h2>${getTruncatedName(orgName)} </h2>`
      }
         <table class="main" width="100%" style="border-right: solid 10px #436eb6;border-left: solid 10px #436eb6;">
            <tr>
               <td align="center">
                 <h2 style="margin: 0; font-size: 24px; color: #436EB6;">${MESSAGE}</h2>

               </td>
            </tr>
            <tr>
               <td align="center">
                  <p>Your Verification Code is: <span style="color: #436EB6; font-weight: bold;">${otp}</span></p>

                 <p style="color: red;">This code is used to validate your job application and is valid for 5 minutes</p>


               </td>
            </tr>
            <tr>
               <td></td>
            </tr>
            <tr>
               <td algin="center">
               <div style="text-align: center;">

               <img
                  src="https://stratum9-images-dev.s3-accelerate.amazonaws.com/resources/best-seller+(1).png"
                  alt="logo"
                  style="
                  width: 200px;
                  height: 200px;
                  margin-bottom: 0px;
                  margin-top: 0px;
                  background:#fff;
                  "
                  />
               </div>
               </td>
            </tr>
            <tr>
               <td>
                  <ul style="list-style: none; padding: 0; margin: 0;">
                     <li><a href=""></a></li>
                     <li><a href=""></a></li>
                  </ul>
               </td>
            </tr>
            <tr>
               <td align="center">
                  <ul
                     style="
                     list-style: none;
                     padding: 0;
                     margin: 0;
                     align-items: center;"   
                     >
                     <li style="padding-right: 0px">
                        <a href="https://www.stratum-nine.com/terms-and-conditions" style="color: #000"> Terms and conditions </a>
                     </li>
                     <li style="padding-right: 0px">
                        <a href="https://www.stratum-nine.com/privacy-policy" style="color: #000"> Privacy Policy </a>
                     </li>
                  </ul>
               </td>
            </tr>
         </table>
      </center>
   </body>
</html>`;

    const response = sendMail({
      email: email.toLowerCase(),
      subject: SUBJECT,
      textContent: TEXT_CONTENT,
      htmlContent,
    });

    return { response, otp };
  } catch (error) {
    console.log("Error sending job application verification email:", error);

    handleSentryError(error, "sendJobApplyVerificationMail");

    return { error: "Email sending failed" }; // You can customize the error message as needed
  }
};

export default sendJobApplyVerificationMail;
