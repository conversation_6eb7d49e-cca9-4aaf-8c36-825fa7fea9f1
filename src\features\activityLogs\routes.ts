// src/features/candidatesManagement/routes.ts
import express from "express";
import auth from "../../middleware/auth";
import { ROUTES } from "../../utils/constants";
import HandleErrors from "../../middleware/handleError";
import { queryValidation } from "../../middleware/validateSchema";
import getActivityLogsValidation from "./validation";
import { authorizedForViewAuditLogs } from "../../middleware/isAuthorized";
import getLogsController from "./controller";

const activityLogsRoute = express.Router();

/**
 * @swagger
 * /activity-logs/get-logs:
 *   get:
 *     summary: Get activity logs
 *     description: Retrieves activity logs with filtering and pagination
 *     tags: [ActivityLogs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number for pagination (starts from 0)
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page
 *       - in: query
 *         name: entityType
 *         schema:
 *           type: string
 *         description: Filter by entity type
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *         description: Filter by action type
 *       - in: query
 *         name: startDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter logs from this date (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter logs until this date (YYYY-MM-DD)
 *     responses:
 *       200:
 *         description: Activity logs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "activity_logs_fetched"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       auditId:
 *                         type: integer
 *                         example: 4054
 *                       orgId:
 *                         type: integer
 *                         example: 251
 *                       logType:
 *                         type: string
 *                         example: "Login"
 *                       userId:
 *                         type: integer
 *                         example: 844
 *                       entityId:
 *                         type: integer
 *                         example: 844
 *                       entityType:
 *                         type: string
 *                         example: "User"
 *                       oldValue:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       newValue:
 *                         type: string
 *                         nullable: true
 *                         example: null
 *                       timestamp:
 *                         type: string
 *                         format: date-time
 *                         example: "2025-09-18T10:18:58.403Z"
 *                       comments:
 *                         type: string
 *                         example: "User successfully logged."
 *                       actionBy:
 *                         type: string
 *                         example: ""
 *                       actionByName:
 *                         type: string
 *                         example: "John Doe"
 *       400:
 *         description: Invalid request parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid request parameters"
 *       401:
 *         description: Unauthorized access
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "unauthorized"
 *       403:
 *         description: Forbidden - user doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Forbidden - user doesn't have required permissions"
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "internal_server_error"
 */
activityLogsRoute.get(
  ROUTES.ACTIVITY_LOGS.GET_LOGS,
  auth,
  authorizedForViewAuditLogs,
  queryValidation(getActivityLogsValidation),
  HandleErrors(getLogsController)
);

export default activityLogsRoute;
