export interface ILogin extends IForgotPassword {
  password: string;
  fcmToken?: string;
  timezone?: string;
}

export interface IForgotPassword {
  email: string;
}

export interface IVerify extends IForgotPassword {
  otp: string;
  type: string;
  name?: string;
}

export interface IResetPassword extends IForgotPassword {
  password: string;
  otp: string;
}

export interface ISignup extends IForgotPassword {
  password: string;
  organizationCode: string;
  organizationName: string;
  firstName: string;
  lastName: string;
  location: string;
  passwordType: string;
  websiteURL?: string;
  branchName?: string;
  branchCode?: string;
  tinNumber?: string;
}
