import sendMail from "./sendgrid";
import { INTERVIEW_EMAIL_TYPE, INTERVIEW_ROUND_TYPE } from "./constants";
import { getTruncatedName, handleSentryError } from "./helper";
import { RoundType } from "../schema/s9-innerview/interview";

// Format date with proper timezone formatting and dynamic timezone based on location
const formatDateWithProperTimezone = (date: Date): string => {
  const dateObj = new Date(date);

  // Format the date parts
  const month = dateObj.toLocaleString("en-US", { month: "short" });
  const day = dateObj.getDate();
  const year = dateObj.getFullYear();
  // Ensure time is in 24-hour format (HH:MM:SS)
  const time = dateObj.toLocaleString("en-US", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
    second: "2-digit",
  });

  // Get timezone information directly from date string
  const dateStr = dateObj.toString();
  // Format: "Wed Aug 19 2025 13:13:53 GMT+0530 (India Standard Time)"
  // Extract GMT+0530 and India Standard Time
  const tzRegex = /(GMT[+-][0-9]{4})\s+\(([^)]+)\)/;
  const tzMatch = dateStr.match(tzRegex);

  let formattedOffset = "";
  let timezoneName = "";

  if (tzMatch) {
    // [0] = full match, [1] = GMT+0530, [2] = India Standard Time
    const [, rawOffset, name] = tzMatch as RegExpMatchArray;
    formattedOffset = rawOffset.replace(/(GMT[+-]\d{2})(\d{2})/, "$1:$2"); // GMT+5:30
    timezoneName = name; // India Standard Time
  }

  // Return formatted date with timezone
  return `${month} ${day}, ${year} ${time} ${formattedOffset} (${timezoneName})`;
};

const generateIcsFile = (
  date: Date,
  duration: number,
  position: string,
  meetingLink: string,
  interviewerEmail: string,
  candidateEmail: string,
  orgEmail: string,
  orgName: string,
  candidateName: string
) => {
  const startDate = new Date(date);
  const endDate = new Date(date);
  endDate.setMinutes(endDate.getMinutes() + duration);

  // Get timezone information from the date
  const dateStr = startDate.toString();
  const tzRegex = /(GMT[+-][0-9]{4})\s+\(([^)]+)\)/;
  const tzMatch = dateStr.match(tzRegex);
  const timezoneName = tzMatch ? tzMatch[2] : "UTC"; // e.g., "India Standard Time"
  const timezoneOffset = tzMatch ? tzMatch[1].replace("GMT", "") : "+0000";

  // Format dates for iCalendar - using local time format with TZID
  const formatLocalDateForICS = (dateObj: Date) =>
    `${
      dateObj.getFullYear() +
      String(dateObj.getMonth() + 1).padStart(2, "0") +
      String(dateObj.getDate()).padStart(2, "0")
    }T${String(dateObj.getHours()).padStart(2, "0")}${String(
      dateObj.getMinutes()
    ).padStart(2, "0")}${String(dateObj.getSeconds()).padStart(2, "0")}`;

  // Format dates for UTC time (for DTSTAMP)
  const formatUTCDateForICS = (dateObj: Date) =>
    dateObj
      .toISOString()
      .replace(/[-:.]/g, "")
      .replace(/\d{3}Z$/, "Z");

  const formatLocation = () => {
    if (!meetingLink) {
      return "In-person (venue details will be shared separately)";
    }

    return "Online Interview";
  };

  const formatDescription = () => {
    let description = `You are invited to an interview for the ${position} position at Stratum9.\\n\\n`;
    description += `Please ensure you are prepared and join the interview promptly at the scheduled time.\\n\\n`;

    if (meetingLink) {
      // Use a cleaner format for the meeting link
      description += `Confirm you have a stable internet connection and access to the meeting link \\n\\n Meeting link: ${meetingLink}\\n\\n`;
    } else {
      description += `Venue details will be shared separately.\\n\\n`;
    }

    description += `Contact: ${orgEmail}`;
    return description;
  };

  const icsContent = `
      BEGIN:VCALENDAR
      PRODID:-//Stratum9//InnerView//EN
      VERSION:2.0
      CALSCALE:GREGORIAN
      METHOD:REQUEST
      BEGIN:VTIMEZONE
      TZID:${timezoneName}
      BEGIN:STANDARD
      DTSTART:19700101T000000
      TZOFFSETFROM:${timezoneOffset}
      TZOFFSETTO:${timezoneOffset}
      END:STANDARD
      END:VTIMEZONE
      BEGIN:VEVENT
      UID:${Date.now()}@stratum9-innerview.com
      DTSTAMP:${formatUTCDateForICS(new Date())}
      DTSTART;TZID=${timezoneName}:${formatLocalDateForICS(startDate)}
      DTEND;TZID=${timezoneName}:${formatLocalDateForICS(endDate)}
      SUMMARY:Interview for ${position}
      DESCRIPTION:${formatDescription()}
      LOCATION:${formatLocation()}
      ORGANIZER;CN=${orgName}:mailto:${interviewerEmail}
      ATTENDEE;CN=${candidateName};ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:mailto:${candidateEmail}
      SEQUENCE:0
      STATUS:CONFIRMED
      BEGIN:VALARM
      ACTION:DISPLAY
      DESCRIPTION:Reminder: Interview for ${position}
      TRIGGER:-PT15M
      END:VALARM
      END:VEVENT
      END:VCALENDAR`;

  // Encode to base64 (SendGrid requires attachments to be base64 encoded)
  const icsBase64 = Buffer.from(icsContent).toString("base64");

  // Prepare the email
  const attachments = [
    {
      content: icsBase64,
      filename: "invite.ics",
      type: "text/calendar",
      disposition: "attachment",
      content_id: "calendar-event",
    },
  ];

  return attachments;
};

// Export as default function
export default async function sendInterviewEmail({
  interviewerEmail,
  candidateEmail,
  candidateName,
  resume,
  position,
  interviewType,
  date,
  duration,
  interviewRound,
  meetingLink,
  interviewerName,
  previousInterviewerName,
  previousInterviewerEmail,
  previousCandidateName,
  previousCandidateEmail,
  orgName,
  type,
  previousDate,
  documents = [],
  logo,
  orgEmail,
}: {
  interviewerEmail: string;
  candidateEmail: string;
  candidateName: string;
  position: string;
  interviewType: string;
  date: Date;
  orgName: string;
  duration: number;
  interviewRound: number;
  meetingLink: string;
  resume: string;
  interviewerName: string;
  previousInterviewerName?: string;
  previousInterviewerEmail?: string;
  previousCandidateName?: string;
  previousCandidateEmail?: string;
  type: string;
  previousDate?: Date;
  documents?: Array<{ url: string; name: string }>;
  logo?: string;
  orgEmail: string;
}) {
  try {
    const attachments = generateIcsFile(
      date,
      duration,
      position,
      meetingLink,
      interviewerEmail,
      candidateEmail,
      orgEmail,
      orgName,
      candidateName
    );
    const formattedInterviewType =
      interviewType === RoundType.ONE_ON_ONE
        ? INTERVIEW_ROUND_TYPE.IN_PERSON
        : INTERVIEW_ROUND_TYPE.ONLINE_INTERVIEW;
    console.log("meetingLink===>>>3134343413", meetingLink);
    const interviewerEmailTemplate = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Scheduled - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
   <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
${
  logo
    ? `
  <img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
  `
    : `<h2>${getTruncatedName(orgName)} </h2>`
}
            </td>
        </tr>

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                        style="color: #3182ce; font-weight: 600;">${interviewerName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    An interview has been scheduled. Please find the details below:
                </p>

                <!-- Interview Details Box -->
                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                    style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                    <tr>
                        <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                            <strong>Candidate:</strong> ${candidateName}<br>
                            <strong>Candidate Email:</strong> 
                            <a href="mailto:${candidateEmail}">${candidateEmail}</a><br>
                            <strong>Position:</strong> ${position}<br>
                            <strong>Interview Type:</strong> ${formattedInterviewType}<br>
                            <strong>Date & Time:</strong> ${formatDateWithProperTimezone(new Date(date))}<br>
                            <strong>Duration:</strong> ${duration} minutes<br>
                            <strong>Interview Round:</strong> ${interviewRound}<br>
                            ${resume ? `<strong>Resume:</strong> <a href="${resume}" style="color: #3182ce; text-decoration: none;">Click to view resume</a><br>` : ""}
                            ${
                              documents && documents.length > 0
                                ? `
                            <strong>Additional Documents:</strong><br>
                            <ul style="margin-top: 5px;">
                              ${documents.map((doc) => `<li><a href="${doc.url}" style="color: #3182ce; text-decoration: none;">${doc.name}</a></li>`).join("")}
                            </ul>
                            `
                                : ""
                            }
                        </td>
                    </tr>
                </table>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We kindly request you to review the candidate's profile in advance and be prepared with relevant questions.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    If you experience any scheduling conflicts or require additional support, please notify the hiring coordinator at the earliest.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best Regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td
                style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const updateInterviewerTemplate = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Updated - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
            ${
              logo
                ? `<img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">`
                : `<h2>${getTruncatedName(orgName)} </h2>`
            }
            </td>
        </tr>

        <!-- Heading Section -->

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                        style="color: #3182ce; font-weight: 600;">${interviewerName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Please note that the interview details for the candidate have been <strong>updated</strong>. Kindly find the revised schedule below:
                </p>

                <!-- Interview Details Box -->
                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                    style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                    <tr>
                        <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                            <strong>Candidate:</strong> ${candidateName}<br>
                            <strong>Candidate Email:</strong> 
                            <a href="mailto:${candidateEmail}">${candidateEmail}</a><br>
                            <strong>Position:</strong> ${position}<br>
                            <strong>Interview Type:</strong> ${formattedInterviewType}<br>
                            <strong>Date & Time:</strong> ${formatDateWithProperTimezone(new Date(date))}<br>
                            <strong>Duration:</strong> ${duration} minutes<br>
                            <strong>Interview Round:</strong> ${interviewRound}<br>
                            ${resume ? `<strong>Resume:</strong> <a href="${resume}" style="color: #3182ce; text-decoration: none;">View Resume</a><br>` : ""}
                            ${
                              documents && documents.length > 0
                                ? `
                            <strong>Additional Documents:</strong><br>
                            <ul style="margin-top: 5px;">
                              ${documents.map((doc) => `<li><a href="${doc.url}" style="color: #3182ce; text-decoration: none;">${doc.name}</a></li>`).join("")}
                            </ul>
                            `
                                : ""
                            }
                        </td>
                    </tr>
                </table>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We request you to review the candidate's profile in advance and proceed with the interview as per the updated schedule.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    If you have any conflicts or need additional support, please notify the hiring coordinator.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Thank you for your cooperation.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td
                style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const candidateUpdatedEmailTemplate = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Update - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
            ${
              logo
                ? `
              
              <img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
              `
                : `<h2>${getTruncatedName(orgName)} </h2>`
            }
            </td>
        </tr>

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                        style="color: #3182ce; font-weight: 600;">${candidateName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We would like to inform you that the details of your interview for the <span style="color: #3182ce; font-weight: 600;">${position}</span> role have been <strong>updated</strong>. Please find the revised schedule below:
                </p>

                <div style="margin: 0 0 20px 0; background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Position:</strong> ${position}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Type:</strong> ${formattedInterviewType}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Date & Time:</strong> ${formatDateWithProperTimezone(new Date(date))}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Duration:</strong> ${duration} minutes
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Round:</strong> ${interviewRound}
                    </p>
                    <p style="margin: 0 0 0 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Link / Venue:</strong> ${meetingLink ? `<a href="${meetingLink}" style="color: #3182ce; text-decoration: none; cursor: pointer;">Click to view meeting</a>` : "In-person (venue details will be shared separately)"}
                    </p>
                </div>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    If you have any questions or fore see any scheduling conflicts, please reach out to us promptly so we can assist you.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We appreciate your understanding and cooperation, and we look forward to your interview.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best Regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const candidateEmailTemplate = `
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Scheduled - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
            ${
              logo
                ? `
              <img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
              `
                : `<h2>${getTruncatedName(orgName)} </h2>`
            }
            </td>
        </tr>

        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                        style="color: #3182ce; font-weight: 600;">${candidateName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We are pleased to inform you that your interview for the <span style="color: #3182ce; font-weight: 600;">${position}</span> role has been scheduled. Please find the details below:
                </p>

                <div style="margin: 0 0 20px 0; background-color: #f5f5f5; padding: 15px; border-radius: 5px;">
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Position:</strong> ${position}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Type:</strong> ${formattedInterviewType}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Date & Time:</strong> ${formatDateWithProperTimezone(new Date(date))}
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Duration:</strong> ${duration} minutes
                    </p>
                    <p style="margin: 0 0 10px 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Round:</strong> ${interviewRound}
                    </p>
                    <p style="margin: 0 0 0 0; font-size: 16px; line-height: 1.5;">
                        <strong>Interview Link / Venue:</strong> ${meetingLink ? `<a href="${meetingLink}" style="color: #3182ce; text-decoration: none; cursor: pointer;">Click to view meeting</a>` : "In-person (venue details will be shared separately)"}
                    </p>
                </div>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Please ensure you are available at the scheduled time and:
                </p>

                ${
                  meetingLink
                    ? `<p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Test your camera, microphone, and internet connection beforehand.
                </p>`
                    : `<p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Arrive 10–15 minutes early at the given venue.
                </p>`
                }

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    If you have any questions or need to reschedule, kindly contact us at the earliest.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    We look forward to speaking with you and wish you the very best.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best Regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const interviewerCancellationTemplate = `<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Cancellation - S9 InnerView</title>
</head>

<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <!-- Main Container -->
    <table width="100%" border="0" cellspacing="0" cellpadding="0"
        style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <!-- Logo Section -->
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
            ${
              logo
                ? `
              
              <img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
              `
                : `<h2>${getTruncatedName(orgName)} </h2>`
            }
            </td>
        </tr>


        <!-- Email Content -->
        <tr>
            <td style="padding: 30px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">Dear <span
                        style="color: #3182ce; font-weight: 600;">${previousInterviewerName}</span>,</p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Please be informed that the scheduled interview has been <strong>canceled</strong>. The details are provided below for your reference:
                </p>

                <!-- Interview Details Box -->
                <table width="100%" border="0" cellspacing="0" cellpadding="0"
                    style="margin: 0 0 20px 0;background-color: #436eb61c;border-radius: 16px;">
                    <tr>
                        <td style="padding: 15px; font-size: 16px; line-height: 1.8;">
                            <strong>Candidate:</strong> ${candidateName}<br>
                            <strong>Candidate Email:</strong> 
                            <a href="mailto:${candidateEmail}">${candidateEmail}</a><br>
                            <strong>Position:</strong> ${position}<br>
                            <strong>Interview Type:</strong> ${formattedInterviewType}<br>
                            <strong>Date & Time:</strong> ${formatDateWithProperTimezone(new Date(date))}<br>
                            <strong>Duration:</strong> ${duration} minutes<br>
                            <strong>Interview Round:</strong> ${interviewRound}<br>
                            ${resume ? `<strong>Resume:</strong> <a href="${resume}" style="color: #3182ce; text-decoration: none;">View Resume</a><br>` : ""}
                            ${
                              documents && documents.length > 0
                                ? `
                            <strong>Additional Documents:</strong><br>
                            <ul style="margin-top: 5px;">
                              ${documents.map((doc) => `<li><a href="${doc.url}" style="color: #3182ce; text-decoration: none;">${doc.name}</a></li>`).join("")}
                            </ul>
                            `
                                : ""
                            }
                        </td>
                    </tr>
                </table>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    No further action is required from your end at this time. If this interview is rescheduled, you will receive a separate notification with the updated details.
                </p>

                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Thank you for your understanding and continued support.
                </p>

                <p style="margin: 0; font-size: 16px; line-height: 1.5;">
                    <strong>Best regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>

        <!-- Footer -->
        <tr>
            <td
                style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>

</html>`;

    const candidateCancellationTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interview Cancellation - S9 InnerView</title>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; color: #333333; background-color: #f5f5f5a8;">
    <table width="100%" border="0" cellspacing="0" cellpadding="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
        <tr>
            <td align="center" style="padding:20px 0 0; background-color: #fff;">
            ${
              logo
                ? `
              
              <img src="${logo}" alt="S9 InnerView Logo" width="180" style="height: auto; display: block;">
              `
                : `<h2>${getTruncatedName(orgName)} </h2>`
            }
            </td>
        </tr>
        <tr>
            <td style="padding: 30px 20px 20px;">
                <p style="margin: 0 0 20px 0; font-size: 16px; line-height: 1.5;">
                    Dear <span style="color: #3182ce; font-weight: 600;">${previousCandidateName || "Candidate"}</span>,
                </p>
                <p style="margin-top: 15px; font-size: 16px; line-height: 1.6;">
                    We regret to inform you that the scheduled interview for the <span style="color: #3182ce; font-weight: 600;">${position}</span> role on <strong>${formatDateWithProperTimezone(date)}</strong> has been <strong>canceled</strong>.
                </p>
                <p style="margin-top: 15px; font-size: 16px; line-height: 1.6;">
                    At this stage, no further action is required from your side. If the interview is rescheduled or if there are next steps in the process, we will notify you promptly.
                </p>
                <p style="margin-top: 15px; font-size: 16px; line-height: 1.6;">
                    We sincerely apologize for any inconvenience this may cause and appreciate your understanding.
                </p>
                <p style="margin-top: 25px; font-size: 16px; line-height: 1.6;">
                    <strong>Best Regards,</strong><br>
                    HR Team<br>
                    ${orgName}
                </p>
            </td>
        </tr>
        <tr>
            <td style="padding: 15px 20px; background-color: #333333; font-size: 12px; color: #ffffff; text-align: center;">
                &copy; ${new Date().getFullYear()} STRATUM 9. All rights reserved.
            </td>
        </tr>
    </table>
</body>
</html>
`;

    // Dynamic subject templates for interviewer emails
    const interviewScheduleInterviewerSubject =
      "Interview Scheduled: {Position} | {CandidateName} | {FormattedDate}";
    const interviewUpdateInterviewerSubject =
      "Updated Interview Schedule – {Position} | {CandidateName}  | {FormattedDate}";
    const interviewCancelInterviewerSubject =
      "Interview Canceled – {Position} | {CandidateName} | {InterviewRound}";

    // Dynamic subject templates for candidate emails
    const interviewScheduleCandidateSubject =
      "Interview Scheduled: {Position} | {FormattedDate}";
    const interviewUpdateCandidateSubject =
      "Interview Details Updated: {Position} | {FormattedDate}";
    const interviewCancelCandidateSubject =
      "Interview Canceled: {Position} | {CandidateName}";

    // Helper function to format date for subject lines
    const formatSubjectDate = (dateToFormat: Date): string =>
      new Date(dateToFormat).toLocaleDateString("en-US", {
        month: "long",
        day: "numeric",
      });

    // Format the subject date
    const formattedSubjectDate = formatSubjectDate(date);

    switch (type) {
      case INTERVIEW_EMAIL_TYPE.SCHEDULE: {
        // Format interviewer subject for scheduling
        const scheduleInterviewerSubject = interviewScheduleInterviewerSubject
          .replace("{Position}", position)
          .replace("{CandidateName}", candidateName)
          .replace("{FormattedDate}", formattedSubjectDate);

        // Format candidate subject for scheduling
        const scheduleCandidateSubject = interviewScheduleCandidateSubject
          .replace("{Position}", position)
          .replace("{FormattedDate}", formattedSubjectDate);

        // Send emails
        await sendMail({
          email: interviewerEmail,
          subject: scheduleInterviewerSubject,
          textContent: scheduleInterviewerSubject,
          htmlContent: interviewerEmailTemplate,
          attachments,
        });

        await sendMail({
          email: candidateEmail,
          subject: scheduleCandidateSubject,
          textContent: scheduleCandidateSubject,
          htmlContent: candidateEmailTemplate,
          attachments,
        });
        break;
      }

      case INTERVIEW_EMAIL_TYPE.UPDATE: {
        // Format interviewer update subject
        const updateInterviewerSubject = interviewUpdateInterviewerSubject
          .replace("{Position}", position)
          .replace("{CandidateName}", candidateName)
          .replace("{FormattedDate}", formattedSubjectDate);

        // Send update email to interviewer
        await sendMail({
          email: interviewerEmail,
          subject: updateInterviewerSubject,
          textContent: updateInterviewerSubject,
          htmlContent: updateInterviewerTemplate,
          attachments,
        });

        // Format candidate update subject
        const updateCandidateSubject = interviewUpdateCandidateSubject
          .replace("{Position}", position)
          .replace("{FormattedDate}", formattedSubjectDate);

        // Send update email to candidate
        await sendMail({
          email: candidateEmail,
          subject: updateCandidateSubject,
          textContent: updateCandidateSubject,
          htmlContent: candidateUpdatedEmailTemplate,
          attachments,
        });
        break;
      }

      case INTERVIEW_EMAIL_TYPE.INTERVIEWER_CHANGE: {
        // When interviewer changes, send cancellation to old interviewer and scheduling to new interviewer
        if (previousInterviewerEmail) {
          // Format interviewer cancel subject
          const interviewerCancelSubject = interviewCancelInterviewerSubject
            .replace("{Position}", position)
            .replace("{CandidateName}", candidateName)
            .replace("{InterviewRound}", interviewRound.toString());

          // For logging/tracking purposes
          const cancelledInterviewerName =
            previousInterviewerName || "Previous Interviewer";
          const originalScheduledDate = previousDate
            ? formatDateWithProperTimezone(new Date(previousDate))
            : formatDateWithProperTimezone(new Date(date));

          console.log(
            `Sending cancellation to ${cancelledInterviewerName} for interview originally scheduled on ${originalScheduledDate}`
          );

          await sendMail({
            email: previousInterviewerEmail,
            subject: interviewerCancelSubject,
            textContent: "Interview Assignment Cancellation",
            htmlContent: interviewerCancellationTemplate,
          });
        }

        // Format subject for new interviewer
        const newInterviewerSubject = interviewScheduleInterviewerSubject
          .replace("{Position}", position)
          .replace("{CandidateName}", candidateName)
          .replace("{FormattedDate}", formattedSubjectDate);

        await sendMail({
          email: interviewerEmail,
          subject: newInterviewerSubject,
          textContent: newInterviewerSubject,
          htmlContent: interviewerEmailTemplate,
          attachments,
        });
        // No email to candidate when only interviewer changes
        break;
      }

      case INTERVIEW_EMAIL_TYPE.CANDIDATE_CHANGE: {
        // When candidate changes, send cancellation to old candidate and scheduling to new candidate
        if (previousCandidateEmail) {
          // Create a candidate cancellation template (using the previous candidate's name)
          // Format candidate cancel subject
          const candidateCancelSubject = interviewCancelCandidateSubject
            .replace("{Position}", position)
            .replace("{CandidateName}", previousCandidateName || "Candidate");

          await sendMail({
            email: previousCandidateEmail,
            subject: candidateCancelSubject,
            textContent: "Interview Cancellation",
            htmlContent: candidateCancellationTemplate,
          });
        }

        // Format subject for interviewer
        const candidateChangeInterviewerSubject =
          interviewUpdateInterviewerSubject
            .replace("{Position}", position)
            .replace("{CandidateName}", candidateName)
            .replace("{FormattedDate}", formattedSubjectDate);

        await sendMail({
          email: interviewerEmail,
          subject: candidateChangeInterviewerSubject,
          textContent: candidateChangeInterviewerSubject,
          htmlContent: updateInterviewerTemplate,
          attachments,
        });

        // Format subject for new candidate
        const newCandidateSubject = interviewScheduleCandidateSubject
          .replace("{Position}", position)
          .replace("{FormattedDate}", formattedSubjectDate);

        await sendMail({
          email: candidateEmail,
          subject: newCandidateSubject,
          textContent: newCandidateSubject,
          htmlContent: candidateEmailTemplate,
          attachments,
        });
        // No email to interviewer when only candidate changes
        break;
      }

      case INTERVIEW_EMAIL_TYPE.CANCEL: {
        // Format interviewer cancel subject
        const cancelInterviewerSubject = interviewCancelInterviewerSubject
          .replace("{Position}", position)
          .replace("{CandidateName}", candidateName)
          .replace("{InterviewRound}", interviewRound.toString());

        // Format candidate cancel subject
        const cancelCandidateSubject = interviewCancelCandidateSubject
          .replace("{Position}", position)
          .replace("{CandidateName}", candidateName);

        // Send cancel emails
        if (interviewerEmail) {
          await sendMail({
            email: interviewerEmail,
            subject: cancelInterviewerSubject,
            textContent: "Interview Cancellation",
            htmlContent: interviewerCancellationTemplate, // Use the new interviewer cancellation template
          });
        }

        if (candidateEmail) {
          await sendMail({
            email: candidateEmail,
            subject: cancelCandidateSubject,
            textContent: "Interview Cancellation",
            htmlContent: candidateCancellationTemplate,
          });
        }
        break;
      }

      default: {
        // Default behavior (fallback)
        if (interviewerEmail) {
          // Format default interviewer subject
          const defaultInterviewerSubject = interviewUpdateInterviewerSubject
            .replace("{Position}", position)
            .replace("{CandidateName}", candidateName)
            .replace("{FormattedDate}", formattedSubjectDate);

          await sendMail({
            email: interviewerEmail,
            subject: defaultInterviewerSubject,
            textContent: defaultInterviewerSubject,
            htmlContent: interviewerEmailTemplate,
            attachments,
          });
        }

        if (candidateEmail) {
          // Format default candidate subject
          const defaultCandidateSubject = interviewUpdateCandidateSubject
            .replace("{Position}", position)
            .replace("{FormattedDate}", formattedSubjectDate);

          await sendMail({
            email: candidateEmail,
            subject: defaultCandidateSubject,
            textContent: defaultCandidateSubject,
            htmlContent: candidateEmailTemplate,
            attachments,
          });
        }
        break;
      }
    }
    return null;
  } catch (error) {
    handleSentryError(error, "sendInterviewEmail");

    return { error: "Email sending failed" };
  }
}
