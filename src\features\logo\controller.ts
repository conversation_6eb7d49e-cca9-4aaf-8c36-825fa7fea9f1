import { Request, Response } from "express";
import Logo from "./service";
import { handleSentryError } from "../../utils/helper";

const updateLogo = async (req: Request, res: Response) => {
  try {
    const result = await Logo.updateLogo(req.orgId, req.body.logo);

    return res.status(result.success ? 200 : 400).json({
      ...result,
      code: result.success ? 200 : 400,
    });
  } catch (error) {
    handleSentryError(error, "updateLogo");
    return res.status(error.output?.statusCode ?? 500).json({
      success: false,
      message: "An error occurred while updating logo",
      code: error.output?.statusCode ?? 500,
    });
  }
};

export default updateLogo;
