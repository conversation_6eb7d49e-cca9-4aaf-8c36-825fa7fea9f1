import {
  <PERSON><PERSON><PERSON>,
  PrimaryGenerated<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  CreateDateC<PERSON>umn,
  UpdateDateColumn,
  <PERSON>in<PERSON><PERSON>umn,
  ManyToOne,
} from "typeorm";
import JobApplicationsModel from "./job_applications";
import { JobsModel } from "./jobs";

@Entity("applicant_final_summaries")
export class ApplicantFinalSummariesModel {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => JobsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_id" })
  job: JobsModel;

  @Column({ name: "job_id", nullable: false })
  jobId: number;

  @ManyToOne(() => JobApplicationsModel, {
    onDelete: "CASCADE",
  })
  @JoinColumn({ name: "job_application_id" })
  jobApplication: JobApplicationsModel;

  @Column({ name: "job_application_id", nullable: false })
  jobApplicationId: number;

  @Column({ name: "overall_success_probability", type: "float" })
  overallSuccessProbability: number; // [1-10] — consider adding custom validation in your service

  @Column({ name: "skill_summary", type: "json", nullable: true })
  skillSummary: object;

  @Column({ name: "development_recommendations", type: "json", nullable: true })
  developmentRecommendations: object;

  @CreateDateColumn({
    name: "created_ts",
    type: "timestamp",
  })
  createdTs: Date;

  @UpdateDateColumn({
    name: "updated_ts",
    type: "timestamp",
  })
  updatedTs: Date;
}

export default ApplicantFinalSummariesModel;
