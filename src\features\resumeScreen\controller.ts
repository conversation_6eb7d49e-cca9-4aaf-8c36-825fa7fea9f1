import { Request, Response } from "express";
import { API_RESPONSE_MSG } from "../../utils/constants";
import ResumeScreenServices from "./services";
import { handleSentryError } from "../../utils/helper";

export const uploadManualCandidate = async (req: Request, res: Response) => {
  try {
    const data = req.body;

    const result = await ResumeScreenServices.manualCandidateUpload({
      ...data,
      organization_id: req.orgId,
      hiring_manager_id: req.userId,
    });

    return res.status(200).json({
      ...result,
    });
  } catch (error) {
    handleSentryError(error, "uploadManualCandidate");
    return res.status(500).json({
      success: false,
      message: API_RESPONSE_MSG.failed,
      error: error.message,
    });
  }
};

/**
 * Get all pending job applications with pagination support
 * @param req Request object with job_id, organization_id, hiring_manager_id, and optional limit, offset, status in query params
 * @param res Response object
 */
export const getAllPendingJobApplications = async (
  req: Request,
  res: Response
) => {
  try {
    const filters = {
      job_id: Number(req.query.job_id),
      organization_id: req.orgId,
      limit: Number(req.query.limit),
      offset: Number(req.query.offset),
      status: req.query.status as string,
    };
    console.log("filters==========>", filters);
    const result =
      await ResumeScreenServices.getAllPendingJobApplications(filters);

    return res.status(200).json({
      success: result.success,
      message: result.message || API_RESPONSE_MSG.success,
      data: result.applications,
      pagination: result.pagination,
    });
  } catch (error) {
    handleSentryError(error, "getAllPendingJobApplications");
    return res.status(500).json({
      success: false,
      message: API_RESPONSE_MSG.failed,
      error: error.message,
    });
  }
};

/**
 * Change the status of a job application
 * @param req Request object with job_id, candidate_id, hiring_manager_id, and status in query params and hiring_manager_reason in body
 * @param res Response object
 */
export const changeApplicationStatus = async (req: Request, res: Response) => {
  try {
    const data = {
      ...req.body,
      hiring_manager_id: req.userId,
    };

    const result = await ResumeScreenServices.changeApplicationStatus(data);

    return res.status(result.success ? 200 : 400).json({
      success: result.success,
      message: result.message || API_RESPONSE_MSG.success,
      data: result.application,
    });
  } catch (error) {
    handleSentryError(error, "changeApplicationStatus");
    return res.status(500).json({
      success: false,
      message: API_RESPONSE_MSG.failed,
      error: error.message,
    });
  }
};
