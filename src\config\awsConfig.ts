import { S3Client } from "@aws-sdk/client-s3";
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from "@aws-sdk/client-secrets-manager";
import { CloudFrontClient } from "@aws-sdk/client-cloudfront";
import { fromIni } from "@aws-sdk/credential-providers";

import { ISecretKeys } from "../interface/commonInterface";

import envConfig from "./envConfig";
import { ENV_VARIABLE } from "../utils/constants";
import { handleSentryError } from "../utils/helper";

const CONFIG = envConfig();

// Create client config with region
interface ClientConfig {
  region: string;
  credentials?: any;
}

export const clientConfig: ClientConfig = {
  region: CONFIG.region,
};

// Add credentials for local development
if (
  [ENV_VARIABLE.LOCAL, ENV_VARIABLE.DEVELOPMENT, ENV_VARIABLE.STAGING].includes(
    CONFIG.env
  )
) {
  clientConfig.credentials = fromIni({ profile: CONFIG.profile });
}

let secretKeys: ISecretKeys;
export const getSecretKeys = async (hardLoad = false): Promise<ISecretKeys> => {
  if (!secretKeys || hardLoad) {
    try {
      const client = new SecretsManagerClient({
        ...clientConfig,
        maxAttempts: 3,
      });
      const command = new GetSecretValueCommand({
        SecretId: CONFIG.secretManagerKey,
      });

      const response = await client.send(command);

      if (response.SecretString) {
        secretKeys = JSON.parse(response.SecretString);
        return secretKeys;
      }
    } catch (err) {
      handleSentryError(err, "getSecretKeys");
      console.log("Error fetching secret keys:", err);

      getSecretKeys(hardLoad);
      // throw err;
    }
  }
  return secretKeys;
};

// Create an S3 client
const s3Client = new S3Client(clientConfig);
const cloudFrontClient = new CloudFrontClient({ region: CONFIG.region });

export default {
  s3Client,
  cloudFrontClient,
};
