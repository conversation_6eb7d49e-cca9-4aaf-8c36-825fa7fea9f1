import { Request, Response } from "express";
import InterviewService from "./services";
import {
  IGetCandidate<PERSON>ist,
  IGetInterviewSkillQuestions,
  IGetJobList,
  IUpdateInterviewSkillQuestion,
  IEndInterview,
} from "./interface";
import { DEFAULT_LIMIT } from "../../utils/constants";
import { handleSentryError } from "../../utils/helper";

export const conductInterviewStaticInformation = async (
  req: Request,
  res: Response
) => {
  try {
    const data = await InterviewService.conductInterviewStaticInformation();
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "conductInterviewStaticInformation");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getJobList = async (req: Request, res: Response) => {
  try {
    const body = {
      orgId: +req.orgId,
      searchString: req.query.searchString,
    };

    const data = await InterviewService.getJobList(body as IGetJobList);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getJobList");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getCandidateList = async (req: Request, res: Response) => {
  try {
    const body = {
      orgId: +req.orgId,
      searchString: req.query.searchString,
      jobId: +req.query.jobId,
    };

    const data = await InterviewService.getCandidateList(
      body as IGetCandidateList
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getCandidateList");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get my interviews.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getMyInterviews = async (req: Request, res: Response) => {
  try {
    const body = {
      ...(req.query as unknown as {
        monthYear: string;
      }),
      userId: req.userId,
      roleId: req.roleId,
      orgId: +req.orgId,
    };

    const data = await InterviewService.getMyInterviews(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getMyInterviews");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get interviewers.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getInterviewers = async (req: Request, res: Response) => {
  try {
    const data = await InterviewService.getInterviewers({
      orgId: req.orgId,
      jobId: +req.query.jobId,
      searchString: req.query.searchString as string,
    });
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getInterviewers");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Schedule interview.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const updateOrScheduleInterview = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateOrScheduleInterview(
      body,
      req.userId,
      +req.orgId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateOrScheduleInterview");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get interview skill questions answers.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getInterviewSkillQuestions = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      jobApplicationId: +req.query.jobApplicationId,
      interviewId: +req.query.interviewId,
      orgId: +req.orgId,
    };

    const data = await InterviewService.getInterviewSkillQuestions(
      body as IGetInterviewSkillQuestions
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getInterviewSkillQuestions");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Update interview skill question.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const updateInterviewSkillQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateInterviewSkillQuestion(
      body as IUpdateInterviewSkillQuestion
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateInterviewSkillQuestion");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Add interview skill question.
 
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const addInterviewSkillQuestion = async (
  req: Request,
  res: Response
) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.addInterviewSkillQuestion(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "addInterviewSkillQuestion");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

// upcoming and past interviews

export const getUpcomingOrPastInterviews = async (
  req: Request,
  res: Response
) => {
  try {
    const { orgId, userId, roleId } = req;

    // 🔹 Parse and validate query parameters
    const isPast = req.query.isPast === "true";
    const limit = Number(req.query.limit) || DEFAULT_LIMIT; // Max limit of 100
    const offset = Number(req.query.page) || 0;
    const searchStr = String(req.query.searchStr || "");

    // 🔹 Call the service with updated arguments
    const data = await InterviewService.getUpcomingOrPastInterviews(
      orgId,
      userId,
      roleId,
      searchStr,
      isPast,
      offset,
      limit
    );

    res.status(200).json({ code: 200, ...data });
  } catch (error) {
    handleSentryError(error, "getUpcomingOrPastInterviews");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const updateInterviewAnswers = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.updateInterviewAnswers(
      body,
      req.userId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateInterviewAnswers");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * End interview and lock all evaluations.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const endInterview = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
    };

    const data = await InterviewService.endInterview(body as IEndInterview);

    console.log("data ===>>>", data);
    res.status(data.code || 200).json({
      ...data,
    });
  } catch (error) {
    handleSentryError(error, "endInterview");
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

export const getInterviewFeedback = async (req: Request, res: Response) => {
  try {
    const data = await InterviewService.getInterviewFeedback(
      +req.params.interviewId,
      +req.orgId
    );
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getInterviewFeedback");
    console.log("error controller ===>>>", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
export const updateInterviewFeedback = async (req: Request, res: Response) => {
  try {
    const body = {
      ...req.body,
      orgId: req.orgId,
      userId: req.userId,
    };

    const data = await InterviewService.updateInterviewFeedback(body);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "updateInterviewFeedback");
    console.log("error controller ===>>>", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};

/**
 * Get pending interviews that need feedback.
 *
 * @param req - The HTTP request object
 * @param res - The HTTP response object
 */
export const getPendingInterviews = async (req: Request, res: Response) => {
  try {
    const { userId, orgId } = req;
    const data = await InterviewService.getPendingInterviews(+userId, +orgId);
    res.status(200).json({
      ...data,
      code: 200,
    });
  } catch (error) {
    handleSentryError(error, "getPendingInterviews");
    console.log("getPendingInterviews error controller ===>>>", error);
    res.status(error.output?.statusCode ?? 500).json(error);
  }
};
