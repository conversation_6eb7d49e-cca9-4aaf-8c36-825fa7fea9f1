import express from "express";
import HandleErrors from "../../middleware/handleError";
import {
  verifyOtpValidation,
  forgotPasswordValidation,
  resetPasswordValidation,
  signInValidation,
  resendOtpValidation,
  updateTimeZoneValidation,
  deleteSessionValidation,
  checkUserOrgExistValidation,
  sendVerificationEmailValidation,
} from "./vaildation";
import {
  paramsValidation,
  schemaValidation,
} from "../../middleware/validateSchema";
import {
  login,
  forgotPassword,
  verifyOtp,
  resetPassword,
  resendOtp,
  deleteSession,
  updateTimeZone,
  checkUserOrgExist,
  sendVerificationEmail,
} from "./controller";
import { ROUTES } from "../../utils/constants";
import auth from "../../middleware/auth";

const authRoutes = express.Router();

/**
 * @swagger
 * /auth/resend-otp:
 *   post:
 *     summary: Resend OTP
 *     description: Resends OTP to the user's email for verification
 *     tags: [Auth Routes]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - type
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: User's email address
 *               type:
 *                 type: string
 *                 example: "signup"
 *                 description: Type of OTP (signup or forgot_password)
 *               name:
 *                 type: string
 *                 example: "test"
 *                 description: Optional user name
 *     responses:
 *       200:
 *         description: OTP sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "otp_sent_success"
 *                 data:
 *                   type: object
 *                   properties:
 *                     otp:
 *                       type: string
 *                       example: "8955"
 *                     generatedAt:
 *                       type: number
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_phone"
 *       404:
 *         description: User not found
 */
authRoutes.post(
  ROUTES.AUTH.RESEND_OTP,
  schemaValidation(resendOtpValidation),
  HandleErrors(resendOtp)
);

/**
 * @swagger
 * /auth/sign-in:
 *   post:
 *     summary: User Signin
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 example: password
 *
 *             required:
 *               - email
 *               - password
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: login_successful
 *                  data:
 *                    type: object
 *                    properties:
 *                      token:
 *                        type: string
 *                        example: GtwM8LfoigDSDgj21VZgBI1R0H4iOATWU7HHiezRZ4U=
 *                      userData:
 *                        type: object
 *                        properties:
 *                          id:
 *                            type: number
 *                            example: 21
 *                          account_type:
 *                            type: string
 *                            example: user
 *                          first_name:
 *                            type: string
 *                            example: Tom
 *                          last_name:
 *                            type: string
 *                            example: Cruise
 *                          gender:
 *                            type: string
 *                            example: male
 *                          email:
 *                            type: string
 *                            example: <EMAIL>
 *                          phone:
 *                            type: string
 *                            example: **********
 *                          password:
 *                            type: string
 *                            example: $2a$08$BAJAhD4zAfqrO4E5vzmhL.ysSf8Xek28JoDZXIln4W2qWbLC7fTzm
 *                          age:
 *                            type: number
 *                            example: 22
 *                          assessment_completed:
 *                            type: boolean
 *                            example: true
 *                          image:
 *                            type: string
 *                            example: GtwM8LfoigDSDgj21VZgBI1R0H4iOATWU7HHiezRZ4U=
 *                          dob:
 *                            type: string
 *                            example: null
 *                          created_ts:
 *                            type: string
 *                            example: 2023-07-10T06:55:59.000Z
 *                          updated_ts:
 *                            type: string
 *                            example: 2023-07-10T06:55:59.000Z
 *                          address:
 *                            type: object
 *                            properties:
 *                              id:
 *                                type: number
 *                                example: 77
 *                              user_id:
 *                                type: number
 *                                example: 209
 *                              occupation:
 *                                type: string
 *                                example: Engineer
 *                              contact_number:
 *                                type: string
 *                                example: 4442223355
 *                              state:
 *                                type: string
 *                                example: Rajasthan
 *                              city:
 *                                type: string
 *                                example: Jaipur
 *                              created_ts:
 *                                type: string
 *                                example: 2023-07-10T06:55:59.000Z
 *                              updated_ts:
 *                                type: string
 *                                example: 2023-07-10T06:55:59.000Z
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.SIGN_IN,
  schemaValidation(signInValidation),
  HandleErrors(login)
);

/**
 * @swagger
 * /auth/verify-otp:
 *   post:
 *     summary: Verify OTP
 *     description: Verifies OTP for signup or password reset
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               otp:
 *                 type: string
 *                 description: The OTP code sent to the user
 *                 example: "8593"
 *               email:
 *                 type: string
 *                 description: User's email address
 *                 example: "<EMAIL>"
 *               type:
 *                 type: string
 *                 description: Type of verification (signup or forgot_password)
 *                 example: "signup"
 *               firstName:
 *                 type: string
 *                 description: User's first name (required for signup)
 *                 example: "admin"
 *               lastName:
 *                 type: string
 *                 description: User's last name (required for signup)
 *                 example: "admin"
 *               password:
 *                 type: string
 *                 description: User's password (required for signup)
 *                 example: "Test@123"
 *               passwordType:
 *                 type: string
 *                 description: Type of password (new or existing)
 *                 example: "Admin@123"
 *               organizationName:
 *                 type: string
 *                 description: Name of the organization (required for signup)
 *                 example: "testorg"
 *               organizationCode:
 *                 type: string
 *                 description: Code of the organization (required for signup)
 *                 example: "5443534534"
 *               location:
 *                 type: string
 *                 description: Location of the user/organization (required for signup)
 *                 example: "Jaipur"
 *               websiteURL:
 *                 type: string
 *                 description: Website URL of the organization (optional)
 *                 example: "https://www.testorg.com"
 *               branchName:
 *                 type: string
 *                 description: Branch name (optional)
 *                 example: "Jaipur"
 *               branchCode:
 *                 type: string
 *                 description: Branch code (optional)
 *                 example: "5443534534"
 *               tinNumber:
 *                 type: string
 *                 description: Tax Identification Number (optional)
 *                 example: ""
 *             required:
 *               - otp
 *               - email
 *               - type
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: signup_successful
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.VERIFY_OTP,
  schemaValidation(verifyOtpValidation),
  HandleErrors(verifyOtp)
);

/**
 * @swagger
 * /auth/forgot-password:
 *   post:
 *     summary: Forgot Password
 *     description: Sends an OTP to the provided email address for password recovery
 *     tags:
 *       - Auth Routes
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: User's email address
 *             required:
 *               - email
 *     responses:
 *       200:
 *          description: Success
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: true
 *                  message:
 *                    type: string
 *                    example: success
 *                  data:
 *                    type: object
 *                    properties:
 *                      otp:
 *                        type: string
 *                        example: 8955
 *                      generatedAt:
 *                        type: number
 *
 *       401:
 *          description: Bad Request
 *          content:
 *            application/json:
 *              schema:
 *                type: object
 *                properties:
 *                  success:
 *                    type: boolean
 *                    example: false
 *                  message:
 *                    type: string
 *                    example: token_req
 *
 */
authRoutes.post(
  ROUTES.AUTH.FORGOT_PASSWORD,
  schemaValidation(forgotPasswordValidation),
  HandleErrors(forgotPassword)
);

/**
 * @swagger
 * /auth/reset-password:
 *   post:
 *     summary: Reset Password
 *     description: Resets user password using OTP verification
 *     tags: [Auth Routes]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - otp
 *               - password
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: Email address of the user
 *               otp:
 *                 type: string
 *                 example: "6128"
 *                 description: OTP received via email
 *               password:
 *                 type: string
 *                 example: "Test@123"
 *                 description: New password
 *     responses:
 *       200:
 *         description: Password reset successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "password_reset_successful"
 *       400:
 *         description: Bad request or invalid OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_otp"
 *       404:
 *         description: User not found
 */
authRoutes.post(
  ROUTES.AUTH.RESET_PASSWORD,
  schemaValidation(resetPasswordValidation),
  HandleErrors(resetPassword)
);

/**
 * @swagger
 * /auth/delete-session/{userId}:
 *   delete:
 *     summary: Delete User Session
 *     description: Deletes a user's active session
 *     tags: [Auth Routes]
 *     parameters:
 *       - in: path
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user whose session to delete
 *     responses:
 *       200:
 *         description: Session deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "session_deleted"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_user_id"
 *       404:
 *         description: User session not found
 */
authRoutes.delete(
  ROUTES.AUTH.DELETE_SESSION,
  paramsValidation(deleteSessionValidation),
  HandleErrors(deleteSession)
);

/**
 * @swagger
 * /auth/update-timezone:
 *   post:
 *     summary: Update User Timezone
 *     description: Updates the timezone setting for the authenticated user
 *     tags: [Auth Routes]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - timezone
 *             properties:
 *               timezone:
 *                 type: string
 *                 example: "Asia/Kolkata"
 *                 description: Timezone string in IANA format
 *     responses:
 *       200:
 *         description: Timezone updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "timezone_updated"
 *                 data:
 *                   type: object
 *                   properties:
 *                     timezone:
 *                       type: string
 *                       example: "Asia/Kolkata"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_timezone"
 *       401:
 *         description: Unauthorized
 */
authRoutes.post(
  ROUTES.AUTH.UPDATE_TIMEZONE,
  auth,
  schemaValidation(updateTimeZoneValidation),
  HandleErrors(updateTimeZone)
);

/**
 * @swagger
 * /auth/check-user-org-exist:
 *   post:
 *     summary: Check if User and Organization Exist
 *     description: Checks if a user and their organization exist in the system
 *     tags: [Auth Routes]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - organizationName
 *               - organizationCode
 *               - firstName
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: Email address to check
 *               organizationName:
 *                 type: string
 *                 example: "xyz"
 *                 description: Name of the organization
 *               organizationCode:
 *                 type: string
 *                 example: "1234567890"
 *                 description: Organization code or identifier
 *               firstName:
 *                 type: string
 *                 example: "test"
 *                 description: First name of the user
 *     responses:
 *       200:
 *         description: Check completed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "user_org_found"
 *                 data:
 *                   type: object
 *                   properties:
 *                     exists:
 *                       type: boolean
 *                       example: true
 *                     userId:
 *                       type: string
 *                       example: "123"
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_email"
 */
authRoutes.post(
  ROUTES.AUTH.CHECK_USER_ORG_EXIST,
  schemaValidation(checkUserOrgExistValidation),
  HandleErrors(checkUserOrgExist)
);

/**
 * @swagger
 * /auth/send-verification-email:
 *   post:
 *     summary: Send Verification Email
 *     description: Sends a verification email to the specified email address
 *     tags: [Auth Routes]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - email
 *               - type
 *             properties:
 *               email:
 *                 type: string
 *                 example: "<EMAIL>"
 *                 description: Email address to send verification to
 *               type:
 *                 type: string
 *                 example: "signup"
 *                 description: Type of verification (signup or forgot_password)
 *               redirectUrl:
 *                 type: string
 *                 example: "https://example.com/verify"
 *                 description: URL to redirect after verification
 *     responses:
 *       200:
 *         description: Verification email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "verification_email_sent"
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "invalid_email"
 *       404:
 *         description: User not found
 */
authRoutes.post(
  ROUTES.AUTH.SEND_VERIFICATION_EMAIL,
  schemaValidation(sendVerificationEmailValidation),
  HandleErrors(sendVerificationEmail)
);

// authRoutes.get(
//   ROUTES.AUTH.GET_PREDICTIONS,
//   HandleErrors(getPredictions)
// );

export default authRoutes;
