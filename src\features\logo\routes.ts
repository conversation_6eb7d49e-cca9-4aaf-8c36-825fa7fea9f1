import express from "express";
import { schemaValidation } from "../../middleware/validateSchema";
import { ROUTES } from "../../utils/constants";

import HandleErrors from "../../middleware/handleError";
import logoValidationSchema from "./validation";
import auth from "../../middleware/auth";
import updateLogo from "./controller";

const OrganizationLogoRoutes = express.Router();

/**
 * @route PUT /logo/update
 * @desc Update the company logo
 * @access Private
 * @swagger
 * /logo/update:
 *   put:
 *     summary: Update the company logo
 *     tags:
 *       - Logo
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LogoUpdate'
 *     responses:
 *       200:
 *         description: Logo updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/LogoResponse'
 *       400:
 *         description: Invalid input
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Server error
 */

// Update logo route

OrganizationLogoRoutes.put(
  ROUTES.LOGO.UPDATE_LOGO,
  auth,
  schemaValidation(logoValidationSchema),
  HandleErrors(updateLogo)
);

export default OrganizationLogoRoutes;
