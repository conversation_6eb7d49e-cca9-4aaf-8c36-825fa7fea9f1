import Jo<PERSON> from "joi";
import {
  LABELS,
  EMAIL_REGEX,
  OTP_TYPE,
  PASSWORD_REGEX,
  NAME_REGEX,
  ORGANIZATION_CODE_REGEX,
  ORGANIZATION_NAME_REGEX,
  WEBSITE_URL_REGEX,
  BRANCH_NAME_REGEX,
  BRANCH_CODE_REGEX,
  TIN_NUMBER_REGEX,
} from "../../utils/constants";

const joiObject = Joi.object().options({ abortEarly: false });

export const emailValidation = joiObject.keys({
  email: Joi.string().regex(EMAIL_REGEX).label(LABELS.email).required(),
});

export const passwordValidation = joiObject.keys({
  password: Joi.string().label(LABELS.password).required(),
});

export const otpValidation = joiObject.keys({
  otp: Joi.string().required(),
});

export const verifyOtpValidationSchema = joiObject
  .keys({
    type: Joi.string()
      .valid(OTP_TYPE.FORGOT_PASSWORD, OTP_TYPE.SIGNUP)
      .required(),
    passwordType: Joi.string().optional().allow(null, ""),
  })
  .concat(otpValidation)
  .concat(emailValidation);

export const signUpValidationSchema = joiObject
  .concat(verifyOtpValidationSchema)
  .keys({
    firstName: Joi.string()
      .trim()
      .required()
      .min(3)
      .max(50)
      .pattern(NAME_REGEX),
    lastName: Joi.string().trim().required().min(3).max(50).pattern(NAME_REGEX),
    password: Joi.string().required().pattern(PASSWORD_REGEX),
    organizationCode: Joi.string()
      .trim()
      .required()
      .pattern(ORGANIZATION_CODE_REGEX)
      .min(6)
      .max(10),
    location: Joi.string().trim().required().min(3).max(50),
    organizationName: Joi.string()
      .trim()
      .required()
      .pattern(ORGANIZATION_NAME_REGEX)
      .min(3)
      .max(150),
    passwordType: Joi.string().required(),
    websiteURL: Joi.string()
      .trim()
      .allow(null, "")
      .pattern(WEBSITE_URL_REGEX)
      .min(3)
      .max(255),
    branchName: Joi.string()
      .trim()
      .allow(null, "")
      .pattern(BRANCH_NAME_REGEX)
      .min(3)
      .max(100),
    branchCode: Joi.string()
      .trim()
      .allow(null, "")
      .pattern(BRANCH_CODE_REGEX)
      .min(3)
      .max(6),
    tinNumber: Joi.string()
      .trim()
      .allow(null, "")
      .pattern(TIN_NUMBER_REGEX)
      .min(9)
      .max(9),
  });

export const verifyOtpValidation = joiObject.when(".type", {
  is: OTP_TYPE.SIGNUP,
  then: signUpValidationSchema,
  otherwise: verifyOtpValidationSchema,
});

export const forgotPasswordValidation = joiObject.concat(emailValidation);
export const resendOtpValidation = joiObject
  .keys({
    type: Joi.string()
      .valid(OTP_TYPE.FORGOT_PASSWORD, OTP_TYPE.SIGNUP)
      .required(),
    name: Joi.string().optional().allow(null, ""),
  })
  .concat(emailValidation);

export const sendVerificationEmailValidation =
  joiObject.concat(resendOtpValidation);

export const resetPasswordValidation = joiObject
  .concat(passwordValidation)
  .concat(emailValidation)
  .concat(otpValidation);

export const signInValidation = joiObject
  .concat(emailValidation)
  .concat(passwordValidation)
  .keys({
    fcmToken: Joi.string().optional(),
    timezone: Joi.string().optional(),
  });

export const updateTimeZoneValidation = joiObject.keys({
  timezone: Joi.string().required(),
});

export const deleteSessionValidation = joiObject.keys({
  userId: Joi.number().required(),
});

export const checkUserOrgExistValidation = joiObject
  .keys({
    organizationName: Joi.string().required(),
    organizationCode: Joi.string().required(),
    firstName: Joi.string().required(),
  })
  .concat(emailValidation);
