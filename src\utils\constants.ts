export const EMAIL_REGEX = /^[a-zA-Z0-9+_.-]+@[a-zA-Z0-9.-]+$/;

export const DEFAULT_VALUE = 0;

export const PASSWORD_REGEX =
  /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,16}$/;

export const PHONE_REGEX =
  /^((\\+[1-9]{1,4}[ \\-]*)|(\\([0-9]{2,3}\\)[ \\-]*)|([0-9]{2,4})[ \\-]*)*?[0-9]{3,4}?[ \\-]*[0-9]{3,4}?$/;

export const IMAGE_REGEX =
  /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)?$/;

export const LIMIT = "5mb";
export const ASSESSMENT_ID_REGEX = /assessment_(\d+)_\d+/;

export const ENV_VARIABLE = {
  LOCAL: "local",
  DEVELOPMENT: "development",
  STAGING: "staging",
};

export const STRATUM9_LOGO_URL =
  "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/Innerview+Logo.png";

export const DB_CONST = {
  TYPE: "mysql",
  CHARSET: "utf8mb4_unicode_ci",
  S9_NAME: "s9_db",
  S9_INNERVIEW_NAME: "s9iv_db",
};

export const BASE_ROUTES = {
  ACCESS_MANAGEMENT: "/api/v1/access-management",
  EMPLOYEE_MANAGEMENT: "/api/v1/employee-management",
  AUTH: "/api/v1/auth",
  INTERVIEW: "/api/v1/interview",
  JOB_REQUIREMENT: "/api/v1/jobs",
  FINAL_ASSESSMENT: "/api/v1/final-assessment",
  RESUME_SCREEN: "/api/v1/resume-screen",
  CANDIDATES: "/api/v1/candidates",
  AGORA_RECORDING: "/api/v1/agora-recordings",
  AGORA_WEBHOOK: "/api/v1/agora",
  NOTIFICATIONS: "/api/v1/notifications",
  USER_PROFILE: "/api/v1/user-profile",
  COMMON: "/api/v1",
  SUBSCRIPTION: "/api/v1/subscription",
  STRIPE: "/api/v1/stripe",
  WEBHOOKS: "/api/v1/webhooks",
  JOB_APPLY: "/api/v1/job-apply",
  ACTIVITY_LOGS: "/api/v1/activity-logs",
  USER_WALKTHROUGH: "/api/v1/user-walkthrough",
  ORGANIZATION_LOGO: "/api/v1/logo",
};

export const SOCKET_ROUTES = {
  CONDUCT_INTERVIEW: "/conduct-interview",
  CANDIDATE_CONDUCT_INTERVIEW: "/candidate-conduct-interview",
};

export const AGORA_USER_TYPE = {
  Interviewer: "interviewer",
  Candidate: "candidate",
};

export const AGORA_API_STATUS = {
  UPLOADED: "uploaded",
};

export const ROUTES = {
  // Access Management Routes
  ADD_ROLE_DEPARTMENT_AND_PERMISSION_FOR_ADMIN:
    "/add-role-department-and-permission-for-admin",
  GET_USER_ROLES: "/user-roles",
  GET_USER_ROLES_PAGINATION: "/user-roles-pagination",
  ADD_USER_ROLE: "/add-user-role",

  // Subscription Routes
  SUBSCRIPTION: {
    CURRENT: "/current",
    ALL_PLANS: "/all",
    CANCEL: "/cancel",
    TRANSACTIONS: "/transactions",
    BUY_SUBSCRIPTION: "/buy-subscription",
    JOB_POSTING_QUOTA: "/job-posting-quota",
  },

  // Webhook Routes
  WEBHOOKS: {
    STRIPE: "/stripe",
  },
  USER_PERMISSIONS: "/user-permissions",
  JOBS: {
    GENERATE_SKILLS: "/generate-skills",
    UPLOAD_URL: "/upload-url",
    PARSE_PDF: "/parse-pdf",
    PARSE_ADVANCED_PDF: "/parse-advanced-pdf",
    EXTRACT_PDF_IMAGES: "/extract-pdf-images",
    GET_PDF_METADATA: "/get-pdf-metadata",
    GET_ALL_SKILLS: "/get-all-skills",
    GENERATE_JOB_REQUIREMENT: "/generate-job-requirement",
    SAVE_JOB_DETAILS: "/save-job-details",
    GET_JOBS_META: "/get-jobs-meta",
    UPDATE_JOB: "/updateJob/:id",
    DASHBOARD_COUNTS: "/dashboard-counts",
    GET_JOB_HTML_DESCRIPTION: "/get-job-html-description",
    UPDATE_JOB_DESCRIPTION: "/update-job-description",
    GENERATE_PDF: "/generate-pdf",
  },
  CANDIDATES: {
    GET_ALL_HIRED_CANDIDATE: "/get-all-hired-candidates",
    ADD_APPLICANT_ADDITIONAL_INFO: "/add-applicant-additional-info",
    GET_CANDIDATE_DETAILS: "/get-candidate-details",
    GET_TOP_CANDIDATES: "/top-candidates",
    PROMOTE_DEMOTE_CANDIDATE: "/update-candidate-rank-status",
    GET_CANDIDATES: "/get-candidates",
    ARCHIVE_ACTIVE_APPLICATION: "/archive-active-application/:applicationId",
    UPDATE_JOB_APPLICATION_STATUS:
      "/update-job-application-status/:jobApplicationId",
    GET_CANDIDATE_INTERVIEW_HISTORY:
      "/get-candidate-interview-history/:jobApplicationId",
    APPLICATION_FINAL_SUMMARY: "/application-final-summary/:jobApplicationId",
    APPLICATION_SKILL_SCORE_DATA:
      "/application-skill-score-data/:jobApplicationId",
    GENERATE_FINAL_SUMMARY: "/generate-final-summary",
  },
  RESUME_SCREEN: {
    MANUAL_CANDIDATE_UPLOAD: "/manual-candidate-upload",
    GET_ALL_PENDING_JOB_APPLICATIONS: "/get-all-pending-job-applications",
    CHANGE_APPLICATION_STATUS: "/change-application-status",
    GET_PRESIGNED_URL: "/get-presigned-url",
  },

  INTERVIEW: {
    UPDATE_OR_SCHEDULE_INTERVIEW: "/update-or-schedule-interview",
    GET_INTERVIEWERS: "/get-interviewers",
    GET_MY_INTERVIEWS: "/get-my-interviews",
    GET_UPCOMING_OR_PAST_INTERVIEWS: "/get-upcoming-or-past-interviews",
    GET_INTERVIEW_SKILL_QUESTIONS: "/get-interview-skill-questions",
    UPDATE_INTERVIEW_SKILL_QUESTION: "/update-interview-skill-question",
    ADD_INTERVIEW_SKILL_QUESTION: "/add-interview-skill-question",
    UPDATE_INTERVIEW_ANSWERS: "/update-interview-answers",
    GET_JOB_LIST: "/get-job-list",
    GET_CANDIDATE_LIST: "/get-candidate-list",
    END_INTERVIEW: "/end-interview",
    CONDUCT_INTERVIEW_STATIC_INFORMATION:
      "/conduct-interview-static-information",
    GET_INTERVIEW_FEEDBACK: "/get-interview-feedback/:interviewId",
    UPDATE_INTERVIEW_FEEDBACK: "/update-interview-feedback",
    GET_PENDING_INTERVIEWS: "/get-pending-interviews",
  },
  NOTIFICATIONS: {
    DELETE_USERS_ALL_NOTIFICATIONS: "/delete-users-all-notifications",
    GET_NOTIFICATIONS: "/get-notifications",
    MARK_AS_WATCHED: "/mark-as-watched",
    GET_UNREAD_NOTIFICATIONS_COUNT: "/get-unread-notifications-count",
  },

  USER_ROLE: "/user-role",
  USER_ROLE_WITH_ID: "/user-role/:roleId",
  ROLE_PERMISSIONS: "/role-permissions",
  ROLE_PERMISSIONS_WITH_ID: "/role-permissions/:roleId",

  // Auth Routes
  AUTH: {
    SIGN_IN: "/sign-in",
    FORGOT_PASSWORD: "/forgot-password",
    RESET_PASSWORD: "/reset-password",
    DELETE_SESSION: "/delete-session/:userId",
    USER_EXISTS: "/user-exists",
    VERIFY_OTP: "/verify-otp",
    RESEND_OTP: "/resend-otp",
    UPDATE_TIMEZONE: "/update-timezone",
    CHECK_USER_ORG_EXIST: "/check-user-org-exist",
    SEND_VERIFICATION_EMAIL: "/send-verification-email",
    // GET_PREDICTIONS: "/get-predictions/:searchText",
  },

  AGORA: {
    START_MEETING_RECORDING: "/meetings/start",
    STOP_MEETING_RECORDING: "/meetings/stop",
    CREATE_TOKEN: "/create-token",
  },

  COMMON: {
    REMOVE_ATTACHMENTS_FROM_S3: "/remove-attachments-from-s3",
    GENERATE_PRE_SIGNED_URL: "/generate-presignedurl",
  },

  // Employee Management Routes
  DEPARTMENTS: "/departments",
  ADD_DEPARTMENT: "/add-department",
  DEPARTMENT_WITH_ID: "/update-department/:departmentId",
  DELETE_DEPARTMENT: "/delete-department/:departmentId",
  DEPARTMENTS_BY_ORGANIZATION: "/departments/organization/:organizationId",
  UPDATE_EMPLOYEE_ROLE: "/employee/:employeeId/role",
  EMPLOYEES: "/employees",
  ADD_EMPLOYEES: "/add-hiring-employee",
  EMPLOYEE_WITH_ID: "/employee/:employeeId",
  EMPLOYEE_ROLE: "/employee/:employeeId/role",
  DELETE_EMPLOYEE: "/employee/:employeeId",
  UPDATE_EMPLOYEE_INTERVIEW_ORDER: "/employee/:employeeId/interview-order",
  UPDATE_EMPLOYEE_STATUS: "/employee/change-status/:employeeId",

  FINAL_ASSESSMENT: {
    CREATE_FINAL_ASSESSMENT: "/create-final-assessment",
    GENERATE_QUESTIONS: "/generate-questions",
    ADD_MANUAL_QUESTION: "/assessment/create-question",
    GET_FINAL_ASSESSMENT_QUESTION: "/assessment/questions",
    SHARE_ASSESSMENT: "/assessment/share",
    GET_FINAL_ASSESSMENT_BY_CANDIDATE: "/candidate/assessment",
    SUBMIT_ASSESSMENT: "/candidate/assessment/submit",
    GET_ASSESSMENT_STATUS: "/assessment-status",
    VERIFY_CANDIDATE_EMAIL: "/candidate/verify-email",
    GENERATE_ASSESSMENT_TOKEN: "/assessment/generate-token",
  },
  // Hire Employee Routes
  ADD_HIRING_EMPLOYEE: "/add-hiring-employee",

  USER_PROFILE: {
    GET_MY_PROFILE: "/get-my-profile",
    UPDATE_MY_PROFILE: "/update-my-profile",
  },
  JOB_APPLY: {
    GET_APPLY_JOB_PORTAL_SCRIPT: "/get-apply-job-portal-script",
    LOAD_IFRAME: "/load-iframe",
    GET_IFRAME_CONTENT: "/get-iframe-content",
    GET_ORGANIZATION_JOBS_LIST: "/get-organization-jobs-list",
    APPLY_JOB: "/apply-job",
    SEND_OTP_TO_JOB_APPLIED_CANDIDATE: "/otp",
    VERIFY_JOB_APPLIED_CANDIDATE_OTP: "/verify-otp",
  },
  ACTIVITY_LOGS: {
    GET_LOGS: "/get-logs",
  },
  LOGO: {
    UPDATE_LOGO: "/update-logo",
  },
};

// update as per your use
export const LABELS = {
  city: "City",
  location: "Location",
  country: "Country",
  country_code: "Country Code",
  sms_notification: "Sms Notification",
  state: "State",
  phone: "Phone",
  email: "Email",
  age: "Age",
  gender: "Gender",
  lastName: "Last Name",
  password: "Password",
  firstName: "First Name",
  occupation: "Occupation",
  company: "Company",
  organizationCode: "Organization Code",
  image: "Image",
  jobId: "Job ID",
  orgId: "Org ID",
  candidateName: "Candidate Name",
  resume: "Resume",
  otp: "OTP",
};

export const PLATFORM = {
  STRATUM9_INNERVIEW: "stratum9_innerview",
};

export const INTERVIEW_EMAIL_TYPE = {
  SCHEDULE: "schedule",
  UPDATE: "update",
  CANCEL: "cancel",
  INTERVIEWER_CHANGE: "interviewer_change",
  CANDIDATE_CHANGE: "candidate_change",
};

// update as per your use
export const API_RESPONSE_MSG = {
  pdf_parsing_failed: "Error in parsing PDF file",
  interview_already_ended: "interview_already_ended",
  invalid_data: "invalid_data",
  signup_successful: "signup_successful",
  unauthorized: "unauthorized",
  skill_added_successfully: "skill_added_successfully",
  otp_sending_failed: "otp_sending_failed",
  can_not_add_new_skill_in_career_based_skill:
    "can_not_add_new_skill_in_career_based_skill",
  organization_code_exist: "organization_code_already_exist",
  org_name_exist: "organization_name_already_exist",
  otp_expired: "otp_has_expired_please_request_a_new_otp",
  interviews_fetched: "interviews_fetched",
  feedback_already_submitted: "feedback_already_submitted",
  cannot_update_feedback_before_ending_interview:
    "cannot_update_feedback_before_ending_interview",
  interviewers_fetched: "interviewers_fetched",
  invalid_email: "invalid_email",
  interview_feedback_is_not_filled_by_interviewer_yet:
    "interview_feedback_is_not_filled_by_interviewer_yet",
  cannot_schedule_interview_once_final_assessment_is_generated:
    "cannot_schedule_interview_once_final_assessment_is_generated",
  interview_is_not_allowed_for_next_round:
    "interview_is_not_allowed_for_next_round",
  interview_feedback_not_found: "interview_feedback_not_found",
  interview_question_added: "interview_question_added",
  interview_is_ended: "interview_is_ended",
  job_application_is_not_approved: "job_application_is_not_approved",

  cannot_update_interview_after_ended: "cannot_update_interview_after_ended",
  interview_question_updated: "interview_question_updated",
  question_not_found: "question_not_found",
  presigned_url_generated: "presigned_url_generated",
  interview_updated_successfully: "interview_updated_successfully",
  previously_scheduled_interview_is_not_ended:
    "previously_scheduled_interview_is_not_ended",
  cannot_add_interview_skill_question_after_ended:
    "cannot_add_interview_skill_question_after_ended",
  interview_ended: "interview_ended",

  email_exist: "email_exist",
  unsupported_email: "unsupported_email",
  interview_already_scheduled: "interview_already_scheduled",
  interview_scheduled_successfully: "interview_scheduled_successfully",

  cannot_schedule_interview_past: "cannot_schedule_interview_past",
  interview_must_be_at_least_10_min: "interview_must_be_at_least_10_min",
  end_time_must_be_after_start_time: "end_time_must_be_after_start_time",
  interview_must_not_exceed_2_hours: "interview_must_not_exceed_2_hours",
  cannot_schedule_more_than_one_month_in_advance:
    "cannot_schedule_more_than_one_month_in_advance",
  interview_not_found: "interview_not_found",
  max_career_based_questions_reached: "max_career_based_questions_reached",
  max_role_specific_questions_reached: "max_role_specific_questions_reached",
  max_culture_specific_questions_reached:
    "max_culture_specific_questions_reached",

  success: "success",
  user_already_associated_with_other_organization:
    "user_already_associated_with_other_organization",
  verification_code_sent: "verification_code_sent",
  otp_verified: "otp_verified_successfully",

  submitted: "submitted_successfully",

  password_not_updated: "password_not_updated",
  user_not_found: "user_not_found",
  password_updated: "password_updated",

  password_update_failed: "password_update_failed",
  profile_updated: "profile_updated",
  user_not_exist: "user_not_exist",
  forgot_user: "email_entered_is_not_associated_with_any_registered_account",
  failed: "something_went_wrong",
  user_session_deleted: "user_session_deleted",

  meeting_already_in_progress: "meeting_already_in_progress",

  wrong_password: "wrong_password",
  wrong_current_pass: "wrong_current_pass",
  login_successful: "login_successful",
  email_not_found: "email_not_found",
  email_not_verified: "email_not_verified",
  login_failed: "login_failed",

  try_again: "something_wrong_try_again",
  reset_password: "reset_password_successfully",
  reset_password_error: "reset_password_error",

  wrong_otp: "please_enter_a_valid_verification_code",
  fetch_success: "fetch_success",
  skills_generated: "skills_generated",
  fetch_failed: "fetch_failed",
  skills_generation_failed: "skills_generation_failed",
  job_requirement_generated: "job_requirement_generated",
  job_requirement_generation_failed: "job_requirement_generation_failed",
  job_details_saved: "job_details_saved",
  job_details_save_failed: "job_details_save_failed",

  job_application_not_found: "job_application_not_found",
  invalidParams: "invalid_params",
  job_fetch_success: "job_fetch_success",
  job_update_success: "job_update_success",
  pdf_generated_success: "pdf_generated_success",
  pdf_generation_failed: "pdf_generation_failed",
  dashboard_counts_fetch_success: "dashboard_counts_fetch_success",
  dashboard_counts_fetch_failed: "dashboard_counts_fetch_failed",
  no_pdf_file_uploaded: "no_pdf_file_uploaded",
  internal_server_error: "internal_server_error",
  job_description_processed_successfully:
    "job_description_processed_successfully",
  failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf:
    "failed_to_process_the_pdf_file_ensure_it_is_a_valid_pdf",
  failed_to_extract_form_fields: "failed_to_extract_form_fields",
  job_description_processed_failed: "job_description_processed_failed",
  job_not_found: "job_not_found",
  failed_to_update_job: "Failed to update job",
  jobs_meta_fetch_success: "Job metadata fetched successfully",
  failed_to_fetch_jobs_meta: "Failed to fetch job metadata",
  open_ai_key_not_configured: "OpenAI API key not configured",
  failed_to_initialize_openai_client: "Failed to initialize OpenAI client",
  open_ai_client_not_initilezed: "OpenAI client not initialized",
  email_already_exists: "email_already_exists",
  recording_started: "Recording started successfully",
  recording_stopped: "Recording stopped successfully",
  failed_to_stop_recording: "Failed to stop recording",
  Agora_token_created_successfully: "Agora token created successfully",
  candidate_advanced_successfully:
    "Candidate advanced to next round successfully",
  candidate_rejected_successfully: "Candidate rejected successfully",
  failed_to_create_Agora_token: "Failed to create Agora token",
  candidate_email_not_found: "candidate_email_not_found",
  interview_doesnt_exist: "interview_doesnt_exist",
  token_generated_successfully: "token_generated_successfully",
  failed_to_start_recording: "Failed to start recording",
  already_applied_for_this_job: "already_applied_for_this_job",
  no_jobs_found: "no_jobs_found",
  application_submitted_successfully: "application_submitted_successfully",
  failed_to_submit_job_application: "failed_to_submit_job_application",
  something_went_wrong: "something_went_wrong",
  organization_not_found: "organization_not_found",
  script_tag_generated_successfully: "script_tag_generated_successfully",
  job_description_lacks_sufficient_content:
    "job_description_lacks_sufficient_content",
  job_restored_successfully: "job_restored_successfully",
  job_archive_successfully: "job_archive_successfully",
  logo_updated_successfully: "logo_updated_successfully",
  no_data_found: "no_data_found",
};

// subscription messages
export const SUBSCRIPTION_MSG = {
  invalid_plan: "invalid_or_inactive_plan_id",
  invalid_pricing: "invalid_pricing_id_for_the_given_plan",
  stripe_customer_not_found: "no_stripe_customer_found_for_the_organization",
  stripe_customer_deleted: "stripe_customer_not_found_or_has_been_deleted",
  stripe_customer_error: "error_retrieving_stripe_customer",
  no_active_subscription: "no_active_subscription_found_for_this_organization",
  subscription_plan_not_found: "subscription_plan_or_pricing_details_not_found",
  subscription_retrieved: "current_subscription_retrieved_successfully",
  subscription_retrieve_error: "error_retrieving_current_subscription",
  subscription_canceled:
    "subscription_will_be_expired_at_the_end_of_the_current_billing_cycle",
  subscription_cancel_error: "error_cancelling_subscription",
  pricing_not_found: "pricing_not_found",
  checkout_session_created: "checkout_session_created_successfully",
  checkout_session_error: "error_creating_checkout_session",
  customer_exists: "customer_already_exists",
  customer_created: "stripe_customer_created_successfully",
  customer_creation_error: "error_creating_stripe_customer",
  plans_retrieved: "all_available_plans_retrieved_successfully",
  plans_retrieve_error: "error_retrieving_subscription_plans",
  webhook_processed: "webhook_processed_successfully",
  webhook_error: "error_processing_webhook",
  organization_not_found: "organization_not_found",
  plan_same:
    "you_have_already_selected_this_plan_please_choose_a_different_plan_to_proceed",

  // General subscription messages (specific limit messages are now generated dynamically)
  using_free_plan_benefit: "using_free_plan_benefit_quota",
  benefit_quota_available: "benefit_quota_available",
  invalid_benefit_type: "invalid_benefit_type",
  subscription_benefit_check_error: "error_checking_subscription_benefit",
  job_posting_limit_reached: "job_posting_limit_reached",
  job_posting_quota_retrieved_successfully:
    "job_posting_quota_retrieved_successfully",
  no_subscription_benefits_found: "no_subscription_benefits_found",
  failed_to_retrieve_job_posting_quota: "failed_to_retrieve_job_posting_quota",
  resume_screening_limit_reached: "resume_screening_limit_reached",
  manual_resume_upload_limit_reached:
    "Only {quota_value} candidate resume upload allowed. Please upgrade for more.",
  no_subscription_plan_found_and_no_free_plan_available:
    "no_subscription_plan_found_and_no_free_plan_available",
  no_subscription_found_and_error_retrieving_free_plan:
    "no_subscription_found_and_error_retrieving_free_plan",
  stripeSubscriptionId_not_found: "stripeSubscriptionId_not_found",
};

// Job posting quota status constants
export const JOB_POSTING_QUOTA_STATUS = {
  UNLIMITED: "unlimited",
  EXHAUSTED: "exhausted",
  AVAILABLE: "available",
};

// Add custom API response messages
export const ACCESS_MANAGEMENT_MSG = {
  user_roles_fetch: "user_roles_fetch",
  user_role_added: "user_role_added",
  user_role_updated: "user_role_updated",
  user_role_deleted: "user_role_deleted",
  role_already_exists: "role_already_exists",
  role_not_found: "role_not_found",
  role_permissions_fetch: "role_permissions_fetch",
  role_permissions_updated: "role_permissions_updated",
  at_least_one_permission_required: "at_least_one_permission_required",
  add_failed: "add_failed",
  update_failed: "update_failed",
  delete_failed: "delete_failed",
  role_has_employees: "role_has_employees",
  default_role_update_not_allowed: "default_role_update_not_allowed",
  no_update_in_permissions: "no_update_in_permissions.",
};

export const FINAL_ASSESSMENT_MSG = {
  final_assessment_created: "final_assessment_created",
  final_assessment_questions_created: "final_assessment_questions_created",
  final_assessment_generated_failed: "final_assessment_generated_failed",
  final_assessment_processing_started: "final_assessment_processing_started",
  job_not_found: "job_not_found",
  job_application_not_found: "job_application_not_found",
  assessment_already_exists: "assessment_already_exists",
  assessment_status_fetched: "assessment_status_fetched",
  assessment_already_shared: "assessment_already_shared",
  assessment_already_submitted: "assessment_already_submitted",
  assessment_created: "assessment_created",
  assessment_not_found: "assessment_not_found",
  no_questions_found: "no_questions_found",
  questions_fetched: "questions_fetched",
  skill_not_found: "skill_not_found",
  candidate_not_found: "candidate_not_found",
  failed_to_send_assessment_email: "failed_to_send_assessment_email",
  assessment_shared_successfully: "assessment_shared_successfully",
  invalid_assessment_link: "invalid_assessment_link",
  invalid_question_ids: "invalid_question_ids",
  assessment_submitted_successfully: "assessment_submitted_successfully",
  email_mismatch: "email_mismatch",
  email_verified_successfully: "email_verified_successfully",
  unauthorized_access: "unauthorized_access",
  not_authenticated: "not_authenticated",
  assessment_has_expired: "assessment_has_expired",
};

export const EMPLOYEE_MANAGEMENT_MSG = {
  departments_fetch: "departments_fetch",
  department_added: "department_added",
  department_deleted: "department_deleted",
  department_updated: "department_updated",
  department_already_exists: "department_already_exists",
  department_not_found: "department_not_found",
  department_has_employees: "Cannot delete department with active employees",
  departments_by_organization_fetch: "departments_by_organization_fetch",
  employees_fetch: "employees_fetch",
  employees_added: "employees_added",
  employee_role_updated: "employee_role_updated",
  employee_deleted: "employee_deleted",
  employee_not_found: "employee_not_found",
  email_already_exists: "email_already_exists",
  add_failed: "add_failed",
  update_failed: "update_failed",
  delete_failed: "delete_failed",
  employee_interview_order_updated: "employee_interview_order_updated",
  employee_status_updated: "employee_status_updated",
  department_id_required: "department_id_required",
  name_required: "name_required",
  organization_id_required: "organization_id_required",
  no_employees_data: "no_employees_data",
  cannot_add_employee_user_registered_with_another_org:
    "cannot_add_employee_user_registered_with_another_org",
  employee_already_registered: "employee_already_registered",
  employee_already_registered_with_diff_org:
    "employee_already_registered_with_diff_org",
  employee_added: "employee_added",
  no_update_data_provided: "no_update_data_provided",
  default_department_cannot_be_deleted: "default_department_cannot_be_deleted",
  same_as_current_order: "same_as_current_order",
  invalid_sort_order: "invalid_sort_order",
  error_updating_interview_order: "error_updating_interview_order",
  user_roles_fetch: "user_roles_fetch",
  user_role_added: "user_role_added",
  user_role_updated: "user_role_updated",
  user_role_deleted: "user_role_deleted",
  role_already_exists: "role_already_exists",
  role_permissions_fetch: "role_permissions_fetch",
  role_permissions_updated: "role_permissions_updated",
  at_least_one_permission_required: "at_least_one_permission_required",
  role_has_employees: "role_has_employees",
  cannot_update_role: "cannot_update_role",
  cannot_update_default_department: "cannot_update_default_department",
  user_profile_fetched: "user_profile_fetched",
  you_are_not_authorized_to_update_this_user_profile:
    "you_are_not_authorized_to_update_this_user_profile",
  failed_to_update_user_profile: "failed_to_update_user_profile",
  user_profile_updated: "user_profile_updated",
  please_select_valid_role: "please_select_valid_role",
  please_select_valid_department: "please_select_valid_department",
  cannot_delete_employee_with_active_jobs:
    "cannot_delete_employee_with_active_jobs",
  cannot_delete_employee_with_active_job_applications:
    "cannot_delete_employee_with_active_job_applications",
  cannot_delete_employee_with_upcoming_interviews:
    "cannot_delete_employee_with_upcoming_interviews",
  cannot_delete_employee_with_job_application_status_changes:
    "cannot_delete_employee_with_job_application_status_changes",
  role_already_assigned: "role_already_assigned",
};

// Authorization related messages
export const AUTH_MSG = {
  permission_not_available: "You don't have permission to access this feature.",
  authorization_error: "Internal server error during authorization check",
  unauthorized_role: "Unauthorized access - Role not found",
};

export const INTERVIEW_QUESTIONS_DIFFICULTY_LEVELS = {
  BEGINNER: "beginner",
  INTERMEDIATE: "intermediate",
  ADVANCED: "advanced",
};

export const MESSAGE_TYPE = {
  SENT: "Sent",
};
export const PASSWORD_HASH_LENGTH = 8;

export const USER_TYPE = {
  user: "user",
  admin: "admin",
  new: "new",
};
export const REQUIRED_TYPES = [
  "Mentality",
  "Cognitive Abilities",
  "Mastery of Emotions",
  "Social Interaction",
  "Personal Health",
];

export const DEFAULT_OFFSET = 0;
export const DEFAULT_LIMIT = 15;
export const DEFAULT_SORT = "DESC";

export const MAX_FINAL_ASSESSMENT_QUESTIONS_PER_SKILL = 2;
/**
 * Redis expiry durations in seconds
 */
export const REDIS_EXPIRY = {
  // 60 seconds * 60 minutes * 24 hours * 30 days
  DEFAULT: 60 * 60 * 24 * 30, // 2,592,000 seconds (30 days)

  // 60 seconds * 60 minutes * 24 hours * 7 days
  PERMISSIONS_UPDATE_STATUS: 60 * 60 * 24 * 7, // 604,800 seconds (7 days)
  APPLY_JOB_CANDIDATE_OTP_EXPIRY_TIME: 60 * 5,
};

export const ONE_TO_ONE_INTERVIEW_INSTRUCTIONS = [
  {
    title: "Be Punctual",
    content:
      "Arrive at the interview location on time and ensure everything is set up before the candidate arrives.",
  },
  {
    title: "Guide the Interview Flow",
    content:
      "Lead the interview process by asking clear, concise questions and giving the candidate time to respond.",
  },
  {
    title: "Review Candidate Materials",
    content:
      "Familiarize yourself with the candidate's resume and any supporting documents before the interview begins.",
  },
  {
    title: "Minimize Distractions",
    content:
      "Set your phone to silent mode and create a distraction-free environment for the interview.",
  },
  {
    title: "Maintain Professionalism",
    content:
      "Dress professionally and maintain positive body language to create a welcoming atmosphere.",
  },
  {
    title: "Listen Actively",
    content:
      "Pay close attention to the candidate's responses and ask follow-up questions if necessary for clarification.",
  },
  {
    title: "Be Respectful",
    content:
      "Allow the candidate to speak without interruptions and ensure a respectful, balanced conversation.",
  },
  {
    title: "Take Notes",
    content:
      "Jot down key points or important details during the interview, but stay focused on the candidate's responses.",
  },
  {
    title: "Provide Support",
    content:
      "If there are any issues or the candidate needs assistance, be sure to offer guidance or notify the relevant coordinator.",
  },
];

export const VIDEO_CALL_INTERVIEW_INSTRUCTIONS = [
  {
    title: "Join the Interview on Time",
    content: "Ensure you join the interview on time using the link provided.",
  },
  {
    title: "Take Candidate Consent",
    content:
      "Before starting the interview, clearly inform the candidate that the session will be recorded. Ask for their verbal consent to proceed with recording, and continue only if the candidate agrees.",
  },
  {
    title: "Ensure a Stable Internet Connection",
    content:
      "Make sure your internet connection is reliable, and ensure you're in a quiet, well-lit space.",
  },
  {
    title: "Test Your Camera, Microphone, and Audio Settings",
    content:
      "Test your equipment in advance to avoid any technical issues during the interview.",
  },
  {
    title: "Keep Your Video On",
    content: "Keep your video on throughout the online interview.",
  },
  {
    title: "Minimize Background Noise and Avoid Multitasking",
    content:
      "Eliminate distractions and focus solely on the interview to create a professional atmosphere.",
  },
  {
    title: "Use Headphones for Better Audio Clarity",
    content:
      "Using headphones helps avoid audio disturbances and ensures clear communication.",
  },
  {
    title: "Be Attentive and Maintain Professional Posture",
    content:
      "Respond clearly, be engaged, and maintain professional posture throughout the interview.",
  },
  {
    title: "Contact Support for Technical Difficulties",
    content:
      "If you experience technical difficulties before or during the interview, contact support immediately.",
  },
];

export const STRATUM_POINT_DESCRIPTION = {
  1: "I am not aware of this skill or how it affects my performance.",
  2: "I am aware of the skill but unconcerned about its impact on my performance.",
  3: "I realize how being extreme or deficient in this skill affects my performance.",
  4: "I’m searching for resources to help me improve this skill.",
  5: "I am actively learning how to improve this skill, but not yet applying.",
  6: "I started applying what I learned, but I am basic, unbalanced, and messy.",
  7: "I am practicing what I learned to improve this skill, but I am inconsistent.",
  8: "I am competent, consistent, and confident in this skill.",
  9: "I am a performance leader in this skill. I can teach others my processes.",
  10: "Excessive use of a skill that disrupts performance, relationships, or clarity, becoming counterproductive over time.",
};

// update as per your use
export const REDIS_KEYS = {
  ROLE_PERMISSIONS: "innerview-role-{roleId}-permissions",
  ROLE_PERMISSIONS_UPDATE_STATUS:
    "innerview-role-{roleId}-permission_update_status",
  USER_SESSIONS: "innerview-user-{userId}-sessions",
  INTERVIEW_TRANSCRIPT_KEY: "interview:transcript:",
  CONDUCT_INTERVIEW_INFORMATION: "interview:conduct:information",
  INTERVIEW_CANDIDATE_KEY: "{interviewId}:candidate:{candidateId}",
  INTERVIEW_INTERVIEWER_KEY: "{interviewId}:interviewer:{interviewerId}",
  MAX_FOLLOW_UP_SKILL_GENERATION_LIMIT:
    "max_follow_up_skill_generation_limit:{interviewId}",
  USER_OTP_KEY: "{email}-otp",
  FINAL_ASSESSMENT_GENERATION_STATUS:
    "final_assessment_generation_status:{jobApplicationId}",
};
export const DEFAULT_COUNTRY_CODE = "1";
export const DEFAULT_COUNTRY = "US";

export const DEFAULT_ORG_USERS_COUNT = 1;

export const INTERVIEW_QUESTION_TYPE = {
  FOLLOW_UP: "follow_up",
};

export const DEFAULT_ORG_FIELDS = {
  DEPARTMENT: "Administrator",
  ROLE: "Admin",
};
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-vars */
// eslint-disable-next-line import/export
export enum QuestionType {
  MCQ = "mcq",
  TRUE_FALSE = "true_false",
}

export const ACTIVITY_LOG_INTERVIEW_FEEDBACK = {
  POSITIVE: "Positive Feedback",
  NEGATIVE: "Negative Feedback",
};

export const GPT_MODEL = "gpt-4.1";
export const GPT_MODEL_TURBO = "gpt-3.5-turbo";
export const SKILLS_CACHE_KEY = "skills_data";

/**
 * Permission slugs for authorization
 */
export const PERMISSION = {
  CREATE_OR_EDIT_JOB_POST: "create-or-edit-job-post",
  SCHEDULE_CONDUCT_INTERVIEWS: "schedule-conduct-interviews",
  VIEW_HIRED_CANDIDATES: "view-hired-candidates",
  ARCHIVE_RESTORE_CANDIDATES: "archive-restore-candidates",
  ARCHIVE_RESTORE_JOB_POSTS: "archive-restore-job-posts",
  MANUAL_RESUME_SCREENING: "manual-resume-screening",
  ADD_ADDITIONAL_CANDIDATE_INFO: "add-additional-candidate-info",
  MANAGE_TOP_CANDIDATES: "manage-top-candidates", // manage promote and demote candidates
  MANAGE_PRE_INTERVIEW_QUESTIONS: "manage-pre-interview-questions",
  MANAGE_CANDIDATE_PROFILE: "manage-candidate-profile",
  HIRE_CANDIDATE: "hire-candidate",
  CREATE_NEW_ROLE: "create-new-role",
  MANAGE_USER_PERMISSIONS: "manage-user-permissions",
  CREATE_NEW_DEPARTMENT: "create-new-department",
  VIEW_SUBSCRIPTION_PLAN: "view-subscription-plan",
  MANAGE_SUBSCRIPTIONS: "manage-subscriptions",
  VIEW_AUDIT_LOGS_UPCOMING: "view-audit-logs-upcoming",
  VIEW_ALL_SCHEDULED_INTERVIEWS: "view-all-scheduled-interviews",
  ADD_EMPLOYEE: "add-employee",
};

export const CANDIDATE_APPLICATION_MSG = {
  can_promote_or_demote_only_after_resume_screening:
    "can_promote_or_demote_only_after_resume_screening",
  already_updated: "already_updated",
  interview_not_ended: "interview_not_ended",
  get_hired_candidate_success: "get_hired_candidate_success",
  get_hired_candidate_failed: "get_hired_candidate_failed",
  final_assessment_not_exists: "final_assessment_not_exists",
  candidates_fetched: "candidates_fetched",
  get_all_candidates_failed: "get_all_candidates_failed",
  job_application_not_found: "job_application_not_found",
  update_application_status_success: "updated_application_status_success",
  update_application_status_failed: "update_application_status_failed",
  top_candidates_retrieved: "top_candidates_retrieved",
  get_top_candidates_failed: "get_top_candidates_failed",
  candidate_application_not_found: "candidate_application_not_found",
  update_rank_status_failed: "update_rank_status_failed",
  update_rank_status_success: "update_rank_status_success",
  candidate_not_found: "candidate_not_found",
  fetch_candidate_details_failed: "fetch_candidate_details_failed",
  candidate_not_found_for_org: "candidate_not_found_for_org",
  additional_info_saved: "additional_info_saved",
  save_additional_info_failed: "save_additional_info_failed",
  interview_history_retrieved: "interview_history_retrieved",
  get_interview_history_failed: "get_interview_history_failed",
  skill_specific_assessment_retrieved: "skill_specific_assessment_retrieved",
  application_final_summary_retrieved: "application_final_summary_retrieved",
  application_final_summary_failed: "application_final_summary_failed",
  get_skill_specific_assessment_failed: "get_skill_specific_assessment_failed",
  unknown_error: "Unknown error occurred",
  no_interviews_found: "no_interviews_found",
  final_summary_generated_successfully: "final_summary_generated_successfully",
  generate_final_summary_failed: "generate_final_summary_failed",
  no_skill_score_data_found: "no_skill_score_data_found",
  no_interview_found_to_update_status: "no_interview_found_to_update_status",
  interviews_not_completed: "interviews_not_completed",
  interview_feedback_pending: "interview_feedback_pending",
  minimum_one_interview_round_required: "minimum_one_interview_round_required",
  final_summary_already_generated: "final_summary_already_generated",
};

export const PDF_CONTENT_TYPE = "application/pdf";

export const ATTEMPT = 0;
export const PDF_PARSING_MAX_ATTEMPTS = 5;
export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

export const AGORA_WEBHOOK_EVENT_TYPE = {
  Active: "active",
  Completed: "completed",
};
export const CANDIDATE_AVATAR_URL = {
  "1": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-1.png",
  "2": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-2.png",
  "3": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-3.png",
  "4": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-4.png",
  "5": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-5.png",
  "6": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-6.png",
  "7": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-7.png",
  "8": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-8.png",
  "9": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-9.png",
  "10": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-10.png",
  "11": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-11.png",
  "12": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-12.png",
  "13": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-13.png",
  "14": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-14.png",
  "15": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-15.png",
  "16": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-16.png",
  "17": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/female-17.png",
  "18": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-1.png",
  "19": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-2.png",
  "20": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-3.png",
  "21": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-4.png",
  "22": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-5.png",
  "23": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-6.png",
  "24": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-7.png",
  "25": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-8.png",
  "26": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-9.png",
  "27": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-10.png",
  "28": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-11.png",
  "29": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-12.png",
  "30": "https://s9-interview-assets-prod.s3.us-east-1.amazonaws.com/candidate-avatars/male-13.png",
};

export const SCHEDULE_INTERVIEW_ONE_MONTH_MS = 30 * 24 * 60 * 60 * 1000; // 30 days in milliseconds
export const SCHEDULE_INTERVIEW_MINUTES_MS = 10 * 60 * 1000; // 10 minutes in milliseconds
export const MAX_HOURS_BETWEEN_START_AND_END_TIME = 2 * 60 * 60 * 1000; // 2 hours in milliseconds

export const BENEFIT_SLUGS = {
  JOB_POSTINGS: "job_postings",
  RESUME_SCREENING: "resume_screening",
  MANUAL_RESUME_UPLOAD: "manual_resume_upload",
};

export const DEFAULT_USER_COLLECTIONS = [
  "My Books",
  "My Power Outfits",
  "My Meals",
  "My Workouts",
];

export const ACTIVITY_LOG_TYPE = {
  LOGIN: "Login",
  ADD_EMPLOYEE: "Add Employee",
  CHANGE_ACCESS_ROLE: "Change Access Role",
  UPDATE_PERMISSIONS: "Update Permissions",
  JOB_POSTING: "Job Posting",
  SUBSCRIPTION_UPDATE: "Subscription Update",
  SCHEDULE_INTERVIEW: "Schedule Interview",
  INTERVIEW_FEEDBACK: "Interview Feedback",
  HIRE_REJECT_CANDIDATE: "Hire/Reject Candidate",
  HIRE_CANDIDATE: "Hire Candidate",
  REJECT_CANDIDATE: "Reject Candidate",
  CANDIDATE_FINAL_SUMMARY: "Candidate Final Summary",
};

export const STATUS = {
  ACTIVE: 1,
  INACTIVE: 0,
};

export const ENTITY_TYPE = {
  USER: "User",
  EMPLOYEE: "Employee",
  DEPARTMENT: "Department",
  ROLE: "Role Permissions Mapping",
  ORGANIZATION_SUBSCRIPTION: "OrganizationSubscription",
  JOB: "Job",
  CANDIDATE: "Candidate",
  INTERVIEW: "interview",
  JOB_APPLICATION: "Job Application",
  JOB_APPLICATION_STATUS_HISTORY: "Job Application Status History",
  INTERVIEW_FEEDBACK_MODEL: "Interview Feedback Model",
};

export const ACTIVITY_LOGS_VALUES = "Interview Scheduled";

export const OTP_TYPE = {
  SIGNUP: "signup",
  FORGOT_PASSWORD: "forgot_password",
};
export const GENDER_TYPES = ["male", "female", "other"];

export const IS_ACTIVE = true;

export const JOB_APPLY_EMAIL_CONTENTS = {
  SUBJECT: "Apply job verification code",
  TEXT_CONTENT: "Apply job verification code",
  MESSAGE: "Apply job Verification Request",
};

export const ALL_JOB_META_LIMIT = 10;

export const INTERVIEW_ROUND_TYPE = {
  IN_PERSON: "In-Person",
  ONLINE_INTERVIEW: "Online Interview",
};

export const S9_INNERVIEW_ROUTES = {
  LOGIN: "login",
};

export const NAME_REGEX = /^[a-zA-Z\s]{3,50}$/;
export const WEBSITE_URL_REGEX = /^(?!-)(?:[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?\.)+[a-z]{2,63}$/;
export const TIN_NUMBER_REGEX = /^\d{9}$/;
export const BRANCH_CODE_REGEX = /^[a-zA-Z0-9]{3,6}$/;
export const BRANCH_NAME_REGEX = /^[a-zA-Z0-9\s\-_/]{3,100}$/;
export const ORGANIZATION_NAME_REGEX = /^[a-zA-Z0-9\s\-_/]{3,150}$/;
export const ORGANIZATION_CODE_REGEX = /^[a-zA-Z0-9]{6,10}$/;
