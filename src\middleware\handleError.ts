import { Request, Response, NextFunction } from "express";
import { handleSentryError } from "../utils/helper";

const HandleErrors =
  (func) =>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  async (req: Request, res: Response, next: NextFunction): Promise<any> => {
    try {
      // eslint-disable-next-line no-console
      console.log("Inside HandleErrors", req.originalUrl);
      await func(req, res, next);
    } catch (error) {
      // eslint-disable-next-line no-console
      handleSentryError(error, "HandleErrors");
      res.status(400).send(error);
      next(error);
    }
  };

export default HandleErrors;
