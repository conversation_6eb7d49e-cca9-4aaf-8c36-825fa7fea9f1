export interface UserProfileResponse {
  success: boolean;
  message: string;
  data?: UserProfileData;
  code: number;
}

export interface DeleteImageFromS3Request {
  id: number;
  fileName: string;
  userId: number;
}

export interface UserProfileData {
  id: number;
  image: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  organizationName: string;
  organizationCode: string;
  departmentName: string;
  createdTs: Date;
}

export interface UpdateUserProfileRequest {
  firstName: string;
  lastName: string;
  image: string | null;
  userId: number;
}

export interface UpdateUserProfileResponse {
  success: boolean;
  message: string;
  data?: UserProfileData;
  code: number;
}
