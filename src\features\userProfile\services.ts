import { UserProfileData, UpdateUserProfileRequest } from "./interfaces";
import UserModel from "../../schema/s9/user";
import DbConnection from "../../db/dbConnection";
import { deleteFileFromS3 } from "../../utils/fileUpload";
import { ResponseObject } from "../../interface/commonInterface";
import { getSecretKeys } from "../../config/awsConfig";
import {
  API_RESPONSE_MSG,
  EMPLOYEE_MANAGEMENT_MSG,
} from "../../utils/constants";
import { handleSentryError } from "../../utils/helper";
import OrganizationModel from "../../schema/s9/organization";

class UserProfileServices {
  static response: ResponseObject;

  /**
   * Get user profile information
   * @param userId - The ID of the user
   * @returns User profile data
   */
  static getMyProfile = async (userId: number, orgId: number) => {
    try {
      // Get S9 data source for user and organization data
      const s9DataSource = await DbConnection.getS9DataSource();
      const organization = await s9DataSource
        .getRepository(OrganizationModel)
        .findOne({
          where: { id: orgId },
        });

      console.log("organization----->>>", organization.logo);

      // Get user data with a single query
      const userData = await s9DataSource
        .createQueryBuilder()
        .select([
          "user.image as image",
          "user.first_name as firstName",
          "user.last_name as lastName",
        ])
        .from(UserModel, "user")
        .where("user.id = :userId", { userId })
        .getRawOne();

      console.log("userData----->>>", userData);

      if (!userData) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }
      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.user_profile_fetched,
        data: {
          ...userData,
          logo: organization?.logo ?? null,
        },
      };
    } catch (error) {
      handleSentryError(error, "getMyProfile");
      return {
        success: false,
        message: API_RESPONSE_MSG.fetch_failed,
      };
    }
  };

  /**
   * Update user profile information
   * @param payload - The update profile payload containing id, firstName, lastName, and image
   * @returns Updated user profile data
   */
  static updateMyProfile = async (
    payload: UpdateUserProfileRequest
  ): Promise<{ success: boolean; message: string; data?: UserProfileData }> => {
    try {
      const { userId, firstName, lastName, image } = payload;

      const userRepo = await DbConnection.getS9DatabaseRepository(UserModel);

      // Find the user by ID
      const user = await userRepo.findOne({
        where: { id: userId },
      });

      if (!user) {
        return {
          success: false,
          message: API_RESPONSE_MSG.user_not_found,
        };
      }
      if (image && image !== null) {
        const keys = await getSecretKeys();
        const imageKey = user.image.replace(
          keys.s3_bucket_cloudfront_distribution_url_for_s9_innerview,
          ""
        );
        if (imageKey) {
          await deleteFileFromS3(imageKey);
        }
        user.image = image;
      }

      // Update user data
      user.first_name = firstName;
      user.last_name = lastName;

      // Save updated user
      await userRepo.save(user);

      return {
        success: true,
        message: EMPLOYEE_MANAGEMENT_MSG.user_profile_updated,
      };
    } catch (error) {
      handleSentryError(error, "updateMyProfile");
      return {
        success: false,
        message: EMPLOYEE_MANAGEMENT_MSG.failed_to_update_user_profile,
      };
    }
  };
}

export default UserProfileServices;
