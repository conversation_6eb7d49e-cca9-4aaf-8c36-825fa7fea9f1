import { Request, Response, NextFunction } from "express";
import SubscriptionServices from "../features/subscriptions/services";
import { getSecretKeys } from "../config/awsConfig";
import { handleSentryError } from "../utils/helper";

/**
 * Middleware to validate incoming Stripe webhook requests using signature verification
 * Uses the webhook secret from AWS Secrets Manager
 */
const validateStripeWebhook = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    // Get the signature from Stripe headers
    const sig = req.headers["stripe-signature"];

    if (!sig) {
      console.error("Missing Stripe signature header");
      return res.status(400).json({
        success: false,
        message: "Missing Stripe signature header",
        code: 400,
      });
    }

    // Initialize Stripe
    const stripe = await SubscriptionServices.initializeStripe();

    // Get webhook secret from AWS Secrets Manager
    const keys = await getSecretKeys();

    // Check if webhook secret exists
    if (!keys.stripe_webhook_secret) {
      console.error("Stripe webhook secret is missing in AWS Secrets Manager");
      return res.status(500).json({
        success: false,
        message: "Server configuration error",
        code: 500,
      });
    }

    // Verify the webhook signature
    const event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      keys.stripe_webhook_secret
    );

    // Log successful validation
    console.log(`Webhook validated successfully. Event type: ${event.type}`);

    // Attach the verified event to the request for downstream handlers
    // req.stripeEvent = event;

    // Call next middleware
    return next(); // Ensure that you are consistently returning next()
  } catch (err) {
    handleSentryError(err, "validateStripeWebhook");
    return res.status(400).json({
      success: false,
      message: `Webhook Error: ${err.message}`,
      code: 400,
    });
  }
};

export default validateStripeWebhook;
