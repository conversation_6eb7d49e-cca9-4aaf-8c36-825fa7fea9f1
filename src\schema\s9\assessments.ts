import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column } from "typeorm";

export interface IAssessmentSchema {
  id: number;
  title: string;
  value_points: string;
  icon: string;
  type: string;
  short_description: string;
  performance_category_mapping_id: number;
  description: string;
  highlighted_description: string;
  created_ts: Date;
  updated_ts: Date;
}

@Entity("assessment")
class AssessmentModel implements IAssessmentSchema {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    length: 255,
  })
  title: string;

  @Column({
    length: 3000,
  })
  short_description: string;

  @Column({
    length: 3000,
  })
  highlighted_description: string;

  @Column({
    length: 4000,
  })
  description: string;

  @Column({
    length: 255,
  })
  value_points: string;

  @Column({
    length: 255,
  })
  type: string;

  @Column({ type: "text" })
  icon: string;

  @Column()
  performance_category_mapping_id: number;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  created_ts: Date;

  @Column({ type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  updated_ts: Date;
}

export default AssessmentModel;
