// src/features/candidatesManagement/interface.ts

export interface Candidate {
  id: number;
  orgId: number;
  name: string;
  email: string;
  gender: "Male" | "Female";
  createdTs: Date;
  updatedTs: Date;
}

export interface CandidateInterviewHistoryItem {
  roundNumber: number;
  interviewerId: number;
  hardSkillMarks: number;
  skillScores: Record<string, number>; // JSON object with skill names as keys and marks as values
}

export interface CandidateInterviewHistoryResponse {
  success: boolean;
  message: string;
  data?: CandidateInterviewHistoryItem[];
  code?: number;
}

export interface SkillSpecificAssessmentItem {
  skillMarks: number;
  skillName: string;
}

export interface SkillSpecificAssessmentResponse {
  success: boolean;
  message: string;
  data?: SkillSpecificAssessmentItem[];
  code?: number;
}

// Skill evaluation data interfaces
// Type definitions for better type safety
export interface SkillEvaluationRaw {
  interviewer_id: number;
  hard_skill_marks: number;
  skills: string;
}

interface SkillDetail {
  skill_name: string;
  skill_marks: number;
  probability_of_success_in_this_skill: object | null;
  strengths: object | null;
  potentials_gaps: object | null;
}

export interface GroupedSkillScore {
  interviewerId: number;
  hardSkillMarks: number;
  skills: SkillDetail[];
}

export interface FlattenedSkillScore {
  skill_name: string;
  skill_marks: number;
  strengths: object | null;
  potentials_gaps: object | null;
  probability_of_success_in_this_skill: object | null;
}
